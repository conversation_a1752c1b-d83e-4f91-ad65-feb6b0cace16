# =============================================================================
# ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and fill in your actual values
# This template shows all required environment variables for the Sunrise B2B App
# =============================================================================

# -----------------------------------------------------------------------------
# SHOPIFY CONFIGURATION
# -----------------------------------------------------------------------------
# Shopify Storefront API Configuration
EXPO_PUBLIC_STOREFRONT_ACCESS_TOKEN=your_storefront_access_token_here
EXPO_PUBLIC_STOREFRONT_API_URL=https://your-store.myshopify.com/api/2025-01/graphql.json

# Shopify Admin API Configuration  
EXPO_PUBLIC_ADMIN_ACCESS_TOKEN=your_admin_access_token_here
EXPO_PUBLIC_ADMIN_API_URL=https://your-store.myshopify.com/admin/api/2025-01/graphql.json

# Shopify Store Configuration
EXPO_PUBLIC_SHOPIFY_STORE_URL=https://your-store.myshopify.com
EXPO_PUBLIC_SHOPIFY_SHOP_ID=your_shop_id_here

# Shopify OAuth Configuration
SHOPIFY_CLIENT_ID=your_shopify_client_id_here
SHOPIFY_CLIENT_SECRET=your_shopify_client_secret_here
SHOPIFY_REDIRECT_URI=your.app.scheme://auth/callback

# -----------------------------------------------------------------------------
# FIREBASE CONFIGURATION
# -----------------------------------------------------------------------------
# Firebase Project Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PROJECT_NUMBER=your_project_number_here
FIREBASE_STORAGE_BUCKET=your-project-id.firebasestorage.app

# Firebase Android Configuration
FIREBASE_ANDROID_API_KEY=your_android_api_key_here
FIREBASE_ANDROID_APP_ID=your_android_app_id_here
FIREBASE_ANDROID_CLIENT_ID=your_android_client_id_here

# Firebase iOS Configuration  
FIREBASE_IOS_API_KEY=your_ios_api_key_here
FIREBASE_IOS_APP_ID=your_ios_app_id_here
FIREBASE_IOS_CLIENT_ID=your_ios_client_id_here
FIREBASE_IOS_REVERSED_CLIENT_ID=your_reversed_client_id_here

# Firebase Service Account (for backend only - never expose to client)
FIREBASE_SERVICE_ACCOUNT_PATH=./config/firebase-service-account.json

# -----------------------------------------------------------------------------
# API ENDPOINTS
# -----------------------------------------------------------------------------
# Company Registration API
EXPO_PUBLIC_COMPANY_REGISTRATION_API_URL=https://your-api-gateway.execute-api.region.amazonaws.com/api/frontend/customer

# -----------------------------------------------------------------------------
# DEVELOPMENT CONFIGURATION
# -----------------------------------------------------------------------------
# Development Server URLs (for local development only)
DEV_SERVEO_URL=https://your-subdomain.serveo.net
DEV_NGROK_URL=https://your-ngrok-url.ngrok-free.app
DEV_LOCAL_IP=*************
DEV_PORT=3000

# Production Backend URL
PROD_BACKEND_URL=https://your-production-backend.com

# -----------------------------------------------------------------------------
# ANDROID BUILD CONFIGURATION
# -----------------------------------------------------------------------------
# Android Keystore Configuration (for release builds)
ANDROID_RELEASE_STORE_FILE=release.keystore
ANDROID_RELEASE_STORE_PASSWORD=your_secure_store_password_here
ANDROID_RELEASE_KEY_ALIAS=androidreleasekey  
ANDROID_RELEASE_KEY_PASSWORD=your_secure_key_password_here

# Android Debug Keystore (default values - can be left as is)
ANDROID_DEBUG_STORE_FILE=debug.keystore
ANDROID_DEBUG_STORE_PASSWORD=android
ANDROID_DEBUG_KEY_ALIAS=androiddebugkey
ANDROID_DEBUG_KEY_PASSWORD=android

# -----------------------------------------------------------------------------
# APP CONFIGURATION
# -----------------------------------------------------------------------------
# App Information
APP_NAME=YourAppName
APP_VERSION=1.0.0
APP_BUNDLE_ID=com.yourcompany.yourapp
APP_SCHEME=yourappscheme

# EAS Configuration
EAS_PROJECT_ID=your-eas-project-id-here
