{"name": "Sunrise", "main": "index.ts", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "postinstall": "node ./scripts/apply-patches.js && npx patch-package", "fix-android": "npx patch-package", "setup-security": "node ./scripts/setup-security.js", "generate-firebase-config": "node ./scripts/generate-firebase-config.js", "generate-android-config": "node ./scripts/generate-android-config.js", "cleanup-sensitive-files": "node ./scripts/cleanup-sensitive-files.js", "verify-security": "node ./scripts/verify-security.js", "prebuild": "npm run generate-firebase-config", "prestart": "npm run generate-firebase-config"}, "jest": {"preset": "jest-expo"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-inappbrowser-reborn", "react-native-chart-kit", "react-native-responsive-screen", "expo-random", "react-native-material-ripple", "react-native-swiper", "@apollo/client", "@expo/metro-runtime", "graphql", "graphql-request", "lodash", "react-native-base64", "react-native-otp-textinput"]}}}, "dependencies": {"@apollo/client": "^3.13.8", "@expo/metro-runtime": "^4.0.1", "@expo/vector-icons": "~14.0.4", "@gorhom/bottom-sheet": "^5.1.2", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-cookies/cookies": "^6.2.1", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/crashlytics": "^22.2.1", "@react-native-firebase/messaging": "^22.2.0", "@react-native-picker/picker": "2.9.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.2.10", "@reduxjs/toolkit": "^2.7.0", "expo": "~52.0.46", "expo-auth-session": "~6.0.3", "expo-blur": "~14.0.3", "expo-constants": "~17.0.8", "expo-crypto": "~14.0.2", "expo-document-picker": "~13.0.3", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-modules-core": "~2.2.3", "expo-random": "^14.0.1", "expo-router": "~4.0.20", "expo-secure-store": "~14.0.1", "expo-splash-screen": "^0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "^0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "graphql": "^16.10.0", "graphql-request": "^7.1.2", "libphonenumber-js": "^1.12.9", "lodash": "^4.17.21", "react": "^18.3.1", "react-dom": "^18.3.1", "react-native": "^0.76.9", "react-native-base64": "^0.2.1", "react-native-chart-kit": "^6.12.0", "react-native-collapsible": "^1.6.2", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.20.2", "react-native-material-ripple": "^0.9.1", "react-native-notifications": "^5.1.0", "react-native-otp-textinput": "^1.1.6", "react-native-paper": "^5.13.3", "react-native-phone-number-input": "^2.1.0", "react-native-ratings": "^8.1.0", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "~3.16.1", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-select-dropdown": "^4.0.1", "react-native-step-indicator": "^1.0.3", "react-native-svg": "15.8.0", "react-native-swiper": "^1.6.0", "react-native-web": "^0.19.13", "react-native-webview": "13.12.5", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "latest", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "dotenv": "^16.6.1", "expo-module-scripts": "^4.1.7", "expo-modules-autolinking": "~2.0.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}