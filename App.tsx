import "react-native-gesture-handler";
import React, { useEffect } from "react";
import Routes from "./app/Navigations/Route";
import { Provider } from "react-redux";
import store from "./app/redux/store";
import { useFonts } from "expo-font";
import { ApolloProvider } from "@apollo/client";
import client from "./app/api/appoloClient";
import { Text, View, Platform, TextInput } from "react-native";
import NotificationService from "./app/services/NotificationService";
import CrashlyticsService from "./app/services/CrashlyticsService";
import { SnackbarProvider } from "./app/context/SnackbarContext";
import { SafeAreaProvider } from "react-native-safe-area-context";
import AsyncStorage from "@react-native-async-storage/async-storage";
(Text as any).defaultProps = {
  ...(Text as any).defaultProps,
  allowFontScaling: false,
};

// 👇 Safely disable font scaling for all TextInput components
(TextInput as any).defaultProps = {
  ...(TextInput as any).defaultProps,
  allowFontScaling: false,
};
const App = () => {

  // Initialize notification service and Crashlytics
  //  console.log("before sending the notification");
  // useEffect(() => {
  //   const checkAndRegisterFCM = async () => {
  //     const customerData = await AsyncStorage.getItem("customerData");
  //     console.log("before sending the notification");
  //     if (customerData) {
  //       const parsed = JSON.parse(customerData);
  //       const shopifyCustomerId = parsed?.data?.customer?.id;
  //       console.log("before sending the notification");
  //       if (shopifyCustomerId) {
  //         console.log("before sending the notification");
  //         await NotificationService.registerFCMToken(shopifyCustomerId);
  //         console.log("after sending the notification");
  //         // Only set up token refresh listener once per session
  //         NotificationService.autoRegisterFCMToken(shopifyCustomerId);
  //       }
  //     }
  //   };
  //   checkAndRegisterFCM();
  // }, []);

  useEffect(() => {
    const initApp = async () => {
      try {
        console.log("🚀 Starting app initialization...");

        // Initialize Crashlytics first (should be early in app lifecycle)
        const crashlyticsInitialized = await CrashlyticsService.initialize();
        if (crashlyticsInitialized) {
          console.log("✅ Crashlytics initialized successfully");
          // Log app startup
          CrashlyticsService.log("App started successfully");
        } else {
          console.warn("⚠️ Crashlytics initialization failed");
        }

        // Only initialize once - onAppInstall already calls initialize internally
        await NotificationService.onAppInstall();
        console.log("✅ App initialization completed successfully");
      } catch (error) {
        console.error("❌ Failed to initialize app:", error);

        // Record the initialization error in Crashlytics
        CrashlyticsService.recordError(
          error as Error,
          "App initialization failed"
        );

        // Fallback to basic notification initialization only if onAppInstall failed
        try {
          console.log("🔄 Attempting fallback initialization...");
          await NotificationService.initialize();
          console.log("✅ Fallback notification service initialized");
        } catch (fallbackError) {
          console.error(
            "❌ Fallback initialization also failed:",
            fallbackError
          );
          CrashlyticsService.recordError(
            fallbackError as Error,
            "Fallback initialization failed"
          );
        }
      }
    };

    initApp();

    // Cleanup function to prevent memory leaks
    return () => {
      console.log("🧹 App cleanup - removing notification handlers");
      NotificationService.cleanup();
    };
  }, []);

  useFonts({
    JostRegular: require("./app/assets/fonts/Jost-Regular.ttf"),
    JostSemiBold: require("./app/assets/fonts/Jost-SemiBold.ttf"),
    JostBold: require("./app/assets/fonts/Jost-Bold.ttf"),
    JostMedium: require("./app/assets/fonts/Jost-Medium.ttf"),
    JostLight: require("./app/assets/fonts/Jost-Light.ttf"),
    PoppinsBlack: require("./app/assets/fonts/Poppins-Black.ttf"),
    PoppinsBlackItalic: require("./app/assets/fonts/Poppins-BlackItalic.ttf"),
    PoppinsBold: require("./app/assets/fonts/Poppins-Bold.ttf"),
    PoppinsBoldItalic: require("./app/assets/fonts/Poppins-BoldItalic.ttf"),
    PopppinsExtraBold: require("./app/assets/fonts/Poppins-ExtraBold.ttf"),
    PopppinsExtraBoldItalic: require("./app/assets/fonts/Poppins-ExtraBoldItalic.ttf"),
    PoppinsExtraLight: require("./app/assets/fonts/Poppins-ExtraLight.ttf"),
    PoppinsExtraLightItalic: require("./app/assets/fonts/Poppins-ExtraLightItalic.ttf"),
    PoppinsItalic: require("./app/assets/fonts/Poppins-Italic.ttf"),
    PoppinsLight: require("./app/assets/fonts/Poppins-Light.ttf"),
    PoppinsLightItalic: require("./app/assets/fonts/Poppins-LightItalic.ttf"),
    PoppinsMedium: require("./app/assets/fonts/Poppins-Medium.ttf"),
    PoppinsMediumItalic: require("./app/assets/fonts/Poppins-MediumItalic.ttf"),
    PoppinsRegular: require("./app/assets/fonts/Poppins-Regular.ttf"),
    PoppinsSemiBold: require("./app/assets/fonts/Poppins-SemiBold.ttf"),
    PoppinsSemiBoldItalic: require("./app/assets/fonts/Poppins-SemiBoldItalic.ttf"),
    PoppinsThin: require("./app/assets/fonts/Poppins-Thin.ttf"),
    PoppinsThinItalic: require("./app/assets/fonts/Poppins-ThinItalic.ttf"),
    DMSansSemiBold: require("./app/assets/fonts/DMSans-SemiBold.ttf"),
    DMSansMedium: require("./app/assets/fonts/DMSans-Medium.ttf"),
    DMSansRegular: require("./app/assets/fonts/DMSans-Regular.ttf"),
    DMSansBlack: require("./app/assets/fonts/DMSans-Black.ttf"),
    DMSansBold: require("./app/assets/fonts/DMSans-Bold.ttf"),
    DMSansExtraBold: require("./app/assets/fonts/DMSans-ExtraBold.ttf"),
    DMSansLight: require("./app/assets/fonts/DMSans-Light.ttf"),
    WorkSansMedium: require("./app/assets/fonts/WorkSans-Medium.ttf"),
    SoraMedium: require("./app/assets/fonts/Sora-Medium.ttf"),
    SoraRegular: require("./app/assets/fonts/Sora-Regular.ttf"),
  });

  return (
    <SafeAreaProvider style={{ flex: 1 }}>
      <Provider store={store}>
        <Routes />
      </Provider>
    </SafeAreaProvider>
  );
};

export default App;
