# 🔒 Security Configuration Guide

This document outlines the comprehensive security measures implemented in the Sunrise B2B App to protect sensitive data and credentials.

## 🚨 Security Issues Addressed

### Critical Issues Fixed:
- ✅ **Firebase Service Account Keys** - Moved to secure configuration
- ✅ **Hardcoded API Keys** - Moved to environment variables
- ✅ **Shopify Credentials** - Secured with environment variables
- ✅ **Development URLs** - Externalized to environment configuration
- ✅ **Android Keystore Passwords** - Secured with environment variables
- ✅ **Firebase Configuration** - Generated from environment variables

## 🔧 Quick Setup

### 1. Environment Configuration

```bash
# Copy the environment template
cp env.example .env

# Edit .env with your actual values
# Fill in all required variables marked with placeholders
```

### 2. Run Security Setup

```bash
# Install dependencies (includes dotenv)
npm install

# Run the complete security setup
npm run setup-security
```

### 3. Verify Configuration

```bash
# Generate Firebase config files
npm run generate-firebase-config

# Update Android build configuration
npm run generate-android-config
```

## 📋 Environment Variables Reference

### Required Variables

#### Shopify Configuration
```env
EXPO_PUBLIC_STOREFRONT_ACCESS_TOKEN=your_storefront_token
EXPO_PUBLIC_ADMIN_ACCESS_TOKEN=your_admin_token
SHOPIFY_CLIENT_ID=your_client_id
SHOPIFY_CLIENT_SECRET=your_client_secret
EXPO_PUBLIC_SHOPIFY_SHOP_ID=your_shop_id
```

#### Firebase Configuration
```env
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PROJECT_NUMBER=your_project_number
FIREBASE_ANDROID_API_KEY=your_android_api_key
FIREBASE_IOS_API_KEY=your_ios_api_key
```

#### App Configuration
```env
APP_BUNDLE_ID=com.yourcompany.yourapp
APP_NAME=YourAppName
```

### Optional Variables

#### Development URLs
```env
DEV_NGROK_URL=https://your-ngrok-url.ngrok-free.app
DEV_SERVEO_URL=https://your-subdomain.serveo.net
DEV_LOCAL_IP=*************
```

#### Android Keystore (for release builds)
```env
ANDROID_RELEASE_STORE_PASSWORD=your_secure_password
ANDROID_RELEASE_KEY_PASSWORD=your_secure_password
```

## 🛡️ Security Features

### 1. Environment Variable Protection
- All sensitive data moved to `.env` file
- Environment template provided (`env.example`)
- Validation of required variables

### 2. Version Control Security
- Updated `.gitignore` to exclude sensitive files
- Firebase service account keys excluded
- Generated configuration files excluded

### 3. NPM Package Security
- `.npmignore` prevents sensitive files from being published
- Backend examples and scripts excluded from package

### 4. Automated Configuration Generation
- Firebase config files generated from environment variables
- Android keystore configuration secured
- Backend configuration templates created

### 5. Development vs Production
- Separate configuration for development and production
- Secure fallbacks for missing configuration
- Environment-specific URL handling

## 🔄 Automated Scripts

### Security Setup
```bash
npm run setup-security
```
Complete security configuration setup and validation.

### Firebase Configuration
```bash
npm run generate-firebase-config
```
Generates `google-services.json` and `GoogleService-Info.plist` from environment variables.

### Android Configuration
```bash
npm run generate-android-config
```
Updates Android build configuration to use environment variables for keystore.

## 🚀 Development Workflow

### Initial Setup
1. Clone the repository
2. Copy `env.example` to `.env`
3. Fill in your actual values in `.env`
4. Run `npm run setup-security`
5. Start development with `npm start`

### Adding New Secrets
1. Add the variable to `.env`
2. Add placeholder to `env.example`
3. Update validation in `scripts/setup-security.js`
4. Update this documentation

## 🏗️ Production Deployment

### Environment Variables
Set all required environment variables in your deployment platform:
- Vercel: Project Settings → Environment Variables
- Netlify: Site Settings → Environment Variables
- AWS: Use Parameter Store or Secrets Manager
- Docker: Use secrets or environment files

### Security Checklist
- [ ] All environment variables set in production
- [ ] Firebase service account key securely stored
- [ ] Android release keystore properly configured
- [ ] HTTPS enabled for all endpoints
- [ ] CORS properly configured for backend
- [ ] API rate limiting implemented

## 🔍 Security Validation

The setup script validates:
- ✅ Required environment variables are set
- ✅ Firebase configuration files are generated
- ✅ Sensitive files are excluded from version control
- ✅ NPM package excludes sensitive files
- ✅ Android build configuration is secured

## 🆘 Troubleshooting

### Common Issues

#### Missing Environment Variables
```
❌ Missing critical environment variables
```
**Solution**: Check your `.env` file and ensure all required variables from `env.example` are set.

#### Firebase Configuration Errors
```
❌ Error generating Firebase configuration files
```
**Solution**: Verify Firebase environment variables are correct and run `npm run generate-firebase-config`.

#### Android Build Issues
```
❌ Keystore configuration errors
```
**Solution**: Check Android keystore environment variables and run `npm run generate-android-config`.

### Getting Help
1. Check this documentation
2. Verify your `.env` file against `env.example`
3. Run `npm run setup-security` to validate configuration
4. Check the console output for specific error messages

## 📞 Support

For security-related issues:
1. Check the troubleshooting section above
2. Verify all environment variables are correctly set
3. Ensure you're using the latest version of the setup scripts
