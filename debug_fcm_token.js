// Debug script to check FCM token
// Run this in your app's debug console or add to a test component

import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';

export const debugFCMToken = async () => {
  try {
    console.log('🔍 Debugging FCM Token...');
    
    // Check stored token
    const storedToken = await AsyncStorage.getItem('fcmToken');
    console.log('📱 Stored FCM Token:', storedToken);
    
    // Get fresh token
    const freshToken = await messaging().getToken();
    console.log('🔄 Fresh FCM Token:', freshToken);
    
    // Check permissions
    const authStatus = await messaging().hasPermission();
    console.log('🔐 Permission Status:', authStatus);
    
    // Check if tokens match
    if (storedToken === freshToken) {
      console.log('✅ Tokens match - FCM is properly configured');
    } else {
      console.log('⚠️ Tokens differ - may need to update stored token');
    }
    
    return {
      storedToken,
      freshToken,
      authStatus,
      tokensMatch: storedToken === freshToken
    };
    
  } catch (error) {
    console.error('❌ Error debugging FCM token:', error);
    return null;
  }
};

// Usage: Call this function in your app to debug
// debugFCMToken().then(result => console.log('Debug result:', result));
