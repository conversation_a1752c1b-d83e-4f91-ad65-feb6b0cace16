import { registerRootComponent } from 'expo';
import { setupBackgroundMessageHandler } from './app/config/firebaseConfig';

import App from './App';

// Initialize Firebase and setup background message handler
// This is done asynchronously to avoid blocking app startup
setupBackgroundMessageHandler().then((success) => {
  if (success) {
    console.log('✅ Firebase background message handler setup completed');
  } else {
    console.warn('⚠️ Firebase background message handler setup failed or skipped');
  }
}).catch((error) => {
  console.error('❌ Error during Firebase background handler setup:', error);
});

registerRootComponent(App);
