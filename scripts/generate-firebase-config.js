#!/usr/bin/env node

/**
 * Generate Firebase Configuration Files from Environment Variables
 * 
 * This script generates google-services.json and GoogleService-Info.plist
 * from environment variables to keep sensitive data out of version control.
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🔥 Generating Firebase configuration files from environment variables...\n');

// Validate required environment variables
const requiredVars = [
  'FIREBASE_PROJECT_ID',
  'FIREBASE_PROJECT_NUMBER',
  'FIREBASE_STORAGE_BUCKET',
  'FIREBASE_ANDROID_API_KEY',
  'FIREBASE_ANDROID_APP_ID',
  'FIREBASE_IOS_API_KEY',
  'FIREBASE_IOS_APP_ID',
  'FIREBASE_IOS_CLIENT_ID',
  'FIREBASE_IOS_REVERSED_CLIENT_ID',
  'APP_BUNDLE_ID'
];

const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => {
    console.error(`   - ${varName}`);
  });
  console.error('\nPlease check your .env file and ensure all required variables are set.');
  console.error('See env.example for reference.\n');
  process.exit(1);
}

// Generate google-services.json for Android
function generateGoogleServicesJson() {
  const googleServicesConfig = {
    project_info: {
      project_number: process.env.FIREBASE_PROJECT_NUMBER,
      project_id: process.env.FIREBASE_PROJECT_ID,
      storage_bucket: process.env.FIREBASE_STORAGE_BUCKET
    },
    client: [
      {
        client_info: {
          mobilesdk_app_id: process.env.FIREBASE_ANDROID_APP_ID,
          android_client_info: {
            package_name: process.env.APP_BUNDLE_ID
          }
        },
        oauth_client: [],
        api_key: [
          {
            current_key: process.env.FIREBASE_ANDROID_API_KEY
          }
        ],
        services: {
          appinvite_service: {
            other_platform_oauth_client: []
          }
        }
      }
    ],
    configuration_version: "1"
  };

  const outputPath = path.join(process.cwd(), 'google-services.json');
  fs.writeFileSync(outputPath, JSON.stringify(googleServicesConfig, null, 2));
  console.log('✅ Generated google-services.json');
}

// Generate GoogleService-Info.plist for iOS
function generateGoogleServiceInfoPlist() {
  const plistContent = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>${process.env.FIREBASE_IOS_CLIENT_ID}</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>${process.env.FIREBASE_IOS_REVERSED_CLIENT_ID}</string>
	<key>API_KEY</key>
	<string>${process.env.FIREBASE_IOS_API_KEY}</string>
	<key>GCM_SENDER_ID</key>
	<string>${process.env.FIREBASE_PROJECT_NUMBER}</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>${process.env.APP_BUNDLE_ID}</string>
	<key>PROJECT_ID</key>
	<string>${process.env.FIREBASE_PROJECT_ID}</string>
	<key>STORAGE_BUCKET</key>
	<string>${process.env.FIREBASE_STORAGE_BUCKET}</string>
	<key>IS_ADS_ENABLED</key>
	<false/>
	<key>IS_ANALYTICS_ENABLED</key>
	<false/>
	<key>IS_APPINVITE_ENABLED</key>
	<true/>
	<key>IS_GCM_ENABLED</key>
	<true/>
	<key>IS_SIGNIN_ENABLED</key>
	<true/>
	<key>GOOGLE_APP_ID</key>
	<string>${process.env.FIREBASE_IOS_APP_ID}</string>
</dict>
</plist>
`;

  // Generate for root ios directory
  const iosOutputPath = path.join(process.cwd(), 'ios', 'GoogleService-Info.plist');
  fs.writeFileSync(iosOutputPath, plistContent);
  console.log('✅ Generated ios/GoogleService-Info.plist');

  // Generate for app-specific directory
  const appOutputPath = path.join(process.cwd(), 'ios', 'Sunrise', 'GoogleService-Info.plist');
  fs.writeFileSync(appOutputPath, plistContent);
  console.log('✅ Generated ios/Sunrise/GoogleService-Info.plist');
}

// Main execution
try {
  generateGoogleServicesJson();
  generateGoogleServiceInfoPlist();
  
  console.log('\n🎉 Firebase configuration files generated successfully!');
  console.log('\n📝 Generated files:');
  console.log('   - google-services.json');
  console.log('   - ios/GoogleService-Info.plist');
  console.log('   - ios/Sunrise/GoogleService-Info.plist');
  console.log('\n⚠️  These files are automatically generated and should not be edited manually.');
  console.log('⚠️  They are excluded from version control for security.');
  
} catch (error) {
  console.error('❌ Error generating Firebase configuration files:', error.message);
  process.exit(1);
}
