#!/usr/bin/env node

/**
 * Security Verification Script
 * 
 * This script verifies that all security measures are properly implemented
 * and that the app can function with the new environment-based configuration.
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🔍 Verifying security configuration...\n');

// Test results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

// Helper function to run a test
function runTest(name, testFn, isWarning = false) {
  try {
    const result = testFn();
    if (result.success) {
      console.log(`✅ ${name}`);
      results.passed++;
      results.tests.push({ name, status: 'passed', message: result.message });
    } else {
      if (isWarning) {
        console.log(`⚠️  ${name}: ${result.message}`);
        results.warnings++;
        results.tests.push({ name, status: 'warning', message: result.message });
      } else {
        console.log(`❌ ${name}: ${result.message}`);
        results.failed++;
        results.tests.push({ name, status: 'failed', message: result.message });
      }
    }
  } catch (error) {
    console.log(`❌ ${name}: ${error.message}`);
    results.failed++;
    results.tests.push({ name, status: 'failed', message: error.message });
  }
}

// Test 1: Environment file exists and is valid
runTest('Environment file exists', () => {
  if (!fs.existsSync('.env')) {
    return { success: false, message: '.env file not found' };
  }
  
  const envContent = fs.readFileSync('.env', 'utf8');
  if (envContent.length < 100) {
    return { success: false, message: '.env file appears to be empty or incomplete' };
  }
  
  return { success: true, message: '.env file exists and has content' };
});

// Test 2: Critical environment variables are set
runTest('Critical environment variables', () => {
  const criticalVars = [
    'EXPO_PUBLIC_STOREFRONT_ACCESS_TOKEN',
    'EXPO_PUBLIC_SHOPIFY_STORE_URL',
    'SHOPIFY_CLIENT_ID',
    'FIREBASE_PROJECT_ID'
  ];
  
  const missingVars = criticalVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    return { success: false, message: `Missing variables: ${missingVars.join(', ')}` };
  }
  
  return { success: true, message: 'All critical variables are set' };
});

// Test 3: Sensitive files are excluded from version control
runTest('Sensitive files excluded from git', () => {
  if (!fs.existsSync('.gitignore')) {
    return { success: false, message: '.gitignore file not found' };
  }
  
  const gitignore = fs.readFileSync('.gitignore', 'utf8');
  const requiredExclusions = ['.env', 'firebase-service-account', 'google-services.json'];
  const missingExclusions = requiredExclusions.filter(exclusion => !gitignore.includes(exclusion));
  
  if (missingExclusions.length > 0) {
    return { success: false, message: `Missing exclusions: ${missingExclusions.join(', ')}` };
  }
  
  return { success: true, message: 'All sensitive files are excluded from git' };
});

// Test 4: NPM ignore file exists
runTest('NPM package security', () => {
  if (!fs.existsSync('.npmignore')) {
    return { success: false, message: '.npmignore file not found' };
  }
  
  const npmignore = fs.readFileSync('.npmignore', 'utf8');
  if (!npmignore.includes('.env') || !npmignore.includes('backend-example')) {
    return { success: false, message: '.npmignore does not exclude sensitive files' };
  }
  
  return { success: true, message: 'NPM package excludes sensitive files' };
});

// Test 5: Firebase configuration can be generated
runTest('Firebase configuration generation', () => {
  const requiredVars = [
    'FIREBASE_PROJECT_ID',
    'FIREBASE_PROJECT_NUMBER',
    'FIREBASE_ANDROID_API_KEY',
    'APP_BUNDLE_ID'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    return { success: false, message: `Missing Firebase variables: ${missingVars.join(', ')}` };
  }
  
  return { success: true, message: 'Firebase configuration variables are available' };
});

// Test 6: Apollo Client configuration
runTest('Apollo Client configuration', () => {
  const apolloClientPath = path.join(process.cwd(), 'app', 'api', 'appoloClient.ts');
  
  if (!fs.existsSync(apolloClientPath)) {
    return { success: false, message: 'Apollo Client file not found' };
  }
  
  const apolloContent = fs.readFileSync(apolloClientPath, 'utf8');
  
  if (apolloContent.includes('a07ab8516e503385c9783460cd1abf3f')) {
    return { success: false, message: 'Apollo Client still contains hardcoded token' };
  }
  
  if (!apolloContent.includes('process.env.EXPO_PUBLIC_STOREFRONT_ACCESS_TOKEN')) {
    return { success: false, message: 'Apollo Client not using environment variables' };
  }
  
  return { success: true, message: 'Apollo Client uses environment variables' };
});

// Test 7: Shopify Auth configuration
runTest('Shopify Auth configuration', () => {
  const shopifyAuthPath = path.join(process.cwd(), 'app', 'api', 'shopifyAuth.ts');
  
  if (!fs.existsSync(shopifyAuthPath)) {
    return { success: false, message: 'Shopify Auth file not found' };
  }
  
  const shopifyContent = fs.readFileSync(shopifyAuthPath, 'utf8');
  
  if (shopifyContent.includes('shp_14fe4f79-cc74-42bb-8f31-e072f2ff10d4')) {
    return { success: false, message: 'Shopify Auth still contains hardcoded credentials' };
  }
  
  if (!shopifyContent.includes('process.env.SHOPIFY_CLIENT_ID')) {
    return { success: false, message: 'Shopify Auth not using environment variables' };
  }
  
  return { success: true, message: 'Shopify Auth uses environment variables' };
});

// Test 8: Notification Service configuration
runTest('Notification Service configuration', () => {
  const notificationServicePath = path.join(process.cwd(), 'app', 'services', 'NotificationService.ts');
  
  if (!fs.existsSync(notificationServicePath)) {
    return { success: false, message: 'Notification Service file not found' };
  }
  
  const notificationContent = fs.readFileSync(notificationServicePath, 'utf8');
  
  if (!notificationContent.includes('process.env.DEV_NGROK_URL')) {
    return { success: false, message: 'Notification Service not using environment variables for URLs' };
  }
  
  return { success: true, message: 'Notification Service uses environment variables' };
});

// Warning Tests (non-critical)
runTest('Development URLs configured', () => {
  const hasNgrok = !!process.env.DEV_NGROK_URL;
  const hasServeo = !!process.env.DEV_SERVEO_URL;
  const hasLocalIP = !!process.env.DEV_LOCAL_IP;
  
  if (!hasNgrok && !hasServeo && !hasLocalIP) {
    return { success: false, message: 'No development URLs configured' };
  }
  
  return { success: true, message: 'Development URLs are configured' };
}, true);

runTest('Android keystore configuration', () => {
  const hasReleaseConfig = !!(
    process.env.ANDROID_RELEASE_STORE_PASSWORD &&
    process.env.ANDROID_RELEASE_KEY_PASSWORD
  );
  
  if (!hasReleaseConfig) {
    return { success: false, message: 'Android release keystore not configured' };
  }
  
  return { success: true, message: 'Android release keystore is configured' };
}, true);

// Run all tests
console.log('Running security verification tests...\n');

// Execute tests
runTest('Environment file exists', () => {
  if (!fs.existsSync('.env')) {
    return { success: false, message: '.env file not found' };
  }
  return { success: true, message: '.env file exists' };
});

// Add more test executions here...

// Final report
console.log('\n' + '='.repeat(60));
console.log('🔍 SECURITY VERIFICATION REPORT');
console.log('='.repeat(60));
console.log(`✅ Passed: ${results.passed}`);
console.log(`❌ Failed: ${results.failed}`);
console.log(`⚠️  Warnings: ${results.warnings}`);
console.log(`📊 Total: ${results.tests.length}`);

if (results.failed === 0) {
  console.log('\n🎉 All critical security tests passed!');
  if (results.warnings > 0) {
    console.log('⚠️  Some optional configurations are missing but the app should work.');
  }
} else {
  console.log('\n❌ Some security tests failed. Please address the issues above.');
}

console.log('\n📋 Recommendations:');
console.log('1. Address any failed tests before deploying');
console.log('2. Configure optional settings for better development experience');
console.log('3. Test the app thoroughly with the new configuration');
console.log('4. Set up production environment variables');

console.log('='.repeat(60));

// Exit with appropriate code
process.exit(results.failed > 0 ? 1 : 0);
