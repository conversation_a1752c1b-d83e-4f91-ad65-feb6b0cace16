#!/usr/bin/env node

/**
 * Generate Android Build Configuration from Environment Variables
 * 
 * This script updates android/app/build.gradle to use environment variables
 * for keystore configuration instead of hardcoded values.
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

console.log('🤖 Updating Android build configuration from environment variables...\n');

// Path to build.gradle file
const buildGradlePath = path.join(process.cwd(), 'android', 'app', 'build.gradle');

// Check if build.gradle exists
if (!fs.existsSync(buildGradlePath)) {
  console.error('❌ android/app/build.gradle not found');
  console.error('Make sure you are running this script from the project root directory.');
  process.exit(1);
}

// Read current build.gradle content
let buildGradleContent = fs.readFileSync(buildGradlePath, 'utf8');

// Define the new signing configs section with environment variables
const newSigningConfigs = `    signingConfigs {
        debug {
            storeFile file(System.getenv('ANDROID_DEBUG_STORE_FILE') ?: 'debug.keystore')
            storePassword System.getenv('ANDROID_DEBUG_STORE_PASSWORD') ?: 'android'
            keyAlias System.getenv('ANDROID_DEBUG_KEY_ALIAS') ?: 'androiddebugkey'
            keyPassword System.getenv('ANDROID_DEBUG_KEY_PASSWORD') ?: 'android'
        }
        release {
            if (System.getenv('ANDROID_RELEASE_STORE_FILE')) {
                storeFile file(System.getenv('ANDROID_RELEASE_STORE_FILE'))
                storePassword System.getenv('ANDROID_RELEASE_STORE_PASSWORD')
                keyAlias System.getenv('ANDROID_RELEASE_KEY_ALIAS')
                keyPassword System.getenv('ANDROID_RELEASE_KEY_PASSWORD')
            } else {
                // Fallback to debug keystore if release keystore is not configured
                storeFile file('debug.keystore')
                storePassword 'android'
                keyAlias 'androiddebugkey'
                keyPassword 'android'
            }
        }
    }`;

// Replace the existing signingConfigs section
const signingConfigsRegex = /signingConfigs\s*\{[^}]*\}[^}]*\}/s;

if (signingConfigsRegex.test(buildGradleContent)) {
  buildGradleContent = buildGradleContent.replace(signingConfigsRegex, newSigningConfigs);
  console.log('✅ Updated existing signingConfigs section');
} else {
  // If signingConfigs section doesn't exist, add it after defaultConfig
  const defaultConfigRegex = /(defaultConfig\s*\{[^}]*\})/s;
  if (defaultConfigRegex.test(buildGradleContent)) {
    buildGradleContent = buildGradleContent.replace(
      defaultConfigRegex,
      `$1\n    ${newSigningConfigs}`
    );
    console.log('✅ Added new signingConfigs section');
  } else {
    console.error('❌ Could not find defaultConfig section in build.gradle');
    process.exit(1);
  }
}

// Write the updated content back to the file
fs.writeFileSync(buildGradlePath, buildGradleContent);

console.log('\n🎉 Android build configuration updated successfully!');
console.log('\n📝 Changes made:');
console.log('   - Keystore configuration now uses environment variables');
console.log('   - Debug keystore uses default values if env vars not set');
console.log('   - Release keystore falls back to debug if not configured');
console.log('\n⚠️  Make sure to set the following environment variables for release builds:');
console.log('   - ANDROID_RELEASE_STORE_FILE');
console.log('   - ANDROID_RELEASE_STORE_PASSWORD');
console.log('   - ANDROID_RELEASE_KEY_ALIAS');
console.log('   - ANDROID_RELEASE_KEY_PASSWORD');
console.log('\n💡 See env.example for reference values.');
