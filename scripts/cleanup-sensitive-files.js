#!/usr/bin/env node

/**
 * Cleanup Sensitive Files Script
 * 
 * This script removes sensitive files that should not be in version control
 * and creates secure alternatives using environment variables.
 */

const fs = require('fs');
const path = require('path');

console.log('🧹 Cleaning up sensitive files from version control...\n');

// List of sensitive files to remove
const sensitiveFiles = [
  'backend-example/fir-analytics-75b8b-firebase-adminsdk-fbsvc-5e4691a4e7.json',
  'google-services.json',
  'ios/GoogleService-Info.plist',
  'ios/Sunrise/GoogleService-Info.plist'
];

// Function to safely remove a file
function removeFile(filePath) {
  const fullPath = path.join(process.cwd(), filePath);
  
  if (fs.existsSync(fullPath)) {
    try {
      fs.unlinkSync(fullPath);
      console.log(`✅ Removed: ${filePath}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to remove ${filePath}:`, error.message);
      return false;
    }
  } else {
    console.log(`ℹ️  File not found (already removed): ${filePath}`);
    return true;
  }
}

// Function to create a secure replacement notice
function createReplacementNotice(filePath, instructions) {
  const fullPath = path.join(process.cwd(), filePath);
  const dir = path.dirname(fullPath);
  
  // Ensure directory exists
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  
  const noticeContent = `# SECURITY NOTICE
# 
# This file has been removed for security reasons.
# ${instructions}
# 
# This file is automatically generated and should not be committed to version control.
# See SECURITY.md for more information.
`;

  const noticePath = fullPath + '.REMOVED';
  fs.writeFileSync(noticePath, noticeContent);
  console.log(`📝 Created notice: ${filePath}.REMOVED`);
}

// Main cleanup process
function main() {
  console.log('🔍 Scanning for sensitive files...\n');
  
  let removedCount = 0;
  
  // Remove Firebase service account key
  if (removeFile('backend-example/fir-analytics-75b8b-firebase-adminsdk-fbsvc-5e4691a4e7.json')) {
    removedCount++;
    createReplacementNotice(
      'backend-example/firebase-service-account.json',
      'Download your Firebase service account key and place it here.'
    );
  }
  
  // Remove Google Services configuration files
  if (removeFile('google-services.json')) {
    removedCount++;
    createReplacementNotice(
      'google-services.json',
      'Run "npm run generate-firebase-config" to generate this file from environment variables.'
    );
  }
  
  if (removeFile('ios/GoogleService-Info.plist')) {
    removedCount++;
    createReplacementNotice(
      'ios/GoogleService-Info.plist',
      'Run "npm run generate-firebase-config" to generate this file from environment variables.'
    );
  }
  
  if (removeFile('ios/Sunrise/GoogleService-Info.plist')) {
    removedCount++;
    createReplacementNotice(
      'ios/Sunrise/GoogleService-Info.plist',
      'Run "npm run generate-firebase-config" to generate this file from environment variables.'
    );
  }
  
  // Create backend config directory structure
  const backendConfigDir = path.join(process.cwd(), 'backend-example', 'config');
  if (!fs.existsSync(backendConfigDir)) {
    fs.mkdirSync(backendConfigDir, { recursive: true });
    console.log('📁 Created backend-example/config directory');
  }
  
  // Create backend .gitignore
  const backendGitignorePath = path.join(process.cwd(), 'backend-example', '.gitignore');
  const backendGitignoreContent = `# Backend sensitive files
.env
.env.*
config/firebase-service-account*.json
config/secrets.json
node_modules/
*.log
`;
  
  fs.writeFileSync(backendGitignorePath, backendGitignoreContent);
  console.log('📝 Created backend-example/.gitignore');
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log(`🎉 Cleanup completed! Removed ${removedCount} sensitive files.`);
  console.log('\n📋 Next steps:');
  console.log('1. Run "npm run setup-security" to configure secure alternatives');
  console.log('2. Download your Firebase service account key to backend-example/config/');
  console.log('3. Update your .env file with actual values');
  console.log('4. Commit the security improvements to version control');
  console.log('\n⚠️  The removed files will be regenerated from environment variables.');
  console.log('⚠️  Make sure your .env file is properly configured before building.');
  console.log('='.repeat(60));
}

main();
