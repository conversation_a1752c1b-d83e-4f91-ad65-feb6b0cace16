#!/usr/bin/env node

/**
 * Security Setup Script for Sunrise B2B App
 * 
 * This script sets up the complete security configuration for the project:
 * - Validates environment variables
 * - Generates Firebase configuration files
 * - Updates Android build configuration
 * - Creates secure backend configuration
 * - Validates security measures
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔒 Setting up security configuration for Sunrise B2B App...\n');

// Check if .env file exists
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  console.error('❌ .env file not found!');
  console.error('Please copy env.example to .env and fill in your values:');
  console.error('   cp env.example .env');
  console.error('   # Then edit .env with your actual values\n');
  process.exit(1);
}

// Load environment variables
require('dotenv').config();

console.log('✅ Found .env file');

// Function to run a script and handle errors
function runScript(scriptName, description) {
  try {
    console.log(`\n🔧 ${description}...`);
    execSync(`node ${scriptName}`, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`❌ Failed to ${description.toLowerCase()}`);
    return false;
  }
}

// Function to validate critical environment variables
function validateEnvironmentVariables() {
  console.log('\n🔍 Validating environment variables...');
  
  const criticalVars = [
    'EXPO_PUBLIC_STOREFRONT_ACCESS_TOKEN',
    'EXPO_PUBLIC_SHOPIFY_STORE_URL',
    'SHOPIFY_CLIENT_ID',
    'SHOPIFY_CLIENT_SECRET',
    'FIREBASE_PROJECT_ID',
    'FIREBASE_PROJECT_NUMBER',
    'APP_BUNDLE_ID'
  ];

  const missingVars = criticalVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing critical environment variables:');
    missingVars.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    console.error('\nPlease update your .env file with the missing values.');
    return false;
  }
  
  console.log('✅ All critical environment variables are set');
  return true;
}

// Function to create secure backend configuration
function createBackendConfig() {
  console.log('\n🔧 Creating secure backend configuration...');
  
  const backendConfigDir = path.join(process.cwd(), 'backend-example', 'config');
  
  // Create config directory if it doesn't exist
  if (!fs.existsSync(backendConfigDir)) {
    fs.mkdirSync(backendConfigDir, { recursive: true });
  }
  
  // Create backend .env file
  const backendEnvContent = `# Backend Environment Configuration
# This file should be kept secure and not committed to version control

# Firebase Configuration
FIREBASE_PROJECT_ID=${process.env.FIREBASE_PROJECT_ID}
FIREBASE_SERVICE_ACCOUNT_PATH=./config/firebase-service-account.json

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/sunrisetrade

# Server Configuration
PORT=3000
NODE_ENV=development

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://your-frontend-domain.com
`;

  const backendEnvPath = path.join(process.cwd(), 'backend-example', '.env');
  fs.writeFileSync(backendEnvPath, backendEnvContent);
  
  console.log('✅ Created backend-example/.env');
  
  // Create README for backend security
  const backendReadmeContent = `# Backend Security Configuration

## 🔒 Security Setup

1. **Firebase Service Account Key**
   - Download your Firebase service account key from Firebase Console
   - Save it as \`config/firebase-service-account.json\`
   - Never commit this file to version control

2. **Environment Variables**
   - Copy the generated \`.env\` file and update values as needed
   - Set \`MONGODB_URI\` to your actual MongoDB connection string
   - Update \`ALLOWED_ORIGINS\` with your actual frontend domains

3. **Production Deployment**
   - Use environment variables instead of .env files in production
   - Ensure all sensitive data is properly secured
   - Use proper SSL/TLS certificates

## 🚀 Running the Backend

\`\`\`bash
cd backend-example
npm install
npm start
\`\`\`

## 📝 Environment Variables

See the generated \`.env\` file for all required variables.
`;

  const backendReadmePath = path.join(process.cwd(), 'backend-example', 'SECURITY.md');
  fs.writeFileSync(backendReadmePath, backendReadmeContent);
  
  console.log('✅ Created backend-example/SECURITY.md');
}

// Function to validate security measures
function validateSecurity() {
  console.log('\n🔍 Validating security measures...');
  
  const checks = [
    {
      name: 'Environment file exists',
      check: () => fs.existsSync('.env'),
      fix: 'Copy env.example to .env and fill in values'
    },
    {
      name: 'Firebase config files generated',
      check: () => fs.existsSync('google-services.json') && fs.existsSync('ios/GoogleService-Info.plist'),
      fix: 'Run: node scripts/generate-firebase-config.js'
    },
    {
      name: 'Sensitive files in .gitignore',
      check: () => {
        const gitignore = fs.readFileSync('.gitignore', 'utf8');
        return gitignore.includes('.env') && gitignore.includes('firebase-service-account');
      },
      fix: 'Update .gitignore to exclude sensitive files'
    },
    {
      name: 'NPM ignore file exists',
      check: () => fs.existsSync('.npmignore'),
      fix: 'Create .npmignore file to exclude sensitive files from npm package'
    }
  ];

  let allPassed = true;
  
  checks.forEach(({ name, check, fix }) => {
    if (check()) {
      console.log(`✅ ${name}`);
    } else {
      console.log(`❌ ${name}`);
      console.log(`   Fix: ${fix}`);
      allPassed = false;
    }
  });
  
  return allPassed;
}

// Main execution
async function main() {
  let success = true;
  
  // Step 1: Validate environment variables
  if (!validateEnvironmentVariables()) {
    success = false;
  }
  
  // Step 2: Generate Firebase configuration
  if (success) {
    success = runScript('scripts/generate-firebase-config.js', 'Generating Firebase configuration files');
  }
  
  // Step 3: Update Android configuration
  if (success) {
    success = runScript('scripts/generate-android-config.js', 'Updating Android build configuration');
  }
  
  // Step 4: Create backend configuration
  if (success) {
    createBackendConfig();
  }
  
  // Step 5: Validate security measures
  const securityValid = validateSecurity();
  
  // Final report
  console.log('\n' + '='.repeat(60));
  if (success && securityValid) {
    console.log('🎉 Security setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Review generated configuration files');
    console.log('2. Test your app with the new environment variables');
    console.log('3. Set up production environment variables');
    console.log('4. Configure your CI/CD pipeline with secure variables');
  } else {
    console.log('❌ Security setup completed with issues');
    console.log('Please address the issues above and run the script again.');
  }
  console.log('='.repeat(60));
}

main().catch(error => {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
});
