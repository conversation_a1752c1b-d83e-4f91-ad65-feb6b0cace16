# =============================================================================
# NPM PACKAGE SECURITY - EXCLUDE SENSITIVE FILES FROM PUBLICATION
# =============================================================================
# This file prevents sensitive files from being published to npm registry
# =============================================================================

# =============================================================================
# SECURITY - SENSITIVE FILES
# =============================================================================
# Environment files with sensitive data
.env
.env.*
env.example
.env*.local

# Firebase service account keys
firebase-service-account*.json
*-firebase-adminsdk-*.json
service-account*.json
backend-example/fir-analytics-*.json

# Google Services configuration files
google-services.json
GoogleService-Info.plist
ios/GoogleService-Info.plist
ios/Sunrise/GoogleService-Info.plist

# Android keystore files
*.keystore
*.jks
release.keystore
debug.keystore
android/app/debug.keystore
android/app/release.keystore

# iOS certificates and provisioning profiles
*.p12
*.p8
*.mobileprovision
*.cer
*.certSigningRequest

# API keys and secrets
secrets.json
config/secrets.json
api-keys.json

# Backend configuration and examples
backend-example/
scripts/

# =============================================================================
# DEVELOPMENT FILES
# =============================================================================
# Development tools and configurations
.expo/
.vscode/
.idea/
*.log
npm-debug.*
yarn-debug.*
yarn-error.*

# Testing
coverage/
.nyc_output/
test-results/

# Build artifacts
dist/
build/
web-build/
.expo-shared/

# Platform specific
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Documentation that shouldn't be in package
README.md
SECURITY.md
docs/
documentation/

# Configuration files
.eslintrc.*
.prettierrc.*
babel.config.*
metro.config.*
jest.config.*

# Patches and temporary files
patches/
*.patch
*.orig
*.rej
*.tmp

# =============================================================================
# INCLUDE ONLY ESSENTIAL FILES
# =============================================================================
# The package should only include:
# - Source code (app/ directory)
# - Package configuration (package.json)
# - Essential configuration files
# - Public assets
# =============================================================================
