import AsyncStorage from '@react-native-async-storage/async-storage';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type LanguageCode = 'en' | 'ms';

export const languageWithCode: Record<LanguageCode, string> = {
  en: 'English',
  ms: 'Malay',
};

export interface LanguageState {
  locale: LanguageCode;
}

const initialState: LanguageState = {
  locale: 'en',
};

const languageSlice = createSlice({
  name: 'language',
  initialState,
  reducers: {
    setLanguage: (state, action: PayloadAction<LanguageCode>) => {
      state.locale = action.payload;
      AsyncStorage.setItem('appLocale', action.payload).catch(console.error);
    },
  },
});

export const getSavedLanguage = async (): Promise<LanguageCode | null> => {
  try {
    const locale = await AsyncStorage.getItem('appLocale');
    if (locale === 'en' || locale === 'ms') {
      return locale;
    }
    return null;
  } catch (err) {
    console.error('Error reading language from storage:', err);
    return null;
  }
};

export const { setLanguage } = languageSlice.actions;
export default languageSlice.reducer;