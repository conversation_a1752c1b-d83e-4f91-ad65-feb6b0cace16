import React from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import HomeScreen from "../Screens/Home/Home";
import BottomTab from "../layout/BottomTab";
import MyCart from "../Screens/MyCart/MyCart";
import Category from "../Screens/Category/Category";
import Profile from "../Screens/profile/Profile";
import { BottomTabParamList } from "./BottomTabParamList";
import Wishlist from "../Screens/Wishlist/Wishlist";
import { Image, Platform } from "react-native";
import Search from "../Screens/search/Search";
import CompanyRegistration from "../Screens/CompanyRegistration/CompanyRegistration";
import { LanguageState } from "../redux/reducer/languageReducer";
import { useSelector } from "react-redux";
import { staticTranslations } from "../constants/staticTranslations";
import ProfileIOS from "../Screens/profile/ProfileIOS";

const Tab = createBottomTabNavigator<BottomTabParamList>();

const BottomNavigation = () => {
  const { locale }: LanguageState = useSelector((state: any) => state.language);
  const staticLabels = locale === "en" ? staticTranslations.english : staticTranslations.malay;
  return (
    <Tab.Navigator
      initialRouteName="Home"
      screenOptions={{
        headerShown: false,
      }}
      tabBar={(props: any) => {
        const isCartActive =
          props.state.routes[props.state.index].name === "MyCart";
        return isCartActive ? null : <BottomTab {...props} />;
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          title: staticLabels.home
        }}
      // options={{
      //   headerShown: true,
      //   title: "",
      //   headerLeft: () => (
      //     <Image
      //       source={require("../../assets/Sunrisetradedarklogo.png")}
      //       style={{ width: 150, height: 50, marginHorizontal: 10 }}
      //     />
      //   ),
      //   // headerRight={()=>()}
      // }}
      />
      <Tab.Screen name="Category" component={Category}
        options={{
          title: staticLabels.category,
        }} />
      <Tab.Screen name="Search" component={Search}
        options={{
          title: staticLabels.search
        }} />
      <Tab.Screen name="MyCart" component={MyCart}
        options={{
          title: staticLabels.my_cart
        }} />
      <Tab.Screen name="Profile" component={Platform.OS === "android" ? Profile : ProfileIOS}
        options={{
          title: staticLabels.profile
        }} />
    </Tab.Navigator>
  );
};

export default BottomNavigation;
