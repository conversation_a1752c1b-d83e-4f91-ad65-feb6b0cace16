import React, { useEffect, useState } from "react";
import {
  NavigationContainer,
  DefaultTheme as NavigationDefaultTheme,
  DarkTheme as NavigationDarkTheme,
} from "@react-navigation/native";
import StackNavigator from "./StackNavigator";
import themeContext from "../constants/themeContext";
import { COLORS } from "../constants/theme";
import { useDispatch, useSelector } from "react-redux";
import { setCartId } from "../redux/reducer/cartReducer";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { SafeAreaView } from "react-native-safe-area-context";
import { getSavedLanguage, LanguageCode, LanguageState, setLanguage } from "../redux/reducer/languageReducer";
import { ApolloProvider } from "@apollo/client";
import getClient from "../api/appoloClient";
import { View, Text } from "react-native";
import { ActivityIndicator } from "react-native-paper";

const { Provider: ThemeProvider } = themeContext;

const Routes = () => {
  const [apolloClient, setApolloClient] = useState(null);
  const { locale }: LanguageState = useSelector((state: any) => state.language)
  const dispatch = useDispatch();
  const [isDarkTheme, setIsDarkTheme] = useState(false);

  useEffect(() => {
    const getCartId = async () => {
      const id = await AsyncStorage.getItem("cartId");
      if (id !== null && typeof id === "string") {
        dispatch(setCartId(id));
      }
    };
    getCartId();
  }, []);

  const authContext = React.useMemo(
    () => ({
      setDarkTheme: () => {
        setIsDarkTheme(true);
      },
      setLightTheme: () => {
        setIsDarkTheme(false);
      },
    }),
    []
  );

  const CustomDefaultTheme = {
    ...NavigationDefaultTheme,
    colors: {
      ...NavigationDefaultTheme.colors,
      background: COLORS.background,
      title: COLORS.title,
      card: COLORS.card,
      text: COLORS.text,
      textLight: COLORS.textLight,
      input: COLORS.input,
      border: COLORS.borderColor,
    },
  };

  const CustomDarkTheme = {
    ...NavigationDarkTheme,
    colors: {
      ...NavigationDarkTheme.colors,
      background: COLORS.darkBackground,
      title: COLORS.darkTitle,
      card: COLORS.darkCard,
      text: COLORS.darkText,
      textLight: COLORS.darkTextLight,
      input: COLORS.darkInput,
      border: COLORS.darkBorder,
    },
  };

  const theme = isDarkTheme ? CustomDarkTheme : CustomDefaultTheme;
  useEffect(() => {
    (async () => {
      try {
        console.log("App Reloaded-------------------");
        const savedLocale = await getSavedLanguage();
        if (savedLocale && savedLocale !== locale) {
          dispatch(setLanguage(savedLocale));
        }
        const client = getClient(savedLocale || locale);
        setApolloClient(client);
      } catch (error) {
        console.error("❌ Failed to create Apollo client", error);
      }
    })();
  }, [locale]);
  if (apolloClient === null) {
    return (
      <View style={{flex:1,}}>
        <ActivityIndicator size={"small"} color={COLORS.black} style={
          {
            height:780
          }
        } />
      </View>
    )
  }

  return (
    <ApolloProvider client={apolloClient} key={locale}>
      <ThemeProvider value={authContext}>
        <SafeAreaView style={{ flex: 1, backgroundColor: COLORS.background }}>
          <NavigationContainer theme={theme}>
            <StackNavigator />
          </NavigationContainer>
        </SafeAreaView>
      </ThemeProvider>
    </ApolloProvider>
  );
};

export default Routes;


function async(arg0: () => void) {
  throw new Error("Function not implemented.");
}