import { gql } from "@apollo/client";
export const GET_HOMEPAGE_BANNERS = gql`
  query getHomepageBanners {
    metaobjects(type: "mob_app_hero_banner", first: 10) {
      edges {
        node {
          id
          fields {
            key
            value
            reference {
              ... on MediaImage {
                image {
                  url
                }
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_COLLECTION_PRODUCTS = gql`
  query CollectionWithSortedProducts(
    $handle: String!
    $first: Int
    $after: String
    $filters: [ProductFilter!]
    $sortKey: ProductCollectionSortKeys
    $reverse: Boolean
  ) {
    collectionByHandle(handle: $handle) {
      title
      image {
        url
      }
      products(
        first: $first
        after: $after
        filters: $filters
        sortKey: $sortKey
        reverse: $reverse
      ) {
        pageInfo {
          hasNextPage
          endCursor
        }
        nodes {
          id
          title
          handle
          description
          tags
          images(first: 10) {
            edges {
              node {
                src
              }
            }
          }
          options {
            name
            values
          }
          variants(first: 50) {
            edges {
              node {
                id
                title
                price {
                  amount
                  currencyCode
                }
                image {
                  url
                }
                selectedOptions {
                 name
                 value
                }
                quantityAvailable
                availableForSale
              }
            }
          }
        }
        filters {
          id
          label
          type
          values {
            label
            count
            swatch {
              color
            }
            input
          }
        }
      }
    }
  }
`;

export const GET_VARIANT_BY_PRODUCT_HANDLE = gql`
  query GetVariantsProductHandle($handle: String!) {
    productByHandle(handle: $handle) {
      title
      variants(first: 50) {
        edges {
          node {
            price {
              amount
              currencyCode
            }
            title
            image {
              url
            }
            id
            quantityAvailable
            availableForSale
            selectedOptions {
             name
             value
            } 
          }
        }
      }
      title
    }
  }
`;

export const GET_HOMEPAGE_CMS = gql`
  query getHomepageBanners {
    metaobjects(type: "mobile_app_home_page_cms", first: 10) {
      edges {
        node {
          id
          fields {
            key
            value
            type
            reference {
              ... on MediaImage {
                image {
                  url
                }
              }
            }
          }
        }
      }
    }
  }
`;

export const GET_LIST_SLIDER = gql`
  query MyQuery($type: String) {
    metaobjects(type: $type, first: 10) {
      edges {
        node {
          id
          fields {
            key
            value
            reference {
              ... on MediaImage {
                image {
                  url
                }
              }
            }
          }
        }
      }
    }
  }
`;
export const PRODUCT_CARD_FRAGMENT = `
fragment ProductCard on Product {
  handle
  title
  tags
  vendor
  featuredImage {
    height
    width
    src
    altText
  }
  variants(first: 10) {
    edges {
      node {
        sku
        title
        image {
          src
        }
        price {
          amount
        }
          selectedOptions {
        name
        value
      }
      }
    }
  }
}

`;

export const HOME_PAGE_FEATURED_COLLECTION = gql`
  ${PRODUCT_CARD_FRAGMENT}
  query homepageFeaturedCollections {
    Section_sorting: metaobjects(type: "app_sections_sorting", first: 20) {
      nodes {
        fields {
          key
          value
        }
      }
    }

    App_top_banner_slider: metaobjects(
      type: "app_top_banner_slider"
      first: 10
    ) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
            }
          }
        }
      }
    }
    Explore_category: metaobjects(type: "app_shop_by_row_1_", first: 10) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
            }
          }
        }
      }
    }

    app_products_by_collection_1: metaobjects(
      type: "app_products_by_collection_1_"
      first: 10
    ) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
            }
          }
        }
      }
    }

    Single_banner: metaobjects(type: "single_banner_1_", first: 1) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
            }
          }
        }
      }
    }
    Product_by_coll_1: metaobjects(
      type: "app_products_by_collection_1_"
      first: 1
    ) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
              products(first: 10) {
                edges {
                  node {
                    ...ProductCard
                  }
                }
              }
            }
          }
        }
      }
    }
    Single_banner_2: metaobjects(type: "single_banner_2", first: 1) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
            }
          }
        }
      }
    }
    Product_by_coll_2: metaobjects(
      type: "app_products_by_collection_2_"
      first: 1
    ) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
              products(first: 10) {
                edges {
                  node {
                    ...ProductCard
                  }
                }
              }
            }
          }
        }
      }
    }
    Single_banner_3: metaobjects(type: "single_banner_3", first: 1) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
            }
          }
        }
      }
    }
    BadmintoCollection: metaobjects(type: "app_shop_by_row_2_", first: 10) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
            }
          }
        }
      }
    }

    app_products_by_collection_1: metaobjects(
      type: "app_products_by_collection_1_"
      first: 10
    ) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
            }
          }
        }
      }
    }
    Product_by_coll_3: metaobjects(
      type: "app_products_by_collection_3_"
      first: 10
    ) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
              title
              products(first: 10) {
                edges {
                  node {
                    handle
                    featuredImage {
                      height
                      altText
                      src
                      width
                    }
                    title
                    tags
                    variants(first: 10) {
                      edges {
                        node {
                          sku
                          title
                          selectedOptions {
                            name
                            value
                          }
                          quantityAvailable
                        }
                      }
                    }
                    vendor
                  }
                }
              }
            }
          }
        }
      }
    }

    Single_banner_4: metaobjects(type: "single_banner_4", first: 1) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
            }
          }
        }
      }
    }
    Product_by_coll_4: metaobjects(
      type: "app_products_by_collection_4_"
      first: 1
    ) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
              products(first: 10) {
                edges {
                  node {
                    ...ProductCard
                  }
                }
              }
            }
          }
        }
      }
    }
    Product_by_coll_5: metaobjects(
      type: "app_products_by_collection_5_"
      first: 10
    ) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
              title
              products(first: 10) {
                edges {
                  node {
                    ...ProductCard
                  }
                }
              }
            }
          }
        }
      }
    }
    Single_banner_5: metaobjects(type: "single_banner_5", first: 1) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
            }
          }
        }
      }
    }
    APP_Shop_By_Row: metaobjects(type: "app_shop_by_row_3_", first: 10) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
            }
          }
        }
      }
    }
    Basketball_Collection: metaobjects(
      type: "app_products_by_collection_6_"
      first: 10
    ) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
              title
              products(first: 10) {
                edges {
                  node {
                    ...ProductCard
                  }
                }
              }
            }
          }
        }
      }
    }
    Football_Collection: metaobjects(type: "football_collection", first: 10) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
              title
              products(first: 10) {
                edges {
                  node {
                    ...ProductCard
                  }
                }
              }
            }
          }
        }
      }
    }
    Single_banner_6: metaobjects(type: "single_banner_6", first: 1) {
      nodes {
        fields {
          key
          value
          reference {
            ... on MediaImage {
              image {
                url
              }
            }
            ... on Collection {
              handle
            }
          }
        }
      }
    }
  }
`;
