import { ApolloClient, InMemoryCache, HttpLink } from "@apollo/client";
import { LanguageCode } from "../redux/reducer/languageReducer";

const getClient = (locale: LanguageCode) => {
  console.log("Inside Get Client", locale)
  const client = new ApolloClient({
    link: new HttpLink({
      uri: "https://sunrise-trade.myshopify.com/api/2025-01/graphql.json",
      headers: {
        "X-Shopify-Storefront-Access-Token": "a07ab8516e503385c9783460cd1abf3f",
        "Content-Type": "application/json",
        "Accept-Language": locale,
      },
    }),
    cache: new InMemoryCache(),
  });
  return client;
};


export default getClient;
