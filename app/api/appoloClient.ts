import { ApolloClient, InMemoryCache, HttpLink } from "@apollo/client";
import { LanguageCode } from "../redux/reducer/languageReducer";

// Get configuration from environment variables
const STOREFRONT_API_URL = process.env.EXPO_PUBLIC_STOREFRONT_API_URL;
const STOREFRONT_ACCESS_TOKEN = process.env.EXPO_PUBLIC_STOREFRONT_ACCESS_TOKEN;

const getClient = (locale: LanguageCode) => {
  console.log("Inside Get Client", locale);

  const client = new ApolloClient({
    link: new HttpLink({
      uri: STOREFRONT_API_URL,
      headers: {
        "X-Shopify-Storefront-Access-Token": STOREFRONT_ACCESS_TOKEN,
        "Content-Type": "application/json",
        "Accept-Language": locale,
      },
    }),
    cache: new InMemoryCache(),
  });

  return client;
};

export default getClient;
