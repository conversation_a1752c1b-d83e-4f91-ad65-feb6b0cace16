import React, { useEffect, useState } from "react";
import {
  View,
  StyleSheet,
  ActivityIndicator,
  Text,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Platform,
} from "react-native";
import { useNavigation, CommonActions } from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useShopifyAuth } from "../../api/shopifyAuth";
import { COLORS, FONTS } from "../../constants/theme";
import { IMAGES } from "../../constants/Images";
import ShopifyWebView from "../../components/ShopifyWebView";
import { GlobalStyleSheet } from "../../constants/StyleSheet";
import SunriseLogo from "../../../assets/Sunrisetradedarklogo.png";
import ShopifyLoginIOS from "./ShopifyLoginIOS";
import NotificationService from "../../services/NotificationService";

const ShopifyLogin = () => {
  const navigation = useNavigation();
  const [showWebView, setShowWebView] = useState(false);

  // useEffect(() => {
  //   const handleNavigation = async () => {
  //     // Wait for the verification check
  //     const isCompanyVerified = await AsyncStorage.getItem("isCompanyVerified");
  //     const isLoggedIn = await AsyncStorage.getItem("isLoggedIn");
  //     const customerToken = await AsyncStorage.getItem("customerToken");
  //     console.log("isCompanyVerified>>>>>>>>>>", isCompanyVerified);
  //     console.log("isLoggedIn≤..,.,.,>>>>>>>>>>", isLoggedIn);
  //     console.log("customerToken>>>>>>>>>>", customerToken);

  //     if (isCompanyVerified === "true"&& isLoggedIn === "true" && customerToken) {
  //       navigation.dispatch(
  //         CommonActions.reset({
  //           index: 0,
  //           routes: [{ name: "DrawerNavigation", params: { screen: "Home" } }],
  //         })
  //       );
  //     }
  //     else if (isLoggedIn === "true" && customerToken && isCompanyVerified === "false") {
  //       navigation.dispatch(
  //         CommonActions.reset({
  //           index: 0,
  //           routes: [{ name: "CompanyRegistration" }],
  //         })
  //       );
  //     }
  //   };

  //   handleNavigation();
  // }, [navigation]);

  const handleLoginWithShopify = async () => {
    setShowWebView(true);
  };

  const handleLoginSuccess = async (userData: any) => {
    console.log("✅ Login successful:", userData);

    // Hide the WebView immediately
    setShowWebView(false);

    // Trigger login success notification - this is the ONLY place it should be called
    try {
      await NotificationService.onLoginSuccess({
        accessToken: userData.token || userData.accessToken,
        customerData: userData.customerData,
        id: userData.customerData?.id || userData.customerData?.formattedId
      });
      console.log("✅ Login notification sent successfully");
    } catch (error) {
      console.error("❌ Error sending login notification:", error);
    }

    // The ShopifyWebView component will handle the navigation directly
    // based on verification status
  };

  if (showWebView) {
    return Platform.OS === "android" ? (
      <ShopifyWebView onLoginSuccess={handleLoginSuccess} />
    ) : (
      <ShopifyLoginIOS onLoginSuccess={handleLoginSuccess} />
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.logoContainer}>
        <Image source={SunriseLogo} style={styles.logo} resizeMode="contain" />
      </View>

      <View style={styles.contentContainer}>
        <Text style={styles.title}>Welcome to Sunrise Trade</Text>
        <Text style={styles.subtitle}>
          Sign in with your Shopify account to access your business account
        </Text>

        <TouchableOpacity
          style={styles.loginButton}
          onPress={handleLoginWithShopify}
        >
          <Image
            source={IMAGES.shopifyLogo}
            style={styles.shopifyLogo}
            resizeMode="contain"
          />
          <Text style={styles.loginButtonText}>Login with Shopify</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: COLORS.white,
  },
  logoContainer: {
    alignItems: "center",
    marginTop: 60,
  },
  logo: {
    width: 200,
    height: 80,
  },
  contentContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  title: {
    ...FONTS.h4,
    color: COLORS.title,
    marginBottom: 10,
    textAlign: "center",
  },
  subtitle: {
    ...FONTS.fontRegular,
    fontSize: 16,
    color: COLORS.text,
    marginBottom: 40,
    textAlign: "center",
  },
  loginButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    paddingVertical: 15,
    paddingHorizontal: 20,
    width: "100%",
    marginBottom: 20,
  },
  loginButtonText: {
    ...FONTS.fontMedium,
    fontSize: 16,
    color: COLORS.white,
    marginLeft: 10,
  },
  shopifyLogo: {
    width: 24,
    height: 24,
    tintColor: COLORS.white,
  },
});

export default ShopifyLogin;
