import React, { useState, useRef, useEffect } from "react";
import WebView from "react-native-webview";
import { Platform, Alert, View, Text } from "react-native";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation, NavigationProp, CommonActions } from "@react-navigation/native";
import <PERSON>ieManager from '@react-native-cookies/cookies';
import { ActivityIndicator } from "react-native-paper";
import { COLORS, FONTS } from "../../constants/theme";
import { clearCart } from "@/app/redux/reducer/cartReducer";
import { useDispatch } from "react-redux";

// Navigation type definition (matching ShopifyWebView)
type RootStackParamList = {
  CompanyRegistration: undefined;
  DrawerNavigation: { screen?: string };
};

interface ShopifyLoginIOSProps {
  onLoginSuccess?: (userData: any) => void;
}

export default function ShopifyLoginIOS({ onLoginSuccess }: ShopifyLoginIOSProps = {}) {
  const uri = "https://sunrise-trade.myshopify.com/account/login";
  const SHOPIFY_ADMIN_TOKEN = "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";
  const SHOPIFY_ADMIN_URL =
    "https://sunrise-trade.myshopify.com/admin/api/2025-01/graphql.json";

  // Navigation setup (matching ShopifyWebView)
  const navigation = useNavigation();
  const webViewRef = useRef<WebView>(null);

  // CRITICAL: ShopifyWebView state management
  const [loading, setLoading] = useState(false);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const hasLoggedInRef = useRef(false);
  const isNavigatingRef = useRef(false);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hasDispatchedNavigationRef = useRef(false);

  // Test: Complete Shopify Authentication Flow (Production-Ready)
  const [authTestEnabled, setAuthTestEnabled] = useState(true);
  const [authenticationState, setAuthenticationState] = useState({
    isInProgress: false,
    isComplete: false,
    hasTokens: false,
    hasCustomerData: false,
    error: null as string | null
  });

  // Cross-platform navigation tracking (iOS fix)
  const navigationCountRef = useRef(0);
  const hasNavigatedRef = useRef(false);

  // CRITICAL: Navigation source tracking to prevent auto-navigation after logout
  const [navigationSource, setNavigationSource] = useState<'startup' | 'logout' | 'manual'>('startup');
  const isFromLogoutRef = useRef(false);
  const [webViewKey, setWebViewKey] = useState(0); // Force WebView recreation
  const [shouldClearWebViewData, setShouldClearWebViewData] = useState(false);
  const dispatch = useDispatch();
  // TESTING HELPER: Function to manually set verification status for testing
  const setManualVerificationStatus = async (isVerified: boolean) => {
    console.log(`🧪 TESTING: Setting manual verification override to ${isVerified}`);
    await AsyncStorage.setItem("MANUAL_VERIFICATION_OVERRIDE", isVerified ? "true" : "false");
    await AsyncStorage.setItem("isCompanyVerified", isVerified ? "true" : "false");
    console.log(`🧪 TESTING: Manual verification status set to ${isVerified}`);
  };

  // TESTING HELPER: Function to clear manual verification override
  const clearManualVerificationOverride = async () => {
    console.log("🧪 TESTING: Clearing manual verification override");
    await AsyncStorage.removeItem("MANUAL_VERIFICATION_OVERRIDE");
    console.log("🧪 TESTING: Manual verification override cleared");
  };

  // SHOPIFY ADMIN API VERIFICATION: Function that implements the exact same logic as ShopifyWebView.tsx
  const checkVerificationStatus = async () => {
    try {
      const storedCustomerData = await AsyncStorage.getItem("customerData");
      const customerData = JSON.parse(storedCustomerData || "{}");
      const customerId = customerData?.data?.customer?.id;

      if (!customerId) {
        console.warn("Customer ID not found in AsyncStorage");
        return;
      }

      const query = `
            query {
              customer(id: "${customerId}") {
                metafield(namespace: "custom", key: "verified") {
                  value
                }
              }
            }
          `;

      const response = await fetch(SHOPIFY_ADMIN_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": SHOPIFY_ADMIN_TOKEN,
        },
        body: JSON.stringify({ query }),
      });

      const result = await response.json();
      const verifiedStatus = result?.data?.customer?.metafield?.value;

      console.log("Verification status:", verifiedStatus);
      const isVerified = verifiedStatus === "true";
      console.log("isVerified:", isVerified);

      // Store verification status in AsyncStorage

      if (
        verifiedStatus == undefined ||
        verifiedStatus == null ||
        verifiedStatus == ""
      ) {
        await AsyncStorage.setItem("isCompanyVerified", "false");
      } else {
        await AsyncStorage.setItem("isCompanyVerified", "true");
      }

      // Return the verification status instead of navigating directly
      return isVerified;
    } catch (error) {
      console.error("Verification check error:", error);
      // Default to false (unverified) if verification check fails
      await AsyncStorage.setItem("isCompanyVerified", "false");
      return false;
    }
  };

  // CRITICAL: Aggressive WebView data clearing script
  const clearWebViewDataScript = `
    (function() {
      console.log('🧹 AGGRESSIVE WebView data clearing started...');

      try {
        // Clear all localStorage
        if (typeof localStorage !== 'undefined') {
          const localStorageKeys = [];
          for (let i = 0; i < localStorage.length; i++) {
            localStorageKeys.push(localStorage.key(i));
          }
          localStorageKeys.forEach(key => {
            if (key) {
              localStorage.removeItem(key);
              console.log('🗑️ Cleared localStorage key:', key);
            }
          });
          localStorage.clear();
          console.log('✅ localStorage completely cleared');
        }

        // Clear all sessionStorage
        if (typeof sessionStorage !== 'undefined') {
          const sessionStorageKeys = [];
          for (let i = 0; i < sessionStorage.length; i++) {
            sessionStorageKeys.push(sessionStorage.key(i));
          }
          sessionStorageKeys.forEach(key => {
            if (key) {
              sessionStorage.removeItem(key);
              console.log('🗑️ Cleared sessionStorage key:', key);
            }
          });
          sessionStorage.clear();
          console.log('✅ sessionStorage completely cleared');
        }

        // Clear all cookies by setting them to expire
        if (typeof document !== 'undefined' && document.cookie) {
          const cookies = document.cookie.split(';');
          cookies.forEach(cookie => {
            const eqPos = cookie.indexOf('=');
            const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
            if (name) {
              // Clear for current domain
              document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
              document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=' + window.location.hostname;
              document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.' + window.location.hostname;
              // Clear for Shopify domain
              document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.myshopify.com';
              document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.shopify.com';
              console.log('🍪 Cleared cookie:', name);
            }
          });
          console.log('✅ All cookies cleared');
        }

        console.log('🎉 AGGRESSIVE WebView data clearing completed!');

        // Notify React Native that clearing is complete
        if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'WEBVIEW_DATA_CLEARED',
            success: true,
            timestamp: new Date().toISOString()
          }));
        }

      } catch (error) {
        console.error('❌ WebView data clearing failed:', error);
        if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'WEBVIEW_DATA_CLEARED',
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
          }));
        }
      }
    })();
    true;
  `;

  // CRITICAL: Detect if component is loaded after logout
  useEffect(() => {
    const checkLogoutFlag = async () => {
      console.log("🔍 Checking for JUST_LOGGED_OUT flag...");
      const logoutFlag = await AsyncStorage.getItem("JUST_LOGGED_OUT");
      console.log("🔍 JUST_LOGGED_OUT flag value:", logoutFlag);

      if (logoutFlag === "true") {
        console.log("🚪 Detected post-logout navigation - enabling aggressive data clearing");
        console.log("🚪 Setting navigation source to 'logout'");
        setNavigationSource('logout');
        isFromLogoutRef.current = true;
        setShouldClearWebViewData(true);
        dispatch(clearCart());
        // CRITICAL: Reset navigation flags to prevent any cached navigation
        console.log("🚪 Resetting all navigation flags");
        hasDispatchedNavigationRef.current = false;
        hasNavigatedRef.current = false;
        navigationCountRef.current = 0;

        // Clear the logout flag and any remaining auth data
        console.log("🚪 Clearing JUST_LOGGED_OUT flag");
        await AsyncStorage.removeItem("JUST_LOGGED_OUT");

        // Force WebView recreation by changing key
        console.log("🚪 Forcing WebView recreation");
        setWebViewKey(prev => prev + 1);

        // Clear iOS cookies to prevent auto-authentication
        if (Platform.OS === 'ios') {
          try {
            await CookieManager.clearAll(true);
            await AsyncStorage.clear();
            webViewRef.current?.injectJavaScript(`
              localStorage.clear();
              sessionStorage.clear();
              document.cookie.split(';').forEach(c => {
                document.cookie = c.trim().split('=')[0] + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
              });
              true;
            `);
            console.log("🍪 iOS cookies cleared after logout");
          } catch (error) {
            console.warn("⚠️ Failed to clear iOS cookies:", error);
          }
        }

        // AGGRESSIVE: Inject clearing script immediately after a short delay
        setTimeout(() => {
          if (webViewRef.current) {
            console.log("🧹 IMMEDIATE: Injecting aggressive data clearing script...");
            webViewRef.current.injectJavaScript(clearWebViewDataScript);
          }
        }, 1000); // Wait 1 second for WebView to be ready
      } else {
        console.log("🔄 Normal app startup - auto-login enabled");
        setNavigationSource('startup');
        isFromLogoutRef.current = false;
        setShouldClearWebViewData(false);
      }
    };

    checkLogoutFlag();
  }, []);

  // TESTING: Log instructions for manual testing
  console.log("🧪 TESTING INSTRUCTIONS (SHOPIFY ADMIN API VERIFICATION):");
  console.log("🧪 To test VERIFIED user: setManualVerificationStatus(true)");
  console.log("🧪 To test UNVERIFIED user: setManualVerificationStatus(false)");
  console.log("🧪 To clear override: clearManualVerificationOverride()");
  console.log("🧪 These functions are available in the component scope");
  console.log("🧪 NOTE: Now using Shopify Admin API verification (matching ShopifyWebView.tsx)");

  // CRITICAL: Bootstrap auto-login check (from ShopifyWebView) - Modified to respect logout state
  useEffect(() => {
    const bootstrap = async () => {
      // If we've already navigated, don't do anything
      if (hasNavigatedRef.current) {
        return;
      }

      // CRITICAL: Skip auto-login if user just logged out
      if (isFromLogoutRef.current || navigationSource === 'logout') {
        console.log("🚪 Skipping bootstrap auto-login - user just logged out");
        console.log("👁️ Showing login page for manual authentication");
        return;
      }

      const token = await AsyncStorage.getItem("customerToken");
      const customerData = await AsyncStorage.getItem("customerData");

      console.log("🔍 Bootstrap check:");
      console.log("  Token:", token ? "found" : "not found");
      console.log("  Customer data:", customerData ? "found" : "not found");
      console.log("  Navigation source:", navigationSource);

      if (token && customerData) {
        console.log("🎯 Existing authentication found, checking verification...");
        hasNavigatedRef.current = true;

        // SHOPIFY ADMIN API VERIFICATION: Check verification status using Shopify Admin API (matching ShopifyWebView.tsx)
        const isVerified = await checkVerificationStatus();
        console.log("✅ SHOPIFY ADMIN API VERIFICATION: Bootstrap verification status:", isVerified);

        // ENHANCED NAVIGATION TRACKING: Dispatch navigation based on verification status
        const bootstrapTimestamp = new Date().toISOString();
        console.log("🧭 WEBVIEW TEST BOOTSTRAP NAVIGATION DECISION:", {
          timestamp: bootstrapTimestamp,
          isVerified,
          decision: isVerified ? "DrawerNavigation" : "CompanyRegistration",
          source: "WebViewTest bootstrap auto-login"
        });

        if (isVerified) {
          console.log("🧭 WebViewTest Bootstrap: Navigating to DrawerNavigation -> Home (VERIFIED USER)");
          console.log("🏠 SHOPIFY ADMIN API VERIFICATION: Bootstrap user is verified, dispatching to DrawerNavigation -> Home");
          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [
                { name: "DrawerNavigation", params: { screen: "Home" } },
              ],
            })
          );
          console.log("✅ WebViewTest Bootstrap: Navigation dispatch completed to DrawerNavigation at", bootstrapTimestamp);
        } else {
          console.log("🧭 WebViewTest Bootstrap: Navigating to CompanyRegistration (UNVERIFIED USER)");
          console.log("📝 SHOPIFY ADMIN API VERIFICATION: Bootstrap user is not verified, dispatching to CompanyRegistration");
          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [{ name: "CompanyRegistration" }],
            })
          );
          console.log("✅ WebViewTest Bootstrap: Navigation dispatch completed to CompanyRegistration at", bootstrapTimestamp);
        }
      } else {
        console.log("👁️ No existing login found, showing login page");
      }
    };

    bootstrap();
  }, [navigation, navigationSource]);

  // CRITICAL: Token formatting function (from ShopifyWebView)
  const formatToken = (token: string): string => {
    // Remove any existing prefix
    const cleanToken = token.replace(/^shcat_/, "");
    // Add the prefix
    return `shcat_${cleanToken}`;
  };

  // CRITICAL: Navigation state handler (matching ShopifyWebView loader logic)
  const handleNavigationStateChange = (navState: any) => {
    if (!navState.url) {
      return;
    }

    console.log("🌐 Navigated to:", navState.url);

    // Check for OTP form submission and subsequent steps - this happens after email is entered
    // Only set loading and navigating flags when OTP is actually submitted or we're in post-OTP flow
    if (
      (navState.navigationType === "formsubmit" &&
        navState.url.includes("/authentication/") &&
        navState.url.includes("/verify")) ||
      (navState.url.includes("/authentication/") &&
        navState.url.includes("/verify") &&
        navState.loading) ||
      (navState.url.includes("/authentication/") &&
        navState.url.includes("/verify") &&
        isNavigatingRef.current) ||
      (navState.url.includes("/authentication/") &&
        navState.url.includes("/oauth/authorize")) || // Redirect step after OTP
      (navState.url.includes("/account/callback") &&
        navState.url.includes("code=")) // Final OAuth redirect
    ) {
      console.log(
        "🔑 OTP submitted/loading, showing loader immediately..."
      );

      // Set a flag to indicate we're expecting a profile redirect
      isNavigatingRef.current = true;

      // Set loading to true to show the loader and hide the WebView
      setLoading(true);

      // OPTIONAL: hide WebView immediately
      webViewRef.current?.injectJavaScript(
        `document.body.style.display = 'none'; true;`
      );
    }

    // For login and authentication pages (but not OTP verification), make sure they're visible
    if (
      (navState.url.includes("/login") || navState.url.includes("/authentication")) &&
      !navState.url.includes("/verify") && // not OTP
      !navState.url.includes("/oauth/authorize") && // not after OTP
      !navState.url.includes("/account/callback") // not final redirect
    ) {
      console.log("👁️ Showing login/auth page:", navState.url);
      setLoading(false);
      isNavigatingRef.current = false;
    }

    // Check if the URL is the profile page or contains account - hide it but allow it to load in background
    // But exclude login and authentication pages
    if (
      (navState.url.includes("/account/profile") ||
        navState.url.includes("/account/orders") ||
        navState.url.includes("/account?") ||
        navState.url.includes("/account/") ||
        (navState.url.includes("/account") &&
          navState.loading &&
          isNavigatingRef.current)) &&
      !navState.url.includes("/login") &&
      !navState.url.includes("/authentication")
    ) {
      // If we're already handling login, don't process this again
      if (hasLoggedInRef.current) {
        console.log("🛑 Already handling login, ignoring account page");
        return;
      }

      console.log(
        "🔒 Hiding profile page but allowing it to load in background:",
        navState.url
      );

      // Immediately show loading and hide WebView from user
      setLoading(true);
      // Set navigating flag to true to maintain loading state
      isNavigatingRef.current = true;

      // Don't stop the page from loading - we need it to load to get the access token
      // But keep it hidden from the user with the loading overlay

      // Immediately inject JavaScript to get the necessary data
      webViewRef.current?.injectJavaScript(`
        (function() {
          function checkLoginData() {
            try {
              const rawCookies = document.cookie.split(';');
              const cookies = rawCookies.reduce((acc, cookie) => {
                const [name, ...rest] = cookie.trim().split('=');
                acc[name] = decodeURIComponent(rest.join('='));
                return acc;
              }, {});

              let accessToken = null;
              let accountNumber = null;

              // Check localStorage for customer account token
              for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key?.includes('__CUSTOMER_ACCOUNT_EXCHANGE_TOKEN__')) {
                  const item = localStorage.getItem(key);
                  try {
                    const parsed = JSON.parse(item);
                    if (parsed?.accessToken) {
                      accessToken = parsed.accessToken;
                      console.log('🔑 Found JWT access token in localStorage:', key);
                    }
                  } catch (parseError) {
                    console.error('❌ Error parsing token item:', parseError);
                  }
                }
              }

              // Extract account number from cookies
              if (cookies['customer_account_new_login']) {
                accountNumber = cookies['customer_account_new_login'];
                console.log('🔢 Found account number in cookies:', accountNumber);
              }

              // Check if user is logged in
              const isLoggedIn = !!(accessToken || accountNumber || cookies['customer_account_new_login']);

              console.log('🔍 Login data extraction results:');
              console.log('  accessToken:', accessToken ? 'found' : 'not found');
              console.log('  accountNumber:', accountNumber || 'not found');
              console.log('  isLoggedIn:', isLoggedIn);

              // If we have the necessary data, send it immediately
              if (isLoggedIn && (accessToken || accountNumber)) {
                console.log("🔑 Found login data, sending to app...");
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  isLoggedIn,
                  url: window.location.href,
                  session: cookies['__session'] || null,
                  shopifyEssential: cookies['_shopify_essential'] || null,
                  shopifyS: cookies['_shopify_s'] || null,
                  shopifyY: cookies['_shopify_y'] || cookies['_y'] || null,
                  masterDeviceId: cookies['master_device_id'] || null,
                  cfClearance: cookies['cf_clearance'] || null,
                  accessToken,
                  accountNumber,
                  customerData: null // Will be fetched via GraphQL
                }));
              } else {
                // If data is not ready, retry
                console.log("🔄 Login data not found, retrying...");
                setTimeout(checkLoginData, 100);
              }
            } catch (error) {
              console.error('❌ checkLoginData error:', error);
              window.ReactNativeWebView.postMessage(JSON.stringify({
                error: 'JWT extraction failed',
                message: error.message
              }));
            }
          }

          // Start checking for login data
          checkLoginData();
        })();
        true;
      `);
    }
  };

  // CRITICAL: Navigation control function (from ShopifyWebView)
  const handleShouldStartLoad = (request: any) => {
    const url = request.url || "";
    console.log("🔍 handleShouldStartLoad:", url);

    // If we're already logged in, block all navigation
    if (hasLoggedInRef.current) {
      console.log("🛑 Already logged in, blocking navigation to:", url);
      return false;
    }

    // For login and authentication pages, always allow and make sure they're visible
    // But if we're in the middle of OTP verification, keep the loader visible
    if (
      (url.includes("/login") || url.includes("/authentication")) &&
      !(url.includes("otp") && isNavigatingRef.current)
    ) {
      console.log("👁️ Allowing login/auth page to load:", url);
      setLoading(false);
      isNavigatingRef.current = false;
      return true;
    }

    // For OTP pages when we're already in the verification process, keep the loader visible
    if (url.includes("otp") && isNavigatingRef.current) {
      console.log("🔒 Keeping loader visible for OTP verification:", url);
      setLoading(true);
      return true;
    }

    // For profile page or any account page after login form submission
    // But exclude the login page itself
    if (
      (url.includes("/account/profile") ||
        url.includes("/account/orders") ||
        url.includes("/account?") ||
        url.includes("/account/") ||
        (url.includes("/account") && isNavigatingRef.current)) &&
      !url.includes("/login") &&
      !url.includes("/authentication")
    ) {
      if (url.includes("/account/profile")) {
        console.log("🔒 Allowing profile page to load in background:", url);
      } else {
        console.log("🔑 Allowing account page to load in background:", url);
      }

      // Show loading screen immediately and set navigating flag
      setLoading(true);
      isNavigatingRef.current = true;

      // Extract data from the page after it loads
      setTimeout(() => {
        // Only inject if we're not already logged in
        if (!hasLoggedInRef.current && webViewRef.current) {
          console.log("🔑 Injecting authentication extraction script...");
          webViewRef.current.injectJavaScript(`
            (function() {
              try {
                let accessToken = null;
                for (let i = 0; i < localStorage.length; i++) {
                  const key = localStorage.key(i);
                  if (key?.includes('__CUSTOMER_ACCOUNT_EXCHANGE_TOKEN__')) {
                    const item = localStorage.getItem(key);
                    try {
                      const parsed = JSON.parse(item);
                      if (parsed?.accessToken) {
                        accessToken = parsed.accessToken;
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                          isLoggedIn: true,
                          accessToken: accessToken,
                          url: window.location.href
                        }));
                        return;
                      }
                    } catch (_) {}
                  }
                }

                // If no token found, try cookie-based detection
                const cookies = document.cookie.split(';').reduce((acc, cookie) => {
                  const [name, ...rest] = cookie.trim().split('=');
                  if (name) acc[name] = decodeURIComponent(rest.join('='));
                  return acc;
                }, {});

                if (cookies['customer_account_new_login']) {
                  window.ReactNativeWebView.postMessage(JSON.stringify({
                    isLoggedIn: true,
                    accountNumber: cookies['customer_account_new_login'],
                    url: window.location.href
                  }));
                }
              } catch (error) {
                console.error("Error extracting token:", error);
              }
            })();
            true;
          `);
        }
      }, 50); // Reduced timeout for faster response

      // Allow the profile page to load in the background but keep it hidden with the loading overlay
      return true;
    }

    // Detect form submission on OTP page only (not email form submission)
    if (
      (url.includes("otp") &&
        (request.navigationType === "formsubmit" ||
          request.navigationType === "other"))
    ) {
      console.log("🔑 OTP submitted, showing loader...");
      setLoading(true);
      // Set a flag to indicate we're expecting a profile redirect
      isNavigatingRef.current = true;
    }

    // Allow all other URLs to load
    return true;
  };

  // CRITICAL: Load start handler (matching ShopifyWebView)
  const handleLoadStart = (event: any) => {
    // For initial load, don't show loading indicator for login pages
    if (!initialLoadComplete) {
      // If it's a login or authentication page, don't show the loader
      if (
        event.nativeEvent &&
        event.nativeEvent.url &&
        (event.nativeEvent.url.includes("/login") ||
          event.nativeEvent.url.includes("/authentication"))
      ) {
        console.log("👁️ Initial load of login page, keeping it visible");
        setLoading(false);
        isNavigatingRef.current = false;
      } else {
        // For other pages during initial load, show loader
        setLoading(true);
      }
      return;
    }

    // Show loading for OTP submission or any account page
    // But only for account pages after login (not the login page itself)
    if (
      event.nativeEvent &&
      event.nativeEvent.url &&
      // For OTP pages, always show loader if we're in the verification process
      ((event.nativeEvent.url.includes("otp") && isNavigatingRef.current) ||
        // For account pages (but not login or authentication pages), show loader
        ((event.nativeEvent.url.includes("/account/profile") ||
          event.nativeEvent.url.includes("/account/orders") ||
          event.nativeEvent.url.includes("/account?") ||
          event.nativeEvent.url.includes("/account/") ||
          (event.nativeEvent.url.includes("/account") &&
            isNavigatingRef.current)) &&
          !event.nativeEvent.url.includes("/login") &&
          !event.nativeEvent.url.includes("/authentication")))
    ) {
      // For OTP, show loader
      if (event.nativeEvent.url.includes("otp")) {
        console.log("🔄 OTP page loading, showing loader...");
        setLoading(true);
      }
      // For account pages (not login), show loader and set navigating flag
      else if (
        !event.nativeEvent.url.includes("/login") &&
        !event.nativeEvent.url.includes("/authentication")
      ) {
        console.log("🔄 Account page loading, showing loader...");
        setLoading(true);
        isNavigatingRef.current = true;
      }
    }
  };

  // CRITICAL: Load end handler (matching ShopifyWebView)
  const handleLoadEnd = (event: any) => {
    // Clear any existing timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }

    // Don't hide loading indicator if we're in the middle of a login flow
    // or if we're on a profile/account page or if navigation has been dispatched
    if (isNavigatingRef.current || hasLoggedInRef.current || isLoggedIn || hasDispatchedNavigationRef.current) {
      console.log("🔒 Keeping loader visible during login flow or after navigation dispatch");

      // Still mark initial load as complete
      if (!initialLoadComplete) {
        setInitialLoadComplete(true);
      }
      return;
    }

    // Keep loader visible for account pages (but not login pages)
    if (
      event.nativeEvent &&
      event.nativeEvent.url &&
      (event.nativeEvent.url.includes("/account/profile") ||
        event.nativeEvent.url.includes("/account/orders") ||
        // event.nativeEvent.url.includes("/account?") ||
        event.nativeEvent.url.includes("/account/") ||
        (event.nativeEvent.url.includes("/account") &&
          !event.nativeEvent.url.includes("/account/login"))) &&
      !event.nativeEvent.url.includes("/login") &&
      !event.nativeEvent.url.includes("/authentication") &&
      !event.nativeEvent.url.includes("/authentication")
    ) {
      console.log(
        "🔒 Keeping loader visible for account page:",
        event.nativeEvent.url
      );
      isNavigatingRef.current = true;
      return;
    }

    // For login and authentication pages, make them visible
    if (
      event.nativeEvent &&
      event.nativeEvent.url &&
      (event.nativeEvent.url.includes("/login") ||
        event.nativeEvent.url.includes("/authentication"))
      ) {
      // If it's an OTP page and we're in the verification process, keep the loader visible
      if (event.nativeEvent.url.includes("otp") && isNavigatingRef.current) {
        console.log(
          "🔒 Keeping loader visible for OTP verification:",
          event.nativeEvent.url
        );
        setLoading(true);
        return;
      }

      console.log("👁️ Showing login/auth page:", event.nativeEvent.url);
      // Only set loading to false if we're not in the middle of a form submission
      if (!isNavigatingRef.current) {
        setLoading(false);
      }
      return;
    }

    // Set a timeout to prevent flickering if another load starts soon
    loadingTimeoutRef.current = setTimeout(() => {
      setLoading(false);
      if (!initialLoadComplete) {
        setInitialLoadComplete(true);
      }
    }, 300); // Small delay to prevent flickering
  };

  // CRITICAL: Enhanced GraphQL customer data fetching (matching ShopifyWebView exactly)
  const fetchCustomerData = async (accessToken: string) => {
    try {
      console.log("� Raw access token:", accessToken);

      // Format token with shcat_ prefix (matching ShopifyWebView)
      const formattedToken = formatToken(accessToken);
      console.log("🔑 Formatted token:", formattedToken);

      // Create request body (matching ShopifyWebView GraphQL query)
      const requestBody = {
        query: `
          {
            customer {
              emailAddress {
                emailAddress
              }
              id
              firstName
              lastName
            }
          }
        `,
        variables: {},
      };

      // Create headers with formatted token (matching ShopifyWebView)
      const headers = {
        "Content-Type": "application/json",
        Authorization: formattedToken,
        Origin: "https://sunrise-trade.myshopify.com",
        "User-Agent":
          "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
      };

      console.log("🔗 Making GraphQL request...");
      console.log("📤 Request Headers:", {
        "Authorization": formattedToken.substring(0, 20) + "...",
        "Content-Type": headers["Content-Type"],
        "Origin": headers.Origin
      });

      const response = await fetch(
        "https://shopify.com/***********/account/customer/api/2024-01/graphql",
        {
          method: "POST",
          headers,
          body: JSON.stringify(requestBody),
        }
      );

      console.log("📥 Response Status:", response.status);

      if (!response.ok) {
        throw new Error(`GraphQL request failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log("📊 GraphQL Response:", JSON.stringify(result, null, 2));

      if (result.errors) {
        throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
      }

      if (!result.data?.customer) {
        throw new Error("No customer data in GraphQL response");
      }

      // Format customer data to match ShopifyWebView format exactly
      const customerData = {
        data: {
          customer: {
            ...result.data.customer,
            formattedId: result.data.customer.id.replace("gid://shopify/Customer/", "")
          }
        }
      };

      console.log("👤 Customer Profile:", JSON.stringify(customerData, null, 2));
      return customerData;
    } catch (error) {
      console.error("❌ fetchCustomerData error:", error);
      throw error;
    }
  };



  // Production-Ready Shopify Authentication Script with All Cross-Platform Solutions
  const testShopifyAuthScript = `
    (function() {
      console.log('🚀 STARTING Production-Ready Shopify Authentication...');
      console.log('🚀 Script loaded at:', new Date().toISOString());
      console.log('🚀 Document ready state:', document.readyState);
      console.log('🚀 Platform: ${Platform.OS}');
      console.log('🚀 Current URL:', window.location.href);
      console.log('🚀 Page title:', document.title);

      try {
        console.log('🔐 Production Authentication Flow Starting...');
        console.log('🌐 URL:', window.location.href);
        console.log('📄 Title:', document.title);
        console.log('🔍 User agent:', navigator.userAgent);
        console.log('🔍 Domain:', document.domain);
        console.log('🔍 Protocol:', window.location.protocol);

        // Check ReactNativeWebView availability
        console.log('🔍 ReactNativeWebView available:', typeof window.ReactNativeWebView !== 'undefined');
        console.log('🔍 postMessage available:', typeof window.ReactNativeWebView?.postMessage !== 'undefined');

        // Production-Ready Cross-Platform Storage Functions
        const getStorageItem = (key) => {
          try {
            // Try localStorage first (iOS), fallback to sessionStorage (Android)
            return localStorage.getItem(key) || sessionStorage.getItem(key);
          } catch (error) {
            console.log('⚠️ localStorage failed, using sessionStorage for:', key);
            try {
              return sessionStorage.getItem(key);
            } catch (sessionError) {
              console.error('❌ Both storage methods failed for:', key);
              return null;
            }
          }
        };

        const setStorageItem = (key, value) => {
          let localSuccess = false;
          let sessionSuccess = false;

          // Try localStorage first (iOS persistent storage)
          try {
            localStorage.setItem(key, value);
            localSuccess = true;
            console.log('💾 localStorage.setItem() succeeded for:', key);
          } catch (error) {
            console.log('⚠️ localStorage.setItem() failed for:', key, error.message);
          }

          // Always try sessionStorage as fallback (Android compatibility)
          try {
            sessionStorage.setItem(key, value);
            sessionSuccess = true;
            console.log('🗂️ sessionStorage.setItem() succeeded for:', key);
          } catch (error) {
            console.error('❌ sessionStorage.setItem() failed for:', key, error.message);
          }

          return { localSuccess, sessionSuccess, hasStorage: localSuccess || sessionSuccess };
        };

        // Production AsyncStorage simulation for testing
        const simulateAsyncStorage = {
          setItem: async (key, value) => {
            try {
              const result = setStorageItem(key, value);
              console.log('📱 AsyncStorage.setItem simulated for:', key, result);
              return result;
            } catch (error) {
              console.error('❌ AsyncStorage.setItem simulation failed:', error);
              throw error;
            }
          },
          getItem: async (key) => {
            try {
              const value = getStorageItem(key);
              console.log('📱 AsyncStorage.getItem simulated for:', key, !!value);
              return value;
            } catch (error) {
              console.error('❌ AsyncStorage.getItem simulation failed:', error);
              return null;
            }
          }
        };

        // Cookie utility functions (from ShopifyWebView implementation)
        const getCookie = (name) => {
          try {
            const value = "; " + document.cookie;
            const parts = value.split("; " + name + "=");
            if (parts.length === 2) return parts.pop().split(";").shift();
            return null;
          } catch (error) {
            console.error('❌ getCookie error:', error.message);
            return null;
          }
        };

        const getAllCookies = () => {
          try {
            const rawCookies = document.cookie.split(';');
            const cookies = rawCookies.reduce((acc, cookie) => {
              const [name, ...rest] = cookie.trim().split('=');
              if (name) {
                acc[name] = decodeURIComponent(rest.join('='));
              }
              return acc;
            }, {});
            return cookies;
          } catch (error) {
            console.error('❌ getAllCookies error:', error.message);
            return {};
          }
        };

        // CRITICAL: JWT Token extraction (matching ShopifyWebView exactly)
        function checkLoginData() {
          try {
            const rawCookies = document.cookie.split(';');
            const cookies = rawCookies.reduce((acc, cookie) => {
              const [name, ...rest] = cookie.trim().split('=');
              acc[name] = decodeURIComponent(rest.join('='));
              return acc;
            }, {});

            let accessToken = null;
            let accountNumber = null;

            // Check localStorage for customer account token (ShopifyWebView pattern)
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key?.includes('__CUSTOMER_ACCOUNT_EXCHANGE_TOKEN__')) {
                const item = localStorage.getItem(key);
                try {
                  const parsed = JSON.parse(item);
                  if (parsed?.accessToken) {
                    accessToken = parsed.accessToken;
                    console.log('🔑 Found JWT access token in localStorage:', key);
                    console.log('🔑 Token preview:', accessToken.substring(0, 50) + '...');
                  }
                } catch (parseError) {
                  console.error('❌ Error parsing token item:', parseError.message);
                }
              }
            }

            // Extract account number from cookies
            if (cookies['customer_account_new_login']) {
              accountNumber = cookies['customer_account_new_login'];
              console.log('� Found account number in cookies:', accountNumber);
            }

            // Check if user is logged in (matching ShopifyWebView logic)
            const isLoggedIn = !!(accessToken || accountNumber || cookies['customer_account_new_login']);

            console.log('🔍 Login data extraction results:');
            console.log('  accessToken:', accessToken ? 'found (' + accessToken.substring(0, 20) + '...)' : 'not found');
            console.log('  accountNumber:', accountNumber || 'not found');
            console.log('  isLoggedIn:', isLoggedIn);

            // If we have the necessary data, send it immediately (matching ShopifyWebView)
            if (isLoggedIn && (accessToken || accountNumber)) {
              console.log("🔑 Found login data, sending to app...");
              window.ReactNativeWebView.postMessage(JSON.stringify({
                isLoggedIn,
                url: window.location.href,
                session: cookies['__session'] || null,
                shopifyEssential: cookies['_shopify_essential'] || null,
                shopifyS: cookies['_shopify_s'] || null,
                shopifyY: cookies['_shopify_y'] || cookies['_y'] || null,
                masterDeviceId: cookies['master_device_id'] || null,
                cfClearance: cookies['cf_clearance'] || null,
                accessToken,
                accountNumber,
                customerData: null // Will be fetched via GraphQL
              }));
            } else {
              // If data is not ready, retry immediately but with a limit
              console.log("🔄 Login data not found, retrying...");
              setTimeout(checkLoginData, 100);
            }
          } catch (error) {
            console.error('❌ checkLoginData error:', error.message);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              error: 'JWT extraction failed',
              message: error.message
            }));
          }
        }

        // Production-Ready Authentication Detection (Enhanced ShopifyWebView Format)
        const detectAuthenticationState = () => {
          try {
            console.log('🔐 Production Authentication Detection Starting...');

            // Extract cookies exactly like ShopifyWebView
            const rawCookies = document.cookie.split(';');
            const cookies = rawCookies.reduce((acc, cookie) => {
              const [name, ...rest] = cookie.trim().split('=');
              if (name) {
                acc[name] = decodeURIComponent(rest.join('='));
              }
              return acc;
            }, {});

            console.log('🍪 Extracted cookies:', Object.keys(cookies).length, 'cookies found');

            // Enhanced access token extraction with cross-platform storage
            let accessToken = null;
            let accountNumber = null;

            // Check localStorage first (iOS), then sessionStorage (Android)
            try {
              for (let i = 0; i < (localStorage?.length || 0); i++) {
                const key = localStorage.key(i);
                if (key?.includes('__CUSTOMER_ACCOUNT_EXCHANGE_TOKEN__')) {
                  const item = localStorage.getItem(key);
                  try {
                    const parsed = JSON.parse(item);
                    if (parsed?.accessToken) {
                      accessToken = parsed.accessToken;
                      accountNumber = parsed.accountNumber || null;
                      console.log('🔑 Found access token in localStorage:', key);
                      break;
                    }
                  } catch (parseError) {
                    console.log('⚠️ Failed to parse localStorage item:', key);
                  }
                }
              }

              // Fallback to sessionStorage if localStorage failed
              if (!accessToken) {
                for (let i = 0; i < (sessionStorage?.length || 0); i++) {
                  const key = sessionStorage.key(i);
                  if (key?.includes('__CUSTOMER_ACCOUNT_EXCHANGE_TOKEN__')) {
                    const item = sessionStorage.getItem(key);
                    try {
                      const parsed = JSON.parse(item);
                      if (parsed?.accessToken) {
                        accessToken = parsed.accessToken;
                        accountNumber = parsed.accountNumber || null;
                        console.log('🔑 Found access token in sessionStorage:', key);
                        break;
                      }
                    } catch (parseError) {
                      console.log('⚠️ Failed to parse sessionStorage item:', key);
                    }
                  }
                }
              }
            } catch (storageError) {
              console.log('⚠️ Storage access error:', storageError.message);
            }

            // Enhanced customer data extraction
            let customerData = null;
            try {
              // Try multiple storage locations
              const sources = ['customerData', 'customer', 'shopify_customer'];
              for (const source of sources) {
                const stored = getStorageItem(source);
                if (stored) {
                  try {
                    customerData = JSON.parse(stored);
                    if (customerData) {
                      console.log('👤 Found customer data in:', source);
                      break;
                    }
                  } catch (parseError) {
                    console.log('⚠️ Failed to parse customer data from:', source);
                  }
                }
              }
            } catch (customerError) {
              console.log('⚠️ Customer data extraction error:', customerError.message);
            }

            // Enhanced authentication status determination
            const hasValidToken = !!(accessToken && accessToken.length > 10);
            const hasSessionCookie = !!(cookies['__session'] || cookies['_shopify_s']);
            const hasAuthCookies = !!(cookies['customer_account_new_login'] || cookies['logged_in'] === 'true');
            const hasCustomerData = !!(customerData?.data?.customer || customerData?.customerId);

            // ENHANCED: Consider authenticated if we have auth cookies (even without access token)
            // This matches the ShopifyWebView behavior where cookies indicate successful authentication
            const isLoggedIn = hasValidToken || hasSessionCookie || hasAuthCookies;

            console.log('🔐 Authentication Analysis:');
            console.log('  🔑 Valid Token:', hasValidToken);
            console.log('  🍪 Session Cookie:', hasSessionCookie);
            console.log('  🍪 Auth Cookies:', hasAuthCookies);
            console.log('  👤 Customer Data:', hasCustomerData);
            console.log('  ✅ Final Status:', isLoggedIn ? 'AUTHENTICATED' : 'NOT AUTHENTICATED');

            // Return enhanced ShopifyWebView format data
            return {
              // Core ShopifyWebView fields
              isLoggedIn,
              url: window.location.href,
              session: cookies['__session'] || null,
              shopifyEssential: cookies['_shopify_essential'] || null,
              shopifyS: cookies['_shopify_s'] || null,
              shopifyY: cookies['_shopify_y'] || cookies['_y'] || null,
              masterDeviceId: cookies['master_device_id'] || null,
              cfClearance: cookies['cf_clearance'] || null,
              accessToken,
              accountNumber,
              customerData,

              // Enhanced fields for production use
              title: document.title,
              allCookies: cookies,
              cookieCount: Object.keys(cookies).length,
              authenticationAnalysis: {
                hasValidToken,
                hasSessionCookie,
                hasAuthCookies,
                hasCustomerData,
                tokenLength: accessToken ? accessToken.length : 0,
                customerEmail: customerData?.data?.customer?.emailAddress?.emailAddress || null,
                customerId: customerData?.data?.customer?.id || cookies['customer_account_new_login'] || null
              },
              storageInfo: {
                localStorageAvailable: typeof localStorage !== 'undefined',
                sessionStorageAvailable: typeof sessionStorage !== 'undefined',
                localStorageItems: localStorage?.length || 0,
                sessionStorageItems: sessionStorage?.length || 0
              }
            };
          } catch (error) {
            console.error('❌ Production authentication detection error:', error.message);
            return {
              isLoggedIn: false,
              url: window.location.href,
              session: null,
              shopifyEssential: null,
              shopifyS: null,
              shopifyY: null,
              masterDeviceId: null,
              cfClearance: null,
              accessToken: null,
              accountNumber: null,
              customerData: null,
              title: document.title,
              error: error.message,
              allCookies: {},
              cookieCount: 0,
              authenticationAnalysis: {
                hasValidToken: false,
                hasSessionCookie: false,
                hasAuthCookies: false,
                hasCustomerData: false,
                tokenLength: 0,
                customerEmail: null,
                customerId: null
              },
              storageInfo: {
                localStorageAvailable: false,
                sessionStorageAvailable: false,
                localStorageItems: 0,
                sessionStorageItems: 0
              }
            };
          }
        };

        // Test variables for authentication flow
        let authState = null;
        let initialAuthState = null;
        let finalAuthState = null;
        let navigationCount = 0;
        let errors = [];

        // Test 1: Initial authentication state
        console.log('🧪 Test 1: Checking initial authentication state');
        try {
          initialAuthState = detectAuthenticationState();
          console.log('🔐 Initial auth state:', initialAuthState);

          // Track navigation
          navigationCount++;
          console.log('🧭 Navigation count:', navigationCount);

        } catch (error) {
          console.error('❌ Error checking initial auth state:', error.message);
          errors.push('Initial auth check: ' + error.message);
        }

        // Test 2: Cross-platform storage test
        console.log('🧪 Test 2: Testing cross-platform storage for tokens');
        try {
          const testTokenKey = '__CUSTOMER_ACCOUNT_EXCHANGE_TOKEN__test_${Platform.OS}';
          const testTokenValue = {
            accessToken: 'test_token_' + Date.now(),
            expiresAt: new Date(Date.now() + 3600000).toISOString(),
            platform: '${Platform.OS}'
          };

          console.log('💾 Testing token storage with key:', testTokenKey);
          const storageResult = setStorageItem(testTokenKey, JSON.stringify(testTokenValue));
          console.log('💾 Storage result:', storageResult);

          // Test retrieval
          const retrievedToken = getStorageItem(testTokenKey);
          console.log('💾 Retrieved token:', retrievedToken);

          let tokenParseSuccess = false;
          if (retrievedToken) {
            try {
              const parsed = JSON.parse(retrievedToken);
              tokenParseSuccess = parsed.accessToken === testTokenValue.accessToken;
              console.log('💾 Token parse success:', tokenParseSuccess);
            } catch (parseError) {
              console.error('❌ Token parse error:', parseError.message);
            }
          }

          // Clean up test token
          try {
            localStorage.removeItem(testTokenKey);
            sessionStorage.removeItem(testTokenKey);
          } catch (cleanupError) {
            console.log('⚠️ Cleanup warning:', cleanupError.message);
          }

        } catch (error) {
          console.error('❌ Storage test error:', error.message);
          errors.push('Storage test: ' + error.message);
        }

        // Test 3: Final authentication state check
        console.log('🧪 Test 3: Checking final authentication state');
        try {
          finalAuthState = detectAuthenticationState();
          console.log('🔐 Final auth state:', finalAuthState);

          // Compare initial vs final state
          const authChanged = initialAuthState?.isLoggedIn !== finalAuthState?.isLoggedIn;
          console.log('🔄 Authentication state changed:', authChanged);

          if (authChanged) {
            console.log('� Authentication flow detected!');
            console.log('🔐 Login status changed from', initialAuthState?.isLoggedIn, 'to', finalAuthState?.isLoggedIn);
          }

        } catch (error) {
          console.error('❌ Error checking final auth state:', error.message);
          errors.push('Final auth check: ' + error.message);
        }

        // Test 4: URL analysis for authentication flow
        console.log('🧪 Test 4: Analyzing URL for authentication patterns');
        try {
          const currentUrl = window.location.href;
          const urlPatterns = {
            isLoginPage: currentUrl.includes('/account/login'),
            isAuthPage: currentUrl.includes('/authentication/'),
            isCallbackPage: currentUrl.includes('/callback'),
            isAccountPage: currentUrl.includes('/account'),
            hasAuthParams: currentUrl.includes('client_id') || currentUrl.includes('access_token'),
            hasShopifyDomain: currentUrl.includes('shopify.com') || currentUrl.includes('myshopify.com')
          };

          console.log('🌐 URL patterns detected:', urlPatterns);

          // Determine authentication flow stage
          let authFlowStage = 'unknown';
          if (urlPatterns.isLoginPage) authFlowStage = 'login_page';
          else if (urlPatterns.isAuthPage) authFlowStage = 'authentication';
          else if (urlPatterns.isCallbackPage) authFlowStage = 'callback';
          else if (urlPatterns.isAccountPage) authFlowStage = 'account_page';

          console.log('🔐 Authentication flow stage:', authFlowStage);

        } catch (error) {
          console.error('❌ URL analysis error:', error.message);
          errors.push('URL analysis: ' + error.message);
        }

        // Enhanced message sending with extensive debugging
        function sendTestMessage() {
          console.log('📤 sendTestMessage called');
          console.log('📤 Checking ReactNativeWebView availability...');
          console.log('📤 window.ReactNativeWebView exists:', typeof window.ReactNativeWebView !== 'undefined');
          console.log('📤 postMessage exists:', typeof window.ReactNativeWebView?.postMessage !== 'undefined');

          if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
            try {
              console.log('📤 Building authentication test message...');
              // Send message in EXACT ShopifyWebView format
              const shopifyMessage = {
                // Core ShopifyWebView data
                isLoggedIn: finalAuthState?.isLoggedIn || false,
                url: window.location.href,
                session: finalAuthState?.session || null,
                shopifyEssential: finalAuthState?.shopifyEssential || null,
                shopifyS: finalAuthState?.shopifyS || null,
                shopifyY: finalAuthState?.shopifyY || null,
                masterDeviceId: finalAuthState?.masterDeviceId || null,
                cfClearance: finalAuthState?.cfClearance || null,
                accessToken: finalAuthState?.accessToken || null,
                accountNumber: finalAuthState?.accountNumber || null,
                customerData: finalAuthState?.customerData || null
              };

              // Additional test data
              const testMessage = {
                type: 'SHOPIFY_AUTH_TEST',
                message: 'Shopify authentication test completed',
                // ShopifyWebView format data
                shopifyData: shopifyMessage,
                // Test-specific data
                initialAuthState: initialAuthState,
                finalAuthState: finalAuthState,
                authStateChanged: initialAuthState?.isLoggedIn !== finalAuthState?.isLoggedIn,
                navigationCount: navigationCount,
                urlPatterns: {
                  isLoginPage: window.location.href.includes('/account/login'),
                  isAuthPage: window.location.href.includes('/authentication/'),
                  isCallbackPage: window.location.href.includes('/callback'),
                  isAccountPage: window.location.href.includes('/account'),
                  hasAuthParams: window.location.href.includes('client_id') || window.location.href.includes('access_token'),
                  hasShopifyDomain: window.location.href.includes('shopify.com') || window.location.href.includes('myshopify.com')
                },
                crossPlatformStorage: {
                  localStorageAvailable: typeof localStorage !== 'undefined',
                  sessionStorageAvailable: typeof sessionStorage !== 'undefined',
                  storageItemCount: (localStorage?.length || 0) + (sessionStorage?.length || 0)
                },
                errors: errors,
                platform: '${Platform.OS}',
                timestamp: new Date().toISOString(),
                debugInfo: {
                  scriptExecuted: true,
                  messageAttempt: true,
                  bridgeAvailable: true
                }
              };

              console.log('📤 ShopifyWebView format data:', shopifyMessage);
              console.log('📤 Test message object built:', testMessage);
              console.log('📤 Converting to JSON...');
              const jsonMessage = JSON.stringify(testMessage);
              console.log('📤 JSON conversion successful, length:', jsonMessage.length);
              console.log('📤 Calling postMessage...');
              window.ReactNativeWebView.postMessage(jsonMessage);
              console.log('✅ Message sent successfully!');
            } catch (error) {
              console.error('❌ Error in sendTestMessage:', error);
              console.error('❌ Error stack:', error.stack);
              errors.push('Message sending: ' + error.message);
            }
          } else {
            console.log('⚠️ ReactNativeWebView not available');
            console.log('⚠️ window.ReactNativeWebView:', window.ReactNativeWebView);
            console.log('⚠️ Retrying in 100ms...');
            setTimeout(sendTestMessage, 100);
          }
        }

        // Send message with enhanced retry logic and debugging
        console.log('🚀 Starting message sending sequence...');
        console.log('🚀 Attempt 1: Immediate');
        sendTestMessage();

        console.log('🚀 Scheduling attempt 2: 500ms delay');
        setTimeout(() => {
          console.log('🚀 Attempt 2: 500ms delay');
          sendTestMessage();
        }, 500);

        console.log('🚀 Scheduling attempt 3: 1000ms delay');
        setTimeout(() => {
          console.log('🚀 Attempt 3: 1000ms delay');
          sendTestMessage();
        }, 1000);

        console.log('🚀 JavaScript execution completed successfully');
        return true;

      } catch (globalError) {
        console.error('💥 GLOBAL ERROR in JavaScript execution:', globalError);
        console.error('💥 Error message:', globalError.message);
        console.error('💥 Error stack:', globalError.stack);

        // Try to send error message
        try {
          if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'SHARED_COOKIES_TEST',
              message: 'JavaScript execution failed',
              error: globalError.message,
              stack: globalError.stack,
              platform: '${Platform.OS}',
              timestamp: new Date().toISOString(),
              debugInfo: {
                scriptExecuted: false,
                globalError: true
              }
            }));
          }
        } catch (errorSendingError) {
          console.error('💥 Failed to send error message:', errorSendingError);
        }

        return false;
      }
    })();
  `;

  // CRITICAL: Comprehensive AsyncStorage data persistence (matching ShopifyWebView)
  const storeComprehensiveAuthData = async (authData: any) => {
    try {
      console.log("📱 COMPREHENSIVE AsyncStorage data persistence starting...");

      // 1. OAuth/Authentication tokens (matching ShopifyWebView patterns)
      if (authData.accessToken) {
        console.log("🔑 Storing access_token:", authData.accessToken.substring(0, 20) + "...");
        await AsyncStorage.setItem("access_token", authData.accessToken);
        await AsyncStorage.setItem("customerToken", authData.accessToken); // ShopifyWebView compatibility
      }

      if (authData.refreshToken) {
        console.log("🔄 Storing refresh_token");
        await AsyncStorage.setItem("refresh_token", authData.refreshToken);
      }

      if (authData.idToken) {
        console.log("🆔 Storing id_token");
        await AsyncStorage.setItem("id_token", authData.idToken);
      }

      if (authData.expiresIn) {
        const expiresAt = Date.now() + authData.expiresIn * 1000;
        console.log("⏰ Storing token_expires_at:", new Date(expiresAt).toISOString());
        await AsyncStorage.setItem("token_expires_at", expiresAt.toString());
      }

      // 2. OAuth PKCE and security parameters (matching ShopifyWebView OAuth flow)
      if (authData.codeVerifier) {
        console.log("🔐 Storing code_verifier");
        await AsyncStorage.setItem("code_verifier", authData.codeVerifier);
      }

      if (authData.oauthState) {
        console.log("🛡️ Storing oauth_state");
        await AsyncStorage.setItem("oauth_state", authData.oauthState);
      }

      if (authData.oauthNonce) {
        console.log("🎲 Storing oauth_nonce");
        await AsyncStorage.setItem("oauth_nonce", authData.oauthNonce);
      }

      // 3. User/Customer data (matching ShopifyWebView format)
      if (authData.customerData) {
        console.log("👤 Storing customerData:", JSON.stringify(authData.customerData).substring(0, 100) + "...");
        await AsyncStorage.setItem("customerData", JSON.stringify(authData.customerData));

        // Extract and store customer email if available
        const customerEmail = authData.customerData?.data?.customer?.emailAddress?.emailAddress;
        if (customerEmail) {
          console.log("📧 Storing customerEmail:", customerEmail);
          await AsyncStorage.setItem("customerEmail", customerEmail);
        }
      }

      if (authData.accountNumber) {
        console.log("🔢 Storing accountNumber:", authData.accountNumber);
        await AsyncStorage.setItem("accountNumber", authData.accountNumber);
      }

      // 4. Application state (matching ShopifyWebView state management)
      if (authData.isLoggedIn !== undefined) {
        console.log("✅ Storing isLoggedIn:", authData.isLoggedIn);
        await AsyncStorage.setItem("isLoggedIn", authData.isLoggedIn ? "true" : "false");
      }

      if (authData.isVerified !== undefined) {
        console.log("🏢 Storing isCompanyVerified:", authData.isVerified);
        await AsyncStorage.setItem("isCompanyVerified", authData.isVerified ? "true" : "false");
      }

      if (authData.hasNavigated !== undefined) {
        console.log("🧭 Storing hasNavigated:", authData.hasNavigated);
        await AsyncStorage.setItem("hasNavigated", authData.hasNavigated ? "true" : "false");
      }

      // 5. Session and cookie data (matching ShopifyWebView session management)
      if (authData.session) {
        console.log("🍪 Storing session data");
        await AsyncStorage.setItem("session", authData.session);
      }

      if (authData.shopifyEssential) {
        await AsyncStorage.setItem("shopifyEssential", authData.shopifyEssential);
      }

      if (authData.shopifyS) {
        await AsyncStorage.setItem("shopifyS", authData.shopifyS);
      }

      if (authData.shopifyY) {
        await AsyncStorage.setItem("shopifyY", authData.shopifyY);
      }

      if (authData.masterDeviceId) {
        await AsyncStorage.setItem("masterDeviceId", authData.masterDeviceId);
      }

      if (authData.cfClearance) {
        await AsyncStorage.setItem("cfClearance", authData.cfClearance);
      }

      console.log("✅ COMPREHENSIVE AsyncStorage data persistence completed successfully!");

      // Verify stored data (debugging)
      const storedToken = await AsyncStorage.getItem("customerToken");
      const storedCustomerData = await AsyncStorage.getItem("customerData");
      const storedIsLoggedIn = await AsyncStorage.getItem("isLoggedIn");

      console.log("🔍 Verification - Stored data:");
      console.log("  customerToken:", storedToken ? "✅ stored" : "❌ missing");
      console.log("  customerData:", storedCustomerData ? "✅ stored" : "❌ missing");
      console.log("  isLoggedIn:", storedIsLoggedIn);

    } catch (error) {
      console.error("❌ COMPREHENSIVE AsyncStorage storage error:", error);
      throw error;
    }
  };

  const handleMessage = async (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log("📩 Received message:", data);

      // CRITICAL: Handle WebView data clearing completion
      if (data.type === 'WEBVIEW_DATA_CLEARED') {
        if (data.success) {
          console.log("✅ WebView data clearing completed successfully");
          setShouldClearWebViewData(false);
          // Re-enable storage after clearing
          setWebViewKey(prev => prev + 1);
        } else {
          console.error("❌ WebView data clearing failed:", data.error);
        }
        return;
      }

      // CRITICAL: Handle ShopifyWebView-style JWT authentication messages
      if (data.isLoggedIn && (data.accessToken || data.accountNumber)) {
        // SAFETY CHECK: Don't process login if user just logged out (but allow if user manually completed login)
        if ((isFromLogoutRef.current || navigationSource === 'logout') && !data.accessToken && !data.accountNumber) {
          console.log("🚪 Ignoring authentication data - user just logged out (no valid tokens)");
          return;
        }

        // If we have valid tokens, this is a successful manual login - reset logout state
        if (data.accessToken || data.accountNumber) {
          console.log("🔄 Valid tokens detected - resetting logout state for successful manual login");
          isFromLogoutRef.current = false;
          setNavigationSource('manual');
          setShouldClearWebViewData(false);
        }

        console.log("🎉 JWT AUTHENTICATION SUCCESS DETECTED!");
        console.log("🔑 Access Token Type:", data.accessToken ? (data.accessToken.startsWith('shcat_') ? 'JWT Token' : 'Raw Token') : 'None');
        console.log("🔑 Token Preview:", data.accessToken ? data.accessToken.substring(0, 50) + '...' : 'None');

        // Mark that we're handling navigation
        isNavigatingRef.current = true;
        setLoading(true);

        const userData = {
          session: data.session,
          shopifyEssential: data.shopifyEssential,
          shopifyS: data.shopifyS,
          shopifyY: data.shopifyY,
          masterDeviceId: data.masterDeviceId,
          cfClearance: data.cfClearance,
          url: data.url,
          accessToken: data.accessToken,
          customerData: data.customerData,
          accountNumber: data.accountNumber
        };

        console.log("📦 JWT User data received:", {
          ...userData,
          accessToken: userData.accessToken ? userData.accessToken.substring(0, 20) + "..." : null
        });

        try {
          let customerData = userData.customerData;

          // If we have a JWT access token, fetch complete customer data via GraphQL (matching ShopifyWebView)
          if (userData.accessToken && userData.accessToken.length > 50) {
            console.log("🔗 Fetching complete customer data via GraphQL...");
            try {
              customerData = await fetchCustomerData(userData.accessToken);
              console.log("✅ GraphQL customer data fetched successfully");

              // Store customer email if available (matching ShopifyWebView)
              if (customerData?.data?.customer?.emailAddress?.emailAddress) {
                const email = customerData.data.customer.emailAddress.emailAddress;
                console.log("📧 Customer email:", email);
                await AsyncStorage.setItem("customerEmail", email);
              }

              // Store account number if available (matching ShopifyWebView)
              if (customerData?.data?.customer?.formattedId) {
                const accountNumber = customerData.data.customer.formattedId;
                console.log("🔢 Account Number:", accountNumber);
                await AsyncStorage.setItem("accountNumber", accountNumber);
              }

            } catch (graphqlError) {
              console.error("❌ GraphQL fetch failed, using basic data:", graphqlError);
              // Fallback to basic customer data
              if (userData.accountNumber) {
                customerData = {
                  data: {
                    customer: {
                      formattedId: userData.accountNumber,
                      id: `gid://shopify/Customer/${userData.accountNumber}`
                    }
                  }
                };
              }
            }
          } else if (userData.accountNumber) {
            // Create basic customer data from account number
            customerData = {
              data: {
                customer: {
                  formattedId: userData.accountNumber,
                  id: `gid://shopify/Customer/${userData.accountNumber}`
                }
              }
            };
          }

          // COMPREHENSIVE AsyncStorage data persistence (matching ShopifyWebView)
          await storeComprehensiveAuthData({
            accessToken: userData.accessToken,
            customerData: customerData,
            accountNumber: userData.accountNumber,
            session: userData.session,
            shopifyEssential: userData.shopifyEssential,
            shopifyS: userData.shopifyS,
            shopifyY: userData.shopifyY,
            masterDeviceId: userData.masterDeviceId,
            cfClearance: userData.cfClearance,
            url: userData.url,
            isLoggedIn: true,
            hasNavigated: true
          });

          console.log("✅ JWT Login successful:", {
            accessToken: userData.accessToken ? userData.accessToken.substring(0, 20) + "..." : null,
            customerData: customerData ? "stored" : "none",
            url: userData.url
          });

          // Mark as logged in
          hasLoggedInRef.current = true;
          setIsLoggedIn(true);

          // CRITICAL: Call onLoginSuccess callback (matching ShopifyWebView)
          if (onLoginSuccess) {
            console.log("📞 Calling onLoginSuccess callback...");
            const callbackData = {
              token: userData.accessToken,
              customerData: customerData?.data?.customer,
              session: userData.session,
              shopifyEssential: userData.shopifyEssential,
              shopifyS: userData.shopifyS,
              shopifyY: userData.shopifyY,
              masterDeviceId: userData.masterDeviceId,
              cfClearance: userData.cfClearance,
              url: userData.url,
              accountNumber: userData.accountNumber
            };
            onLoginSuccess(callbackData);
            console.log("✅ onLoginSuccess callback completed");
          }

          // SHOPIFY ADMIN API VERIFICATION: Use Shopify Admin API (matching ShopifyWebView.tsx exactly)
          console.log("🔍 SHOPIFY ADMIN API VERIFICATION: Checking verification status using Shopify Admin API...");
          const isVerified = await checkVerificationStatus();
          console.log("✅ SHOPIFY ADMIN API VERIFICATION: User verification status:", isVerified);

          // Store verification status
          await storeComprehensiveAuthData({ isVerified });

          // ENHANCED NAVIGATION TRACKING: Dispatch navigation based on verification status
          const timestamp = new Date().toISOString();
          console.log("🧭 WEBVIEW TEST NAVIGATION DECISION:", {
            timestamp,
            isVerified,
            decision: isVerified ? "DrawerNavigation" : "CompanyRegistration",
            source: "WebViewTest authentication completion"
          });

          // Keep loader visible during navigation
          console.log("🔒 Keeping loader visible during navigation...");
          setLoading(true);
          isNavigatingRef.current = true;
          hasLoggedInRef.current = true;

          if (isVerified) {
            console.log("🧭 WebViewTest: Navigating to DrawerNavigation -> Home (VERIFIED USER)");
            console.log("🏠 SHOPIFY ADMIN API VERIFICATION: User is verified, dispatching to DrawerNavigation -> Home");
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [
                  { name: "DrawerNavigation", params: { screen: "Home" } },
                ],
              })
            );
            console.log("✅ WebViewTest: Navigation dispatch completed to DrawerNavigation at", timestamp);
          } else {
            console.log("🧭 WebViewTest: Navigating to CompanyRegistration (UNVERIFIED USER)");
            console.log("📝 SHOPIFY ADMIN API VERIFICATION: User is not verified, dispatching to CompanyRegistration");
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [{ name: "CompanyRegistration" }],
              })
            );
            console.log("✅ WebViewTest: Navigation dispatch completed to CompanyRegistration at", timestamp);
          }

          console.log("🎯 JWT Navigation dispatch completed successfully!");
          console.log("🔒 Loader will remain visible until component unmounts");

        } catch (processingError) {
          console.error("❌ Error processing JWT authentication data:", processingError);
          // Keep loader visible even during error - don't reset navigation state
          console.log("🔒 Keeping loader visible during error handling");
        }

        return;
      }

      if (data.type === 'SHOPIFY_AUTH_TEST' || data.type === 'SHOPIFY_AUTH_UPDATE' || data.type === 'SHOPIFY_AUTH_ERROR') {
        console.log("🧪 TEST MESSAGE - Shopify Authentication:", data);
        console.log("✅ Shopify Authentication test completed!");
        console.log("🌐 URL from JS:", data.url);
        console.log("📄 Title from JS:", data.title);
        console.log("💾 localStorage supported:", data.localStorageSupported);
        console.log("�️ sessionStorage supported:", data.sessionStorageSupported);
        console.log("🔄 Cross-platform strategy supported:", data.crossPlatformSupported);
        console.log("💾 localStorage data:", data.localStorageData);
        console.log("🗂️ sessionStorage data:", data.sessionStorageData);
        console.log("🔄 Cross-platform data:", data.crossPlatformData);
        console.log("🛠️ Strategy:", data.strategy);
        console.log("💡 Recommendation:", data.recommendation);
        console.log("❌ Errors:", data.errors);
        console.log("📱 Platform:", data.platform);
        console.log("⏰ Timestamp:", data.timestamp);
      }

      // SIMPLIFIED NAVIGATION DISPATCH - Trigger on authentication success OR account page detection
      const hasAuthCookies = data.finalAuthState?.authenticationAnalysis?.hasAuthCookies ||
        data.shopifyData?.authenticationAnalysis?.hasAuthCookies ||
        (data.finalAuthState?.allCookies?.customer_account_new_login);

      const isOnAccountPage = data.finalAuthState?.url?.includes('/account') ||
        data.shopifyData?.url?.includes('/account') ||
        data.urlPatterns?.isAccountPage;

      // DEBUG: Log authentication detection values
      console.log("🔍 NAVIGATION DISPATCH DEBUG:");
      console.log("  data.isLoggedIn:", data.isLoggedIn);
      console.log("  hasAuthCookies:", hasAuthCookies);
      console.log("  isOnAccountPage:", isOnAccountPage);
      console.log("  data.finalAuthState?.url:", data.finalAuthState?.url);
      console.log("  data.urlPatterns?.isAccountPage:", data.urlPatterns?.isAccountPage);
      console.log("  data.finalAuthState?.allCookies?.customer_account_new_login:", data.finalAuthState?.allCookies?.customer_account_new_login);

      // Trigger navigation dispatch if we have authentication indicators OR reached account page
      if (data.isLoggedIn || hasAuthCookies || data.accessToken || data.customerData || isOnAccountPage) {
        // CRITICAL SAFETY CHECK: Don't process authentication if user just logged out (unless they have valid tokens)
        console.log("🔍 AUTHENTICATION CHECK:");
        console.log("🔍   isFromLogoutRef.current:", isFromLogoutRef.current);
        console.log("🔍   navigationSource:", navigationSource);
        console.log("🔍   hasAuthCookies:", hasAuthCookies);
        console.log("🔍   data.accessToken:", data.accessToken ? "present" : "none");
        console.log("🔍   data.accountNumber:", data.accountNumber ? "present" : "none");
        console.log("🔍   isOnAccountPage:", isOnAccountPage);
        console.log("🔍   customer_account_new_login:", data.finalAuthState?.allCookies?.customer_account_new_login);

        // Block authentication ONLY if user just logged out AND doesn't have valid tokens AND isn't on account page
        const hasValidTokens = data.accessToken || data.accountNumber;
        const shouldBlock = (isFromLogoutRef.current || navigationSource === 'logout') && !hasValidTokens && !isOnAccountPage;

        if (shouldBlock) {
          console.log("🚪 BLOCKING authentication detection - user just logged out (no valid tokens)");
          console.log("🚪 Detected auth cookies but ignoring due to logout state");
          console.log("🚪 hasAuthCookies:", hasAuthCookies);
          console.log("🚪 customer_account_new_login:", data.finalAuthState?.allCookies?.customer_account_new_login);
          return;
        }

        // If we reach here and were in logout state, reset it (successful manual login)
        if ((isFromLogoutRef.current || navigationSource === 'logout') && (hasValidTokens || isOnAccountPage)) {
          console.log("🔄 Resetting logout state - successful manual login detected");
          console.log("🔄   hasValidTokens:", hasValidTokens);
          console.log("🔄   isOnAccountPage:", isOnAccountPage);
          isFromLogoutRef.current = false;
          setNavigationSource('manual');
          setShouldClearWebViewData(false);
        }

        console.log("🎉 AUTHENTICATION SUCCESS DETECTED!");
        console.log("📩 Authentication Data:", data);

        // Prevent duplicate navigation dispatch
        if (hasDispatchedNavigationRef.current) {
          console.log("🛑 Navigation already dispatched. Skipping...");
          return;
        }
        hasDispatchedNavigationRef.current = true;

        // Mark navigation state
        isNavigatingRef.current = true;
        hasNavigatedRef.current = true;

        // Handle authentication and navigation dispatch
        (async () => {
          try {
            // Extract customer ID from various sources
            const customerId = data.finalAuthState?.allCookies?.customer_account_new_login ||
              data.finalAuthState?.authenticationAnalysis?.customerId ||
              data.shopifyData?.customerId;

            // Create customer data if we have customer ID but no customer data
            let customerData = data.customerData || data.finalAuthState?.customerData || data.shopifyData?.customerData;

            if (!customerData && customerId) {
              console.log("🔧 Creating customer data from customer ID:", customerId);
              customerData = {
                data: {
                  customer: {
                    id: `gid://shopify/Customer/${customerId}`,
                    formattedId: customerId
                  }
                }
              };
              console.log("✅ Customer data created:", JSON.stringify(customerData));
            }

            // COMPREHENSIVE AsyncStorage data persistence (matching ShopifyWebView)
            const authDataToStore = {
              // Core authentication data
              accessToken: data.accessToken || data.finalAuthState?.accessToken || data.shopifyData?.accessToken,
              customerData: customerData,
              accountNumber: data.accountNumber || data.finalAuthState?.accountNumber || data.shopifyData?.accountNumber || customerId,

              // Session and cookie data
              session: data.session || data.finalAuthState?.session || data.shopifyData?.session,
              shopifyEssential: data.shopifyEssential || data.finalAuthState?.shopifyEssential || data.shopifyData?.shopifyEssential,
              shopifyS: data.shopifyS || data.finalAuthState?.shopifyS || data.shopifyData?.shopifyS,
              shopifyY: data.shopifyY || data.finalAuthState?.shopifyY || data.shopifyData?.shopifyY,
              masterDeviceId: data.masterDeviceId || data.finalAuthState?.masterDeviceId || data.shopifyData?.masterDeviceId,
              cfClearance: data.cfClearance || data.finalAuthState?.cfClearance || data.shopifyData?.cfClearance,

              // Application state
              isLoggedIn: true,
              hasNavigated: true
            };

            // If we have auth cookies but no explicit token, create a placeholder token
            if (!authDataToStore.accessToken && hasAuthCookies) {
              const customerId = data.finalAuthState?.authenticationAnalysis?.customerId ||
                data.shopifyData?.authenticationAnalysis?.customerId ||
                data.finalAuthState?.allCookies?.customer_account_new_login;

              if (customerId) {
                console.log("🔑 Creating customer ID token placeholder:", customerId);
                authDataToStore.accessToken = `customer_${customerId}`;

                // Create minimal customer data if not available
                if (!authDataToStore.customerData) {
                  authDataToStore.customerData = {
                    data: {
                      customer: {
                        formattedId: customerId,
                        id: `gid://shopify/Customer/${customerId}`
                      }
                    }
                  };
                }
              }
            }

            // Store all authentication data comprehensively
            await storeComprehensiveAuthData(authDataToStore);

            // Check verification status and dispatch navigation
            console.log("🔍 Checking verification status...");
            const isVerified = await checkVerificationStatus();
            console.log("✅ User verification status:", isVerified);

            // Keep loader visible during navigation
            console.log("🔒 Keeping loader visible during navigation...");
            setLoading(true);
            isNavigatingRef.current = true;
            hasLoggedInRef.current = true;

            // Dispatch navigation based on verification status (matching ShopifyWebView logic)
            if (isVerified) {
              console.log("🏠 User is verified, dispatching to DrawerNavigation -> Home");
              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [
                    { name: "DrawerNavigation", params: { screen: "Home" } },
                  ],
                })
              );
            } else {
              console.log("📝 User is not verified, dispatching to CompanyRegistration");
              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [{ name: "CompanyRegistration" }],
                })
              );
            }

            console.log("🎯 Navigation dispatch completed successfully!");
            console.log("🔒 Loader will remain visible until component unmounts");

          } catch (error) {
            console.error("❌ Error during authentication handling:", error);
            // Keep loader visible even during error navigation
            setLoading(true);
            isNavigatingRef.current = true;
            hasLoggedInRef.current = true;

            // Default to CompanyRegistration if verification check fails
            console.log("🔄 Error in verification check, defaulting to CompanyRegistration");
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [{ name: "CompanyRegistration" }],
              })
            );
            console.log("🔒 Loader will remain visible until component unmounts");
          }
        })();
      }
    } catch (error) {
      console.error("❌ Error parsing message:", error);
    }
  };

  // Production-Ready Authentication State Management
  const toggleAuthTest = () => {
    setAuthTestEnabled(!authTestEnabled);
    console.log(`🔄 Authentication Test ${!authTestEnabled ? 'ENABLED' : 'DISABLED'}`);
    console.log(`🔐 authTestEnabled prop set to: ${!authTestEnabled}`);
  };

  // COMPREHENSIVE Bootstrap useEffect for auto-login (matching ShopifyWebView)
  useEffect(() => {
    const bootstrap = async () => {
      // If we've already navigated, don't do anything
      if (hasNavigatedRef.current) {
        return;
      }

      console.log("🔍 COMPREHENSIVE Bootstrap check starting...");

      // Check all possible authentication tokens (matching ShopifyWebView patterns)
      const customerToken = await AsyncStorage.getItem("customerToken");
      const accessToken = await AsyncStorage.getItem("access_token");
      const refreshToken = await AsyncStorage.getItem("refresh_token");
      const idToken = await AsyncStorage.getItem("id_token");
      const customerData = await AsyncStorage.getItem("customerData");
      const isLoggedIn = await AsyncStorage.getItem("isLoggedIn");
      const hasNavigated = await AsyncStorage.getItem("hasNavigated");
      const accountNumber = await AsyncStorage.getItem("accountNumber");
      const customerEmail = await AsyncStorage.getItem("customerEmail");

      console.log("🔍 COMPREHENSIVE Bootstrap check results:");
      console.log("  customerToken:", customerToken ? "✅ found" : "❌ missing");
      console.log("  access_token:", accessToken ? "✅ found" : "❌ missing");
      console.log("  refresh_token:", refreshToken ? "✅ found" : "❌ missing");
      console.log("  id_token:", idToken ? "✅ found" : "❌ missing");
      console.log("  customerData:", customerData ? "✅ found" : "❌ missing");
      console.log("  isLoggedIn:", isLoggedIn);
      console.log("  hasNavigated:", hasNavigated);
      console.log("  accountNumber:", accountNumber ? "✅ found" : "❌ missing");
      console.log("  customerEmail:", customerEmail ? "✅ found" : "❌ missing");

      // Check if user is authenticated (any valid token + customer data)
      const hasValidToken = customerToken || accessToken || refreshToken;
      const hasValidCustomerData = customerData && customerData !== "{}";
      const isAuthenticated = hasValidToken && hasValidCustomerData && isLoggedIn === "true";

      console.log("🔍 Authentication status analysis:");
      console.log("  hasValidToken:", !!hasValidToken);
      console.log("  hasValidCustomerData:", !!hasValidCustomerData);
      console.log("  isAuthenticated:", isAuthenticated);

      if (isAuthenticated) {
        try {
          const parsedCustomer = JSON.parse(customerData);
          if (parsedCustomer?.data?.customer) {
            console.log("🔑 Found existing authentication, processing auto-login");
            console.log("👤 Customer ID:", parsedCustomer.data.customer.id);

            // Mark that we're already logged in
            hasNavigatedRef.current = true;

            // Update hasNavigated status comprehensively
            await storeComprehensiveAuthData({ hasNavigated: true });

            // Check verification status
            const isVerified = await checkVerificationStatus();
            console.log("🔁 Auto-login: user already authenticated. Verified?", isVerified);

            // Update verification status in comprehensive storage
            await storeComprehensiveAuthData({ isVerified });

            // Navigate to the appropriate screen
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [
                  {
                    name: isVerified ? "DrawerNavigation" : "CompanyRegistration",
                    ...(isVerified ? { params: { screen: "Home" } } : {}),
                  },
                ],
              })
            );

            console.log("🎯 COMPREHENSIVE Auto-login navigation dispatch completed!");
          }
        } catch (e) {
          console.error("❌ Error during comprehensive auto-login bootstrap:", e);
        }
      } else {
        console.log("👁️ No existing authentication found, showing login page");
        console.log("🔍 Missing authentication components:");
        if (!hasValidToken) console.log("  ❌ No valid authentication token");
        if (!hasValidCustomerData) console.log("  ❌ No valid customer data");
        if (isLoggedIn !== "true") console.log("  ❌ isLoggedIn not set to true");
      }
    };

    bootstrap();
  }, []);

  // Production initialization with comprehensive logging
  useEffect(() => {
    console.log('🚀 PRODUCTION-READY AUTHENTICATION SYSTEM INITIALIZED');
    console.log(`📱 Platform: ${Platform.OS}`);
    console.log(`🌐 Testing URL: ${uri}`);
    console.log(`🔐 Auth Test Enabled: ${authTestEnabled}`);
    console.log('✅ Cross-platform solutions active:');
    console.log('  🍎 iOS: Navigation handling (allow initial + 4 main, skip extras)');
    console.log('  🤖 Android: Natural navigation behavior');
    console.log('  🔄 JavaScript retry logic (immediate, 500ms, 1000ms)');
    console.log('  � Cross-platform storage (localStorage + sessionStorage fallback)');
    console.log('  📱 AsyncStorage integration (ShopifyWebView format)');
    console.log('  🧭 Enhanced navigation dispatch with error handling');

    // Auto-toggle after 5 seconds to test different states
    const timer = setTimeout(() => {
      console.log("🔄 Auto-toggling Authentication Test to test different state...");
      toggleAuthTest();
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  // Production error handling
  const handleWebViewError = (error) => {
    console.error('❌ WebView error:', error);
    setAuthenticationState(prev => ({
      ...prev,
      error: error.message
    }));
  };

  return (
    <View style={{ flex: 1 }}>
      <WebView
        ref={webViewRef}
        key={webViewKey} // Force recreation when user logs out
        source={{ uri }}
        injectedJavaScript={shouldClearWebViewData ? clearWebViewDataScript : testShopifyAuthScript}
        onMessage={handleMessage}

        // Production-Ready WebView Configuration
        javaScriptEnabled={true}
        domStorageEnabled={!shouldClearWebViewData} // Disable storage when clearing data
        sharedCookiesEnabled={!shouldClearWebViewData} // Disable shared cookies when clearing data
        thirdPartyCookiesEnabled={!shouldClearWebViewData} // Disable third-party cookies when clearing data
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}

        // Production-Ready Cross-Platform Navigation Handling (ULTRA-PERMISSIVE)
        onShouldStartLoadWithRequest={(request: any) => {
          console.log(`🧭 Navigation: ${request.url} (Platform: ${Platform.OS})`);

          if (Platform.OS === 'ios') {
            // iOS: Ultra-permissive strategy - only block truly problematic requests
            const requestCount = ++navigationCountRef.current;
            console.log(`🍎 iOS Navigation Request #${requestCount}: ${request.url}`);

            // Always allow initial request (prevents white screen)
            if (requestCount === 1) {
              console.log('🍎 iOS: Allowing initial request (prevents white screen)');
              return true;
            }

            // TEMPORARILY ALLOW EVERYTHING for debugging - let's see what's being blocked
            console.log('🍎 iOS: ALLOWING ALL REQUESTS (debug mode) - URL:', request.url);
            return true;
          } else {
            // Android: Natural behavior (no intervention needed)
            console.log('🤖 Android: Using natural navigation behavior');
            return true;
          }
        }}

        // Enhanced Navigation State Tracking with Authentication Detection
        onNavigationStateChange={handleNavigationStateChange}


        // Enhanced Load Event Handlers (matching ShopifyWebView loader behavior)
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}

        onError={(error) => {
          console.error('❌ WebView error:', error);
          setAuthenticationState(prev => ({
            ...prev,
            error: error.nativeEvent?.description || 'WebView error'
          }));
          // Don't hide loader if we're in the middle of navigation
          if (!isNavigatingRef.current && !hasLoggedInRef.current && !hasDispatchedNavigationRef.current) {
            setLoading(false);
          }
        }}

        onHttpError={(error) => {
          console.error('❌ HTTP error:', error);
          setAuthenticationState(prev => ({
            ...prev,
            error: `HTTP ${error.nativeEvent?.statusCode}: ${error.nativeEvent?.description}`
          }));
        }}

        // Production-Ready Styling
        style={{ flex: 1 }}

        // Enhanced User Agent for better compatibility
        userAgent={Platform.OS === 'ios'
          ? 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1'
          : 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36'
        }
      />

      {/* Loader Overlay (matching ShopifyWebView.tsx) */}
      {loading && (
        <View
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: COLORS.white,
            justifyContent: "center",
            alignItems: "center",
            zIndex: 1000,
          }}
        >
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text
            style={{
              marginTop: 20,
              fontSize: 16,
              color: COLORS.text,
              fontFamily: FONTS.fontMedium.fontFamily,
              textAlign: "center",
            }}
          >
            {hasDispatchedNavigationRef.current || hasLoggedInRef.current
              ? "Login successful! Redirecting..."
              : isNavigatingRef.current
                ? "Verifying login information..."
                : "Loading..."}
          </Text>
        </View>
      )}
    </View>
  );
}