import React, { useState, useEffect } from 'react';
import { useTheme } from '@react-navigation/native';
import { View, SafeAreaView, LayoutAnimation, Text, ActivityIndicator } from 'react-native';
import Header from '../../layout/Header';
import { Gesture<PERSON><PERSON>lerRootView, ScrollView } from 'react-native-gesture-handler';
import SwipeBox from '../../components/SwipeBox';
import { IMAGES } from '../../constants/Images';
import { GlobalStyleSheet } from '../../constants/StyleSheet';
import "react-native-gesture-handler";
import NotificationService from '../../services/NotificationService';
import { FONTS } from '../../constants/theme';
import messaging from '@react-native-firebase/messaging';

// Default notification images based on type
const getNotificationImage = (type) => {
    switch(type) {
        case 'promotion':
            return IMAGES.small5;
        case 'order':
            return IMAGES.small3;
        case 'product':
            return IMAGES.small6;
        case 'account':
            return IMAGES.small4;
        default:
            return IMAGES.small2;
    }
}

// Format date to readable format
const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
    });
}

const Notification = () => {
    const theme = useTheme();
    const { colors } : {colors : any } = theme;

    const [lists, setLists] = useState([]);
    const [loading, setLoading] = useState(true);

    // Load notifications from storage
    useEffect(() => {
        const loadNotifications = async () => {
            try {
                setLoading(true);
                const notifications = await NotificationService.getNotifications();

                // Transform notifications to match SwipeBox format
                const formattedNotifications = notifications.map(notification => ({
                    id: notification.id,
                    image: getNotificationImage(notification.data?.type || 'default'),
                    title: notification.title,
                    body: notification.body,
                    date: formatDate(notification.date),
                    read: notification.read,
                    data: notification.data
                }));

                setLists(formattedNotifications);
            } catch (error) {
                console.error('Error loading notifications:', error);
            } finally {
                setLoading(false);
            }
        };

        loadNotifications();

        // Set up a listener for new notifications using NotificationService event emitter
        const unsubscribe = NotificationService.onNotification(() => {
            // Refresh the notification list when a new notification arrives
            loadNotifications();
        });

        return unsubscribe;
    }, []);

    const deleteItem = async (index: any, id: string) => {
        LayoutAnimation.configureNext(LayoutAnimation.Presets.spring);

        // Delete from local state
        const arr = [...lists];
        arr.splice(index, 1);
        setLists(arr);

        // Delete from storage
        await NotificationService.deleteNotification(id);
    };

    const markAsRead = async (id: string) => {
        await NotificationService.markAsRead(id);

        // Update local state
        setLists(lists.map(item =>
            item.id === id ? {...item, read: true} : item
        ));
    };

    return (
        <SafeAreaView style={{ backgroundColor: colors.background, flex: 1 }}>
            <Header
                title={`Notifications (${lists.length})`}
                leftIcon={'back'}
                rightIcon2={'search'}
            />
            <GestureHandlerRootView style={{ flex: 1 }}>
                {loading ? (
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                        <ActivityIndicator size="large" color={colors.primary} />
                    </View>
                ) : lists.length === 0 ? (
                    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
                        <Text style={{ ...FONTS.fontMedium, fontSize: 18, color: colors.text, textAlign: 'center' }}>
                            No notifications yet
                        </Text>
                        <Text style={{ ...FONTS.fontRegular, fontSize: 14, color: colors.text, textAlign: 'center', marginTop: 10 }}>
                            When you receive notifications, they will appear here
                        </Text>
                    </View>
                ) : (
                    <ScrollView>
                        <View style={[GlobalStyleSheet.container, { padding: 0, paddingTop: 5, paddingBottom: 5 }]}>
                            {lists.map((data, index) => {
                                return (
                                    <View key={index}>
                                        <SwipeBox
                                            data={data}
                                            theme={theme}
                                            colors={colors}
                                            handleDelete={() => deleteItem(index, data.id)}
                                            onPress={() => markAsRead(data.id)}
                                        />
                                        <View
                                            style={{
                                                height: 1,
                                                width: '100%',
                                                backgroundColor: colors.border,
                                            }}
                                        />
                                    </View>
                                )
                            })}
                        </View>
                    </ScrollView>
                )}
            </GestureHandlerRootView>
        </SafeAreaView>
    )
}

export default Notification