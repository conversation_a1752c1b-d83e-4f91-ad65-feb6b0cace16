import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  Image,
  TouchableWithoutFeedback,
  Linking,
  TextInput,
  StyleSheet,
  Platform,
  useWindowDimensions,
} from "react-native";
import CustomAlert from "../../components/CustomAlert";
import { useCustomAlert } from "../../hooks/useCustomAlert";
import { useTheme } from "@react-navigation/native";
import {
  ButtonLabel,
  COLORS,
  FONTS,
  FONTWEIGHT,
  SIZES,
} from "../../constants/theme";
import { ScrollView } from "react-native-gesture-handler";
import Button from "../../components/Button/Button";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList } from "../../Navigations/RootStackParamList";
import Header from "../../layout/Header";
import CheckBox from "../../components/CheckBox/CheckBox";
import { useMutation, useQuery } from "@apollo/client";
import { GET_CHECKOUT_PRODUCTS } from "../../api/checkoutProductsQuery";
import { ActivityIndicator } from "react-native-paper";
import {
  CREATE_CART,
  REMOVE_LINES_FROM_CART,
  UPDATE_LINES_IN_CART,
} from "../../api/cartQuery";
import AsyncStorage from "@react-native-async-storage/async-storage";
import _ from "lodash";
import Close from "../../assets/icons/closeicon.png";
import AddIcon from "../../assets/icons/addicon.png";
import RemoveIcon from "../../assets/icons/removeIcon.png";
import { IMAGES } from "../../constants/Images";
import { useDispatch, useSelector } from "react-redux";
import { setCartId } from "../../redux/reducer/cartReducer";
import "react-native-gesture-handler";
import WebView from "react-native-webview";
import { COMPANY_LOCATION_QUERY } from "@/app/api/companyDetailsQuery";
import { DRAFT_ORDER_MUTATION } from "@/app/api/draftOrder";
import { LanguageState } from "@/app/redux/reducer/languageReducer";
import { staticTranslations } from "@/app/constants/staticTranslations";

const filterCartData = async (data: any) => {
  if (!data)
    return {
      totalAmount: 0,
      subtoalAmount: 0,
      cartId: "",
      checkoutUrl: "",
      variants: [],
    };
  let filteredVariants = [];
  if (Array.isArray(data.lines.edges) && data.lines.edges.length > 0) {
    filteredVariants = data.lines.edges.map((variant: any, index: number) => {
      // Debug log for each cart line
      console.log("[filterCartData] Cart line:", {
        lineId: variant.node.id,
        merchandiseId: variant.node?.merchandise?.id,
        imageUrl: variant.node?.merchandise?.image?.url,
        productImageUrl: variant.node?.merchandise?.product?.images?.edges[0]?.node?.url,
        title: variant.node?.merchandise?.title,
        productTitle: variant.node?.merchandise?.product?.title,
      });
      console.log("MyCart filterCartData - variant attributes:", variant.node?.attributes);
      console.log("MERCHANDISE OBJECT:", variant.node?.merchandise);

      const size =
        variant.node?.attributes?.find(
          (attr: any) => attr.key.toLowerCase() === "size"
        )?.value || "";

      console.log("MyCart filterCartData - extracted size:", size);

      // Clamp quantity to quantityAvailable
      const quantityAvailable = variant.node?.merchandise?.quantityAvailable;
      let quantity = variant.node?.quantity; // Always show user-added value

      const filteredVariant = {
        lineId: variant.node.id,
        handle: variant.node?.merchandise?.product?.handle,
        price: variant.node?.merchandise?.price,
        merchandiseId: variant.node?.merchandise?.id,
        merchandiseTitle: variant.node?.merchandise?.title,
        productTitle: variant.node?.merchandise?.product?.title,
        quantity: quantity,
        url: variant.node?.merchandise?.image?.url || variant.node?.merchandise?.product?.images?.edges[0]?.node?.url,
        size: size,
        quantityAvailable: quantityAvailable,
      };
      return filteredVariant;
    });
  }
  const cardData = {
    totalAmount: data.cost.totalAmount.amount,
    subtoalAmount: data.cost.subtotalAmount.amount,
    cartId: data.id,
    variants: filteredVariants,
    checkoutUrl: filteredVariants.length > 0 ? data.checkoutUrl : "",
  };
  return cardData;
};

const MyCart = ({ navigation, route }: any) => {
  const { cartId } = useSelector((state: any) => state.cart);
  const cartQtyTimersRef = useRef<{ [id: string]: NodeJS.Timeout }>({});
  const dispatch = useDispatch();
  const theme = useTheme();
  const [show, setshow] = useState(false);
  const { colors }: { colors: any } = theme;
  const cart: any = route?.params;
  const [merchandiseId, setMerchandiseId] = useState(null);
  const [cartUpdated, setCartUpdated] = useState(false);
  const [selectAll, setSelectAll] = useState(false);
  const [companyData, setCompanyData] = useState({});
  const [isVerified, setIsVerified] = useState(undefined);
  const { locale }: LanguageState = useSelector((state: any) => state.language);
  const staticLabels = locale === "en" ? staticTranslations.english : staticTranslations.malay;
  // Custom Alert Hook
  const {
    alertConfig,
    visible: alertVisible,
    showError,
    hideAlert,
  } = useCustomAlert();
  const [cartData, setCartData] = useState({
    totalAmount: 0,
    subtoalAmount: 0,
    cartId: "",
    variants: [],
    checkoutUrl: "",
  });
  const [localQuantities, setLocalQuantities] = useState<{ [id: string]: string }>({});
  const { height: screenHeight } = useWindowDimensions();
  useEffect(() => {
    const checkLogin = async () => {
      const checkVerificationStatus = async () => {
        try {
          const storedCustomerData = await AsyncStorage.getItem("customerData");
          const customerData = JSON.parse(storedCustomerData || "{}");
          const customerId = customerData?.data?.customer?.id;
          console.log("customerid>>>", customerId);

          if (!customerId) {
            console.warn("Customer ID not found in AsyncStorage");
            return;
          }

          const query = `
                query {
                  customer(id: "${customerId}") {
                    metafield(namespace: "custom", key: "verified") {
                      value
                    }
                  }
                }
              `;

          const response = await fetch(
            `${process.env.EXPO_PUBLIC_ADMIN_API_URL}`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "X-Shopify-Access-Token": `${process.env.EXPO_PUBLIC_ADMIN_ACCESS_TOKEN}`,
              },
              body: JSON.stringify({ query }),
            }
          );
          const result = await response.json();
          const verifiedStatus = result?.data?.customer?.metafield?.value;

          console.log("Verification status:", result);
          const isVerified: any = verifiedStatus === "true";
          console.log("verifiedStatus::", verifiedStatus);

          if (
            verifiedStatus === undefined ||
            verifiedStatus === null ||
            verifiedStatus === "" ||
            verifiedStatus === false
          ) {
            await AsyncStorage.setItem("isCompanyVerified", "false");
          }
          else {
            await AsyncStorage.setItem("isCompanyVerified", "true");
          }
          // Store verification status in AsyncStorage

          // Return the verification status instead of navigating directly
          setIsVerified(isVerified);

          return isVerified;
        } catch (error) {
          console.error("Verification check error:", error);
        }
      };
      const isCompanyVerified = await checkVerificationStatus();

      console.log("isCompanyVerified>>>>", isCompanyVerified);
    };
    checkLogin();
  }, []);
  const [isDraftOrderLoading, setIsDraftOrderLoading] = useState(false);

  const { loading, error, data, refetch } = useQuery(GET_CHECKOUT_PRODUCTS, {
    variables: { cartId },
    fetchPolicy: 'cache-and-network', // Always fetch fresh data but show cache first
    notifyOnNetworkStatusChange: true, // Update UI when network status changes
    skip: !cartId, // Skip query if no cartId
  });

  const [createCart, { loading: cartLoading, error: cartError }] =
    useMutation(CREATE_CART);

  const [removeCartItem, { loading: removeLoading }] = useMutation(
    REMOVE_LINES_FROM_CART
  );

  const [updateItem, { loading: updateLoading }] =
    useMutation(UPDATE_LINES_IN_CART);

  const fetchCompanyLocation = async (customerId: string) => {
    const variables = {
      customerId: customerId,
    };

    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_ADMIN_API_URL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": process.env.EXPO_PUBLIC_ADMIN_ACCESS_TOKEN,
        },
        body: JSON.stringify({
          query: COMPANY_LOCATION_QUERY,
          variables,
        }),
      });

      console.log("response>>>>", response);
      const result = await response.json();
      const profiles = result.data?.customer?.companyContactProfiles || [];

      if (profiles.length > 0) {
        const company = profiles[0].company;
        const companyId = company?.id;
        const locationEdges = company?.locations?.edges || [];
        const locationIds = locationEdges.map((edge: any) => edge.node.id);

        setCompanyData((prev) => ({
          ...prev,
          companyId,
          locationIds,
        }));
        console.log("Company ID:", companyId);
        console.log("Location IDs:", locationIds);
      }
      console.log("result>>>>>", result);

      if (result.errors) {
        console.error("GraphQL Errors:", result.errors);
        return null;
      }

      return result.data;
    } catch (error) {
      console.error("Fetch Error:", error);
      return null;
    }
  };

  const fetchCustomerContacts = async (customerId: string) => {
    const variables = { customerId: customerId };

    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_ADMIN_API_URL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": process.env.EXPO_PUBLIC_ADMIN_ACCESS_TOKEN,
        },
        body: JSON.stringify({
          query: `
          query getCustomerContacts($customerId: ID!) {
            customer(id: $customerId) {
              companyContactProfiles {
                id
                isMainContact
                company {
                  id
                  name
                }
              }
            }
          }
        `,
          variables,
        }),
      });

      const result = await response.json();
      console.log("companyCOntat ifnormation>>>>>", result);

      const companyContactProfiles =
        result.data?.customer?.companyContactProfiles || [];
      const companyContactId =
        companyContactProfiles.length > 0 ? companyContactProfiles[0].id : null;

      console.log("Company Contact ID:", companyContactId);
      if (companyContactId) {
        setCompanyData((prev) => ({
          ...prev,
          companyContactId,
        }));
      }

      if (result.errors) {
        console.error("GraphQL Errors:", result.errors);
        return null;
      }

      const contacts = result.data?.customer?.companyContactProfiles || [];
      console.log("Customer Contacts >>>>", contacts);
      return contacts;
    } catch (error) {
      console.error("Fetch Error:", error);
      return null;
    }
  };

  useEffect(() => {
    const getCustomerId = async () => {
      const customer: any = await AsyncStorage.getItem("customerData");
      const customerData = JSON.parse(customer);
      console.log("id>>>>>", customerData?.data?.customer?.id);
      const customerId = customerData?.data?.customer?.id;
      console.log("id>>>", customerId);
      // if (id) {
      console.log("id>>>", customerId);
      fetchCompanyLocation(customerId);
      fetchCustomerContacts(customerId);
      // }
    };
    getCustomerId();
  }, [navigation]);

  useEffect(() => {
    if (route.params?.cartId && route.params.cartId !== merchandiseId) {
      setMerchandiseId(route.params.cartId);
      setCartUpdated(true);
      // Also update Redux state with the new cart ID
      dispatch(setCartId(route.params.cartId));
    }
  }, [route.params?.cartId]);

  const toggleSelectAll = () => {
    if (cartData.variants?.length < 1) return;
    setSelectAll(!selectAll);
  };

  const handleIncrease = (variantId: string) => {
    if (cartLoading || removeLoading || updateLoading) return;

    const currentVariant: any = cartData.variants.find(
      (v: any) => v.merchandiseId === variantId
    );
    if (!currentVariant) return;

    // Debug log before update
    console.log('[handleIncrease] Before update:', currentVariant);

    // Check if current quantity is at or above quantityAvailable
    const available = typeof currentVariant.quantityAvailable === 'number' ? currentVariant.quantityAvailable : 9999;

    const newQuantity = currentVariant.quantity + 1;

    // Preserve existing attributes (like size)
    const attributes = currentVariant.size
      ? [{ key: "Size", value: currentVariant.size }]
      : [];

    updateItem({
      variables: {
        cartId,
        lines: [
          {
            id: currentVariant.lineId,
            merchandiseId: variantId,
            quantity: newQuantity,
            attributes: attributes,
          },
        ],
      },
    })
      .then(async (response) => {
        // Debug log after update
        console.log('[handleIncrease] After update response:', response.data.cartLinesUpdate.cart.lines.edges.map((edge: any) => ({
          lineId: edge.node.id,
          merchandiseId: edge.node?.merchandise?.id,
          imageUrl: edge.node?.merchandise?.image?.url,
          productImageUrl: edge.node?.merchandise?.product?.images?.edges[0]?.node?.url,
          title: edge.node?.merchandise?.title,
          productTitle: edge.node?.merchandise?.product?.title,
        })));
        const filtered = await filterCartData(
          response.data.cartLinesUpdate.cart
        );
        setCartData(filtered);
      })
      .catch((error) => {
        console.error("Update error:", error);
      });
  };

  const handleDecrease = (variantId: string) => {
    if (cartLoading || removeLoading || updateLoading) return;

    const currentVariant: any = cartData.variants.find(
      (v: any) => v.merchandiseId === variantId
    );
    if (!currentVariant) return;

    // Debug log before update
    console.log('[handleDecrease] Before update:', currentVariant);

    const newQuantity = Math.max(1, currentVariant.quantity - 1);

    // Preserve existing attributes (like size)
    const attributes = currentVariant.size
      ? [{ key: "Size", value: currentVariant.size }]
      : [];

    updateItem({
      variables: {
        cartId,
        lines: [
          {
            id: currentVariant.lineId,
            merchandiseId: variantId,
            quantity: newQuantity,
            attributes: attributes,
          },
        ],
      },
    })
      .then(async (response) => {
        // Debug log after update
        console.log('[handleDecrease] After update response:', response.data.cartLinesUpdate.cart.lines.edges.map((edge: any) => ({
          lineId: edge.node.id,
          merchandiseId: edge.node?.merchandise?.id,
          imageUrl: edge.node?.merchandise?.image?.url,
          productImageUrl: edge.node?.merchandise?.product?.images?.edges[0]?.node?.url,
          title: edge.node?.merchandise?.title,
          productTitle: edge.node?.merchandise?.product?.title,
        })));
        const filtered = await filterCartData(
          response.data.cartLinesUpdate.cart
        );
        setCartData(filtered);
      })
      .catch((error) => {
        console.error("Update error:", error);
      });
  };

  const removeItem = async (lineId: any) => {
    const lineIds = [lineId];
    try {
      const response = await removeCartItem({
        variables: { cartId, lineIds },
      });
      const filtered = await filterCartData(response.data.cartLinesRemove.cart);
      setCartData(filtered);
      const newCartId = response?.data?.cartLinesRemove?.cart?.id;
      if (newCartId) {
        await AsyncStorage.setItem("merchandiseId", newCartId);
        setMerchandiseId(newCartId);
      }
    } catch (err) {
      console.error("Cart removal error:", err);
      showError("Error", "Something went wrong. Please try again.");
    }
  };

  const removeAllItems = () => {
    if (cartData.variants?.length < 1) return;
    createCart({
      variables: {
        lines: [],
      },
    })
      .then(async (response: any) => {
        if (response.data.cartCreate.userErrors.length > 0) {
          showError("Error", response.data.cartCreate.userErrors[0].message);
        } else {
          try {
            // Get the new empty cart ID from the response
            const newCartId = response.data.cartCreate.cart.id;
            console.log("🆕 New empty cart ID after removeAll:", newCartId);

            // Update AsyncStorage with new cart ID
            await AsyncStorage.setItem("merchandiseId", newCartId);

            // Update all state with new cart ID
            setMerchandiseId(newCartId);
            dispatch(setCartId(newCartId));
            setSelectAll(false);

            // Set cart data to empty state with new cart ID
            setCartData({
              totalAmount: 0,
              subtoalAmount: 0,
              cartId: newCartId,
              variants: [],
              checkoutUrl: "",
            });

            // Force refetch with new cart ID to ensure GraphQL cache is updated
            setTimeout(() => {
              refetch({ cartId: newCartId });
            }, 100);

            console.log("✅ All items removed successfully, new cart created");
          } catch (e: any) {
            console.log("Error updating cart after removeAll:", e?.message);
          }
        }
      })
      .catch((err) => {
        showError("Error", "Something went wrong. Please try again.");
        console.error("Cart clearing error:", err);
      });
  };

  useEffect(() => {
    const getFilteredData = async () => {
      const filtered = await filterCartData(data.cart);
      setCartData(filtered);
    };
    getFilteredData();
  }, [navigation, data]);

  const updateQuantity = (variantId: any, newQuantity: any) => {
    const currentVariant: any = cartData.variants.find(
      (v: any) => v.merchandiseId === variantId
    );
    if (!currentVariant) return;

    // Preserve existing attributes (like size)
    const attributes = currentVariant.size
      ? [{ key: "Size", value: currentVariant.size }]
      : [];

    updateItem({
      variables: {
        cartId,
        lines: [
          {
            id: currentVariant.lineId,
            merchandiseId: variantId,
            quantity: newQuantity,
            attributes: attributes,
          },
        ],
      },
    })
      .then(async (response) => {
        const filtered = await filterCartData(
          response.data.cartLinesUpdate.cart
        );
        setCartData(filtered);
      })
      .catch((error) => {
        console.error("Update error:", error);
      });
  };

  const placeDraftOrder = async () => {
    const lineItems = cartData.variants.map((item: any) => ({
      variantId: item.merchandiseId, // adjust if your key differs
      quantity: item.quantity || 0,
    }));
    console.log("lineItems>>>", lineItems);

    const variables = {
      input: {
        lineItems,
        purchasingEntity: {
          purchasingCompany: {
            companyId: companyData?.companyId,
            companyLocationId: companyData?.locationIds[0],
            companyContactId: companyData?.companyContactId,
          },
        },
        paymentTerms: {
          paymentTermsTemplateId: "gid://shopify/PaymentTermsTemplate/5", // Net 60
          paymentSchedules: [{ issuedAt: new Date().toISOString() }],
        },
      },
    };

    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_ADMIN_API_URL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": process.env.EXPO_PUBLIC_ADMIN_ACCESS_TOKEN,
        },
        body: JSON.stringify({
          query: DRAFT_ORDER_MUTATION,
          variables,
        }),
      });

      const result = await response.json();
      console.log("Draft Order Result >>>>>", result);

      if (result.errors || result.data?.draftOrderCreate?.userErrors?.length) {
        console.error(
          "Draft Order Errors:",
          result.errors || result.data.draftOrderCreate.userErrors
        );
        return null;
      }

      const draftOrder = result.data?.draftOrderCreate?.draftOrder;
      console.log("Draft Order Created:", draftOrder.invoiceUrl);

      return draftOrder.invoiceUrl;
    } catch (error) {
      console.error("Draft Order Error:", error);
      return null;
    }
  };

  // Sync localQuantities with cartData.variants
  useEffect(() => {
    if (cartData.variants && Array.isArray(cartData.variants)) {
      const newQuantities: { [id: string]: string } = {};
      cartData.variants.forEach((item: any) => {
        newQuantities[item.merchandiseId] = String(item.quantity || 0);
      });
      setLocalQuantities(newQuantities);
    }
  }, [cartData.variants]);

  return (
    <SafeAreaView style={{ backgroundColor: COLORS.background, flex: 1 }}>
      <Header title={staticLabels.my_cart} leftIcon={"back"} titleLeft paddingLeft />
      <>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginHorizontal: Platform.OS === "ios" ? 25 : 0,
          }}
        >
          <CheckBox
            label={`${selectAll ? cartData.variants.length : 0}/${cartData.variants.length
              } ${staticLabels.items_selected}`}
            styles={{ width: "80%" }}
            checkedState={{
              show: selectAll,
              setshow: toggleSelectAll,
              checkedUnChecked: toggleSelectAll,
            }}
          />
          <TouchableOpacity
            onPress={removeAllItems}
            style={{ width: Platform.OS === "ios" ? 30 : 40 }}
            disabled={cartData.variants.length === 0 || !selectAll}
          >
            <Image
              source={IMAGES.delete}
              style={{
                width: 20,
                height: 20,
                opacity: selectAll === false ? 0.3 : 1,
              }}
            />
          </TouchableOpacity>
        </View>
        <ScrollView
          contentContainerStyle={{
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {loading ? (
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
                height: 600,
              }}
            >
              <ActivityIndicator size="large" color={COLORS.primary} />
            </View>
          ) : cartData?.variants?.length === 0 ? (
            <View style={{ height: screenHeight * 0.5, justifyContent: "center", alignItems: "center", marginTop: 40 }}>
              <Image
                source={{ uri: "https://cdn-icons-png.flaticon.com/256/11329/11329060.png" }} // Optional: use a placeholder image
                style={{ width: 120, height: 120, marginBottom: 16 }}
                resizeMode="contain"
              />
              <Text style={{ ...FONTS.fontMedium, fontSize: 16, color: COLORS.gray }}>
                No items in your cart
              </Text>
            </View>
          ) : (
            cartData?.variants?.map((data: any, index: any) => {
              // Inline quantity input logic with localQuantities
              const localQty = localQuantities[data.merchandiseId] ?? String(data.quantity || 1);
              const available = typeof data.quantityAvailable === 'number' ? data.quantityAvailable : 9999;
              const handleTextChange = (text: string) => {
                const numeric = text.replace(/[^0-9]/g, "");
                let parsed = parseInt(numeric, 10);
                if (isNaN(parsed)) parsed = 1;
                // Remove inventory restriction: do not clamp to available, and do not show error
                if (parsed < 1) parsed = 0;
                setLocalQuantities((prev) => ({ ...prev, [data.merchandiseId]: String(parsed) }));
                // Debounce updateQuantity
                if (cartQtyTimersRef.current[data.merchandiseId]) clearTimeout(cartQtyTimersRef.current[data.merchandiseId]);
                cartQtyTimersRef.current[data.merchandiseId] = setTimeout(() => {
                  if (parsed === 0) {
                    removeItem(data?.lineId);
                  } else {
                    updateQuantity(data?.merchandiseId, parsed);
                  }
                }, 500);
              };
              return (
                <TouchableOpacity
                  activeOpacity={0.7}
                  onPress={() =>
                    navigation.push("ProductDetailsPage", {
                      handle: data?.handle,
                    })
                  }
                  style={{ marginHorizontal: 5, width: "90%" }}
                  key={index}
                >
                  <View
                    style={{
                      borderRadius: 20,
                      backgroundColor: COLORS.card,
                      flexDirection: "row",
                      marginVertical: 10,
                      padding: 10,
                    }}
                  >
                    {/* Image Block */}
                    <View style={{ width: "40%", marginTop: 3 }}>
                      <Image
                        style={{
                          width: "100%",
                          aspectRatio: 1,
                          borderRadius: 10,
                          objectFit: "fill",
                        }}
                        source={{ uri: data?.url ?? "https://t3.ftcdn.net/jpg/02/01/90/26/360_F_201902639_gZhcd695qjLUnVtJPXqlWpxNj5TvVHw2.jpg" }}
                      />
                    </View>

                    {/* Detail Block */}
                    <TouchableWithoutFeedback>
                      <View
                        style={{
                          flex: 1,
                          paddingLeft: 10,
                          gap: 5
                          // justifyContent: "space-between",
                        }}
                      >
                        {/* Title + Remove */}
                        <View style={{ flexDirection: "row", alignItems: "center" }}>
                          <Text
                            style={{
                              flex: 1,
                              fontSize: SIZES.font,
                              color: COLORS.addToCartCardTitle,
                              ...FONTS.fontMedium,
                            }}
                            numberOfLines={1}
                          >
                            {data?.productTitle.length > 8
                              ? `${data.productTitle.slice(0, 28)}...`
                              : data.productTitle}
                          </Text>
                          <TouchableOpacity onPress={() => removeItem(data?.lineId)}>
                            <Image
                              source={Close}
                              style={{
                                width: 20,
                                height: 20,
                                marginLeft: 10,
                              }}
                            />
                          </TouchableOpacity>
                        </View>

                        {/* Price + MRP */}
                        <View style={{ flexDirection: "row", alignItems: "center", }}>
                          <Text style={{ ...FONTWEIGHT.Bold }}>
                            {"$" + data?.price.amount}
                          </Text>
                          <Text
                            style={{
                              color: COLORS.lightgray,
                              textDecorationLine: "line-through",
                              marginLeft: 8,
                              ...FONTWEIGHT.Normal,
                            }}
                          >
                            {"$" + (data?.price.amount * 2)}
                          </Text>
                        </View>

                        {/* Size and Color */}
                        <View style={{}}>
                          {data?.size && (
                            <View style={{ flexDirection: "row", marginBottom: 2 }}>
                              <Text style={{ ...FONTS.fontSm, color: COLORS.textBrandName }}>
                                Size:
                              </Text>
                              <Text
                                style={{
                                  ...FONTS.fontSm,
                                  color: COLORS.darkgray,
                                  marginLeft: 4,
                                  fontFamily: "DMSansMedium",
                                }}
                              >
                                {data?.size}
                              </Text>
                            </View>
                          )}

                          <View style={{ flexDirection: "row", width: "80%" }}>
                            <Text style={{ ...FONTS.fontSm, color: COLORS.textBrandName }}>
                              Color
                            </Text>
                            <Text
                              style={{
                                ...FONTS.fontSm,
                                color: COLORS.darkgray,
                                marginLeft: 4,
                                fontFamily: "DMSansMedium",
                              }}
                            >
                              {data?.merchandiseTitle}
                            </Text>
                          </View>
                        </View>

                        {/* Quantity Selector */}
                        <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                            backgroundColor: "#FFFFFF",
                            borderRadius: 10,
                            borderWidth: 1,
                            borderColor: COLORS.darkgray,
                            overflow: "hidden",
                            height: 35,
                            width: 130,
                            marginBottom: 12,
                            marginTop: 5
                          }}
                        >
                          <TouchableOpacity
                            onPressOut={() => handleDecrease(data?.merchandiseId)}
                            disabled={updateLoading || removeLoading || cartLoading}
                            style={{
                              flex: 1,
                              height: "100%",
                              justifyContent: "center",
                              alignItems: "center",
                              backgroundColor: "transparent",
                              opacity: updateLoading || removeLoading || cartLoading ? 0.6 : 1,
                            }}
                            hitSlop={{
                              top: 10,
                              bottom: 10,
                              left: 10,
                              right: 10,
                            }}
                          >
                            <Image style={{ width: 15, height: 15 }} source={RemoveIcon} />
                          </TouchableOpacity>
                          <View
                            style={{
                              flex: 1,
                              height: "100%",
                              justifyContent: "center",
                              alignItems: "center",
                              backgroundColor: "transparent",
                            }}
                          >
                            <TextInput
                              style={{
                                fontWeight: "600",
                                textAlign: "center",
                                width: "100%",
                                height: "100%",
                                backgroundColor: "transparent",
                                borderWidth: 0,
                                paddingVertical: 0,
                                paddingHorizontal: 0,
                                ...FONTS.font,
                                color: "#222222",
                                fontSize: 14,
                              }}
                              keyboardType="numeric"
                              value={localQty}
                              onChangeText={handleTextChange}
                              maxLength={6}
                              selectTextOnFocus={false}
                              editable={!(updateLoading || removeLoading || cartLoading)}
                            />
                          </View>
                          <TouchableOpacity
                            onPressOut={() => handleIncrease(data?.merchandiseId)}
                            disabled={updateLoading || removeLoading || cartLoading}
                            style={{
                              flex: 1,
                              height: "100%",
                              justifyContent: "center",
                              alignItems: "center",
                              backgroundColor: "transparent",
                              opacity: updateLoading || removeLoading || cartLoading ? 0.6 : 1,
                            }}
                            hitSlop={{
                              top: 10,
                              bottom: 10,
                              left: 10,
                              right: 10,
                            }}
                          >
                            <Image style={{ width: 15, height: 15 }} source={AddIcon} />
                          </TouchableOpacity>
                        </View>
                      </View>
                    </TouchableWithoutFeedback>
                  </View>
                </TouchableOpacity>
              );
            })
          )}
        </ScrollView>
        <View
          style={{
            backgroundColor: COLORS.white,
            padding: 10,
            borderTopColor: COLORS.gray,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            marginHorizontal: 3,
            height: 150,
            gap: 10,
          }}
        >
          <View>
            <Text style={{ ...FONTS.font, ...FONTWEIGHT.Bold }}>
              {staticLabels.order_details}
            </Text>
          </View>
          <View
            style={{
              width: "100%",
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text style={{ ...FONTS.fontSm, ...FONTWEIGHT.SemiBold }}>
              {staticLabels.subtotal}
            </Text>
            <Text style={{ flexDirection: "row" }}>
              <Text
                style={{
                  ...FONTS.fontXLg,
                  ...FONTWEIGHT.Bold,
                  textAlign: "center",
                }}
              >
                $
              </Text>
              <Text
                style={{
                  ...FONTS.fontXLg,
                  textAlign: "center",
                  ...FONTS.fontSemiBold,
                }}
              >
                {cartData.subtoalAmount}
              </Text>
            </Text>
          </View>
          <View
            style={{
              width: "100%",
              flexDirection: "row",
              justifyContent: "space-between",
            }}
          >
            <View
              style={{
                justifyContent: "center",
                gap: 5,
                width: "25%",
              }}
            >
              <Text style={{ flexDirection: "row" }}>
                <Text
                  style={{
                    ...FONTS.fontXLg,
                    ...FONTWEIGHT.Bold,
                    textAlign: "center",
                  }}
                >
                  $
                </Text>
                <Text
                  style={{
                    ...FONTS.fontXLg,
                    ...FONTWEIGHT.Bold,
                    textAlign: "center",
                  }}
                >
                  {cartData.subtoalAmount}
                </Text>
              </Text>
              <Text
                style={{
                  ...FONTS.fontXs,
                  ...FONTWEIGHT.SemiBold,
                  textAlign: "center",
                }}
              >
                {`${staticLabels.incl_of_all_taxes}`}
              </Text>
            </View>
            <View>
              <Button
                rightDisabled={
                  cartData.checkoutUrl === "" || updateLoading ? true : false
                }
                onPress={async () => {
                  if (cartData.checkoutUrl === "" || isVerified !== true) {
                    return showError(
                      "Company Verification Required",
                      "Your Company is not Verified. Please verify first then you can proceed."
                    );
                  }
                  if (updateLoading) {
                    return showError(
                      "Please Wait",
                      "Cart is being updated. Please wait for the update to complete."
                    );
                  }
                  setIsDraftOrderLoading(true);
                  // console.log(
                  //   "Ready for procced",
                  //   cartData.checkoutUrl === "" || isVerified !== true
                  // );
                  const draftOrderUrl = await placeDraftOrder();
                  setIsDraftOrderLoading(false);
                  console.log("cartData.checkoutUrl", draftOrderUrl);
                  navigation.navigate("CheckouWebViewPage", {
                    checkoutUrl: draftOrderUrl,
                  });
                }}
                title={
                  updateLoading ? `${staticLabels.updating_cart}...` : staticLabels.place_order
                }
                btnRounded
                style={{
                  width: 200,
                  opacity: updateLoading ? 0.6 : 1,
                }}
                loading={isDraftOrderLoading || updateLoading}
              />
            </View>
          </View>
        </View>
      </>

      {/* Custom Alert */}
      {alertConfig && (
        <CustomAlert
          visible={alertVisible}
          title={alertConfig.title}
          message={alertConfig.message}
          type={alertConfig.type}
          buttons={alertConfig.buttons}
          onClose={hideAlert}
        />
      )}
    </SafeAreaView>
  );
};

export default MyCart;

const styles = StyleSheet.create({
  // Removed unused quantity button styles since we now use CartQuantitySelector
});
