import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  Alert,
  SafeAreaView,
  StyleSheet,
  Platform,
} from "react-native";
import { useTheme } from "@react-navigation/native";
import Button from "../../components/Button/Button";
import Header from "../../layout/Header";
import { COLORS, FONTS } from "../../constants/theme";
import CrashlyticsService from "../../services/CrashlyticsService";
import crashlytics from "@react-native-firebase/crashlytics";

const CrashlyticsDemo: React.FC = ({ navigation }: any) => {
  const theme = useTheme();
  const [isInitialized, setIsInitialized] = useState(false);

  const [loading, setLoading] = useState(false);
  const [enabled, setEnabled] = useState(
    crashlytics().isCrashlyticsCollectionEnabled
  );

  useEffect(() => {
    checkInitializationStatus();
  }, []);

  async function toggleCrashlytics() {
    await crashlytics()
      .setCrashlyticsCollectionEnabled(!enabled)
      .then(() => setEnabled(crashlytics().isCrashlyticsCollectionEnabled));
  }

  const checkInitializationStatus = () => {
    const initialized = CrashlyticsService.isReady();
    setIsInitialized(initialized);

    // Note: User info is managed internally by Crashlytics
  };

  const initializeCrashlytics = async () => {
    setLoading(true);
    try {
      const success = await CrashlyticsService.initialize();
      if (success) {
        setIsInitialized(true);
        Alert.alert("Success", "Crashlytics initialized successfully!");
        checkInitializationStatus();
      } else {
        Alert.alert("Error", "Failed to initialize Crashlytics");
      }
    } catch (error) {
      Alert.alert(
        "Error",
        "Failed to initialize Crashlytics: " + error.message
      );
    } finally {
      setLoading(false);
    }
  };

  const logCustomMessage = () => {
    const message = "User clicked custom log button";
    const additionalData = {
      screen: "CrashlyticsDemo",
      timestamp: new Date().toISOString(),
      platform: Platform.OS,
    };

    CrashlyticsService.log(message, additionalData);
    Alert.alert("Success", "Custom message logged to Crashlytics!");
  };

  const setUserIdentifier = () => {
    Alert.prompt(
      "Set User ID",
      "Enter a user ID:",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Set",
          onPress: async (userId) => {
            if (userId) {
              await CrashlyticsService.setUserId(userId);
              Alert.alert("Success", `User ID set to: ${userId}`);
            }
          },
        },
      ],
      "plain-text",
      "user123"
    );
  };

  const setUserEmail = () => {
    Alert.prompt(
      "Set User Email",
      "Enter user email:",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Set",
          onPress: async (email) => {
            if (email) {
              await CrashlyticsService.setUserEmail(email);
              Alert.alert("Success", `User email set to: ${email}`);
            }
          },
        },
      ],
      "plain-text",
      "<EMAIL>"
    );
  };

  const setCustomAttributes = async () => {
    const attributes = {
      userType: "premium",
      appVersion: "1.0.0",
      lastLoginDate: new Date().toISOString(),
      deviceType: Platform.OS === "ios" ? "iPhone" : "Android",
    };

    await CrashlyticsService.setUserAttributes(attributes);
    Alert.alert("Success", "Custom attributes set!");
  };

  const recordNonFatalError = () => {
    try {
      // Simulate an error
      throw new Error("This is a test non-fatal error from CrashlyticsDemo");
    } catch (error) {
      CrashlyticsService.recordError(
        error as Error,
        "CrashlyticsDemo - Test Error"
      );
      Alert.alert("Success", "Non-fatal error recorded!");
    }
  };

  const recordCustomError = () => {
    const errorMessage = "Custom API call failed";
    const errorCode = "API_ERROR_001";
    const additionalData = {
      endpoint: "/api/users",
      statusCode: 500,
      timestamp: new Date().toISOString(),
    };

    CrashlyticsService.recordCustomError(
      errorMessage,
      errorCode,
      additionalData
    );
    Alert.alert("Success", "Custom error recorded!");
  };

  const setCustomKeys = async () => {
    await CrashlyticsService.setCustomKey("feature_flag_enabled", true);
    await CrashlyticsService.setCustomKey("user_level", 5);
    await CrashlyticsService.setCustomKey("last_action", "button_click");

    Alert.alert("Success", "Custom keys set!");
  };

  const clearUserData = async () => {
    await CrashlyticsService.clearUserData();
    Alert.alert("Success", "User data cleared!");
  };

  const triggerTestCrash = () => {
    Alert.alert(
      "Warning",
      "This will crash the app immediately! Are you sure?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Crash App",
          style: "destructive",
          onPress: () => {
            CrashlyticsService.testCrash();
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: COLORS.background }]}
    >
      <Header title="Crashlytics Demo" leftIcon="back" titleLeft />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Status Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Status</Text>
          <View style={styles.statusContainer}>
            <Text style={styles.statusText}>
              Initialized: {isInitialized ? "✅ Yes" : "❌ No"}
            </Text>
          </View>
        </View>

        {/* Initialization Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Initialization</Text>
          <Button
            title="Initialize Crashlytics"
            onPress={initializeCrashlytics}
            loading={loading}
            style={styles.button}
          />
        </View>

        {/* Logging Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Custom Logging</Text>
          <Button
            title="Log Custom Message"
            onPress={logCustomMessage}
            style={styles.button}
          />
        </View>

        {/* User Identification Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>User Identification</Text>
          <Button
            title="Set User ID"
            onPress={setUserIdentifier}
            style={styles.button}
          />
          <Button
            title="Set User Email"
            onPress={setUserEmail}
            style={styles.button}
          />
          <Button
            title="Set Custom Attributes"
            onPress={setCustomAttributes}
            style={styles.button}
          />
          <Button
            title="Clear User Data"
            onPress={clearUserData}
            style={styles.button}
          />
        </View>

        {/* Error Recording Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Error Recording</Text>
          <Button
            title="Record Non-Fatal Error"
            onPress={recordNonFatalError}
            style={styles.button}
          />
          <Button
            title="Record Custom Error"
            onPress={recordCustomError}
            style={styles.button}
          />
        </View>

        {/* Custom Keys Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Custom Keys</Text>
          <Button
            title="Set Custom Keys"
            onPress={setCustomKeys}
            style={styles.button}
          />
        </View>

        {/* Test Crash Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Test Crash</Text>
          <Text style={styles.warningText}>
            ⚠️ Warning: This will crash the app immediately!
          </Text>
          <Button
            title="Trigger Test Crash"
            onPress={triggerTestCrash}
            style={[styles.button, styles.dangerButton]}
          />
        </View>
        <Button title="Toggle Crashlytics" onPress={toggleCrashlytics} />
        <Button title="Crash" onPress={() => crashlytics().crash()} />
        <Text>Crashlytics is currently {enabled ? "enabled" : "disabled"}</Text>
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginVertical: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: COLORS.title,
    marginBottom: 10,
    ...FONTS.fontMedium,
  },
  statusContainer: {
    backgroundColor: COLORS.card,
    padding: 15,
    borderRadius: 10,
  },
  statusText: {
    fontSize: 14,
    color: COLORS.text,
    marginBottom: 5,
    ...FONTS.fontRegular,
  },
  button: {
    marginVertical: 5,
  },
  dangerButton: {
    backgroundColor: "#FF4444",
  },
  warningText: {
    fontSize: 12,
    color: "#FF6B6B",
    marginBottom: 10,
    textAlign: "center",
    ...FONTS.fontRegular,
  },
  bottomSpacing: {
    height: 50,
  },
});

export default CrashlyticsDemo;
