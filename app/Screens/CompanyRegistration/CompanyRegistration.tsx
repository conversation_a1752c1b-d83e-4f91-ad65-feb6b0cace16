import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Image,
  Linking,
  ActivityIndicator,
} from "react-native";
import HeaderStyle4 from "../../components/Headers/HeaderStyle4";
import StepIndicator from "react-native-step-indicator";
// import { companyDetails } from "../../constants/companyDetails";
import { COLORS, FONTS, SORA } from "../../constants/theme";
import { Picker } from "@react-native-picker/picker";
import ChevronICon from "../../assets/icons/chevron.png";
import * as DocumentPicker from "expo-document-picker";
import { Feather } from "@expo/vector-icons";
import { useQuery } from "@apollo/client";
import { COMPANY_VERIFY_STATUS_CHECK } from "@/app/api/verifyStatus";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  CommonActions,
  useFocusEffect,
  useIsFocused,
  useRoute,
} from "@react-navigation/native";
import CustomAlert from "../../components/CustomAlert";
import { useCustomAlert } from "../../hooks/useCustomAlert";
import SelectDropdown from "react-native-select-dropdown";
import { useSelector } from "react-redux";
import { LanguageState } from "@/app/redux/reducer/languageReducer";
import { companyRegistraion } from "@/app/constants/companyDetails";
import { staticTranslations } from "@/app/constants/staticTranslations";
import { malaysiaStateZipMap } from "@/app/constants/malaysiaStateZipMap";
import PhoneInput from "react-native-phone-number-input";
import * as FileSystem from 'expo-file-system';

interface FormData {
  [key: string]: string | FileData;
}

interface FileData {
  uri: string;
  name: string;
  type: string;
}

interface StepDetails {
  [key: string]: string;
}

interface ApiResponse {
  success: boolean;
  data?: {
    currentStep: number;
    details: {
      [key: string]: StepDetails;
    };
  };
}

const CountryData = [
  { value: "Select", label: "Select" },
  { value: "MY", label: "Malesia" },
];
const StateData = [
  { value: "Select", label: "Select" },
  { value: "JHR", label: "Johor" },
  { value: "KDH", label: "Kedah" },
  { value: "KTN", label: "Kelantan" },
  { value: "MLK", label: "Malacca (Melaka)" },
  { value: "N9N", label: "Negeri Sembilan" },
  { value: "PHG", label: "Pahang" },
  { value: "PNG", label: "Penang (Pulau Pinang)" },
  { value: "PRK", label: "Perak" },
  { value: "PLS", label: "Perlis" },
  { value: "SBH", label: "Sabah" },
  { value: "SWK", label: "Sarawak" },
  { value: "SGR", label: "Selangor" },
  { value: "TRG", label: "Terengganu" },
  { value: "KUL", label: "Kuala Lumpur" },
  { value: "LBN", label: "Labuan" },
  { value: "PJY", label: "Putrajaya" },
];

// Utility to strip country code and leading zero from phone number
function getLocalPhoneNumber(phone: string | undefined, dialCode: string | undefined): string {
  let local = phone || '';
  if (dialCode && local.startsWith('+' + dialCode)) {
    local = local.slice(('+' + dialCode).length);
  }
  if (local.startsWith('0')) {
    local = local.slice(1);
  }
  return local;
}

const CompanyRegistration = ({ navigation }: any) => {
  const [currentPosition, setCurrentPosition] = useState(0);
  const [formData, setFormData] = useState<FormData>({});
  const [finalReviewFields, setFinalReviewFields] = useState<FormData>({});
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(true);
  const [isPickingDocument, setIsPickingDocument] = useState(false);
  const [zipValidation, setZipValidation] = useState<{
    isValid: boolean | null;
    message: string;
  }>({ isValid: null, message: "" });
  const { locale }: LanguageState = useSelector((state: any) => state.language)
  const staticLabels = locale === "en" ? staticTranslations.english : staticTranslations.malay;

  const companyDetails = locale === "en" ? companyRegistraion.english : companyRegistraion.malay

  const route = useRoute();

  // Custom Alert Hook
  const {
    alertConfig,
    visible: alertVisible,
    showError,
    hideAlert,
  } = useCustomAlert();

  const [phone, setPhone] = useState("");
  const [countryCode, setCountryCode] = useState("MY"); // default Malaysia
  const [isPhoneValid, setIsPhoneValid] = useState(true);
  const phoneInput = useRef<PhoneInput>(null);

  // Define file fields config at the top-level of the component
  const limitedLiabilityFileFields = [
    { key: "form9", label: "Form 9 Business Registration form", isMandatory: true },
    { key: "form24", label: "Form 24 Allotment of Shares", isMandatory: true },
    { key: "form49", label: "Form 49 Directors Particulars and Company Secretory Info", isMandatory: true },
    { key: "memArticle", label: "Memorandom of Articles M&A", isMandatory: true },
    { key: "lan", label: "Latest Annual Returns", isMandatory: false },
    { key: "bankStatement", label: "Last 3 Month Bank Statements", isMandatory: false },
    { key: "icCard", label: "Identity Cards of Directors-IC Card", isMandatory: false },
  ];
  const soleProprietorFileFields = [
    { key: "formD", label: "Form D from ROC(SSM) -Peninsular Dealer", isMandatory: true },
    { key: "form1", label: "Form 1 and Biz registration certificate (Sabah Sarawak)", isMandatory: true },
    { key: "icCard", label: "Identity Cards of Directors-IC Card", isMandatory: true },
  ];

  useEffect(() => {
    fetchAndFillFormData();
  }, []);

  console.log("currentPosition", currentPosition);

  // Check if the company form is submitted
  useLayoutEffect(() => {
    const isCompanySubmitted = async () => {
      const isCompanyFormSubmitted = await AsyncStorage.getItem(
        "isCompanyFormSubmitted"
      );
      if (isCompanyFormSubmitted === "true") {
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "DrawerNavigation", params: { screen: "Home" } }],
          })
        );
      }
    };
    isCompanySubmitted();
  });

  useEffect(() => {
    const handleNavigation = async () => {
      // Wait for the verification check
      const isCompanyVerified = await AsyncStorage.getItem("isCompanyVerified");
      const isCompanyFormSubmitted = await AsyncStorage.getItem(
        "isCompanyFormSubmitted"
      );
      console.log("isCompanyFormSubmitted>>>>>", isCompanyFormSubmitted);

      // if (isCompanyFormSubmitted === "true") {
      //   setIsLoading(true);
      //   return navigation.dispatch(
      //     CommonActions.reset({
      //       index: 0,
      //       routes: [{ name: "DrawerNavigation", params: { screen: "Home" } }],
      //     })
      //   );
      // }
      if (isCompanyVerified === "true") {
        setIsPageLoading(true);
        return navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: "DrawerNavigation", params: { screen: "Home" } }],
          })
        );
      } else {
        setIsPageLoading(false);
        return;
      }
    };

    handleNavigation();
  }, [currentPosition, navigation]);

  // Ensure phone input always shows only the local number, even after navigating back
  useEffect(() => {
    if (currentPosition === 0 && phoneInput.current) {
      const dialCode = phoneInput.current.getCallingCode();
      setPhone(getLocalPhoneNumber(phone, dialCode));
    }
  }, [currentPosition]);

  const validateZipCode = (zip: string, stateValue: string) => {
    if (zip?.length > 0 && zip?.length < 5) {
      setZipValidation({
        isValid: false,
        message: "ZIP code must be 5 digits.",
      });
      return;
    }

    if (zip?.length !== 5) {
      setZipValidation({ isValid: null, message: "" });
      return;
    }

    const stateLabel = StateData.find((s) => s.value === stateValue)?.label;
    if (!stateLabel) {
      setZipValidation({
        isValid: false,
        message: "Please select a state first.",
      });
      return;
    }

    const zipRange = malaysiaStateZipMap[stateLabel];
    const zipNumber = parseInt(zip, 10);

    if (zipRange && zipNumber >= zipRange.min && zipNumber <= zipRange.max) {
      setZipValidation({
        isValid: true,
        message: "",
      });
    } else {
      setZipValidation({
        isValid: false,
        message: "ZIP code does not match selected state",
      });
    }
  };

  const handleInputChange = (field: string, value: string) => {
    if (value == "Select type of your company") {
      return showError(
        staticLabels?.empty_input,
        staticLabels?.select_validinput_errMsg
      );
    }
    const newFormData = { ...formData, [field]: value };
    // Phone number handled by PhoneInput
    if (field === "phone") {
      setPhone(value);
      setFormData({
        ...formData,
        phone: value,
        countryCode: countryCode,
      });
      return;
    }
    if (field === "state") {
      setFormData({ ...newFormData, pincode: "" });
      setZipValidation({ isValid: null, message: "" });
    } else {
      setFormData(newFormData);
    }

    if (field === "pincode") {
      validateZipCode(value, newFormData.state as string);
    }

    if (field === "state") {
      validateZipCode(newFormData.pincode as string, value);
    }
  };

  const handleFileUpload = async (field: string) => {
    if (isPickingDocument) {
      return; // Prevent multiple pickers
    }

    try {
      setIsPickingDocument(true);
      const result = await DocumentPicker.getDocumentAsync({
        type: "*/*",
        copyToCacheDirectory: true,
      });

      if (
        result.canceled === false &&
        result.assets &&
        result.assets.length > 0
      ) {
        const file = result.assets[0];

        // Check file size (2MB limit)
        const fileSizeLimit = 2 * 1024 * 1024;
        if (file.size && file.size > fileSizeLimit) {
          showError(
            "File Too Large",
            "The selected file exceeds the 2MB size limit."
          );
          return; // Don't set the file
        }

        setFormData({
          ...formData,
          [field]: {
            uri: file.uri,
            name: file.name || "file",
            type: file.mimeType || "application/octet-stream",
          },
        });
      }
    } catch (error) {
      console.error("Error picking document:", error);
      showError("Error", "Failed to pick document");
    } finally {
      setIsPickingDocument(false);
    }
  };

  const removeFile = (field: string) => {
    const fileData = formData[field];

    // Don't allow removing files that are already uploaded (URLs)
    if (typeof fileData === "string" && fileData.startsWith("http")) {
      showError("Warning", "Cannot remove already uploaded files. Please contact support if you need to replace this file.");
      return;
    }

    const newFormData = { ...formData };
    delete newFormData[field];
    setFormData(newFormData);
  };

  const fetchAndFillFormData = async () => {
    setIsLoading(true);

    const customerId = await AsyncStorage.getItem("accountNumber");
    console.log("customerId", typeof Number(customerId));
    try {
      const response = await fetch(
        `${process.env.EXPO_PUBLIC_COMPANY_REGISTRATION_API_URL}/${Number(
          customerId
        )}`,
        {
          method: "GET",
        }
      );

      const result: ApiResponse = await response.json();
      console.log("Fetched data:", result);

      if (result?.data?.details) {
        setIsLoading(false);
        const currentStep = result.data.currentStep;
        console.log("currentStep>>>>", currentStep);
        const details = result.data.details;

        // Fill final review fields
        fillFinalReviewFields(details);

        // Create a map of all form data
        const populatedFormData: FormData = {};

        // Process each step's data
        Object.entries(details).forEach(([stepKey, stepData]) => {
          Object.entries(stepData).forEach(([fieldKey, value]) => {
            if (value !== undefined && value !== null && value !== "") {
              if (fieldKey === "country") {
                populatedFormData["country"] = "MY";
              } else if (fieldKey === "phone") {
                // Always use the phone as returned from API (already in international format)
                populatedFormData[fieldKey] = value;
              } else {
                // For file fields, preserve URLs as strings
                populatedFormData[fieldKey] = value;
                console.log(`Setting ${fieldKey}:`, value, typeof value);
              }
            }
          });
        });

        // Update form data
        setFormData(populatedFormData);
        if (populatedFormData.state && populatedFormData.pincode) {
          validateZipCode(
            populatedFormData.pincode as string,
            populatedFormData.state as string
          );
        }
        if (populatedFormData.phone && typeof populatedFormData.phone === 'string' && phoneInput.current) {
          const dialCode = phoneInput.current.getCallingCode();
          setPhone(getLocalPhoneNumber(populatedFormData.phone, dialCode));
        } else if (populatedFormData.phone && typeof populatedFormData.phone === 'string') {
          setPhone(getLocalPhoneNumber(populatedFormData.phone, undefined));
        }
        if (populatedFormData.countryCode) {
          console.log("populatedFormData.countryCode", populatedFormData.countryCode)
          setCountryCode(populatedFormData.countryCode as string);
        }
        // Only set the initial position on first load
        if (isInitialLoad) {
          // If we have all required data for step 3 (files), move to step 4
          if (currentStep === 3) {
            const hasRequiredFiles = checkRequiredFiles(populatedFormData);
            if (hasRequiredFiles) {
              setCurrentPosition(3); // Move to step 4 (index 3)
              setIsLoading(false);
              // return navigation.dispatch(
              //   CommonActions.reset({
              //     index: 0,
              //     routes: [
              //       { name: "DrawerNavigation", params: { screen: "Home" } },
              //     ],
              //   })
              // );
            } else {
              setCurrentPosition(2); // Stay on step 3 (index 2)
            }
          } else {
            setCurrentPosition(Math.max(0, currentStep - 1));
            // For other steps, use the API response
          }
          setIsInitialLoad(false);
        }
        // Don't change position if not initial load (when going back)
      }
    } catch (error) {
      console.error("Error fetching form data:", error);
      showError("Error", "Something went wrong while fetching your data.");
    }
  };

  // Update checkRequiredFiles to use the config
  const checkRequiredFiles = (data: FormData): boolean => {
    let fileFields: { key: string; label: string; isMandatory: boolean }[] = [];
    if (data.entityType === "Limited Liability Company") {
      fileFields = limitedLiabilityFileFields;
    } else if (data.entityType === "Sole Proprietor") {
      fileFields = soleProprietorFileFields;
    }
    return fileFields.filter(f => f.isMandatory).every(f => data[f.key]);
  };

  // Phone number validation function
  const isPhoneNumberValid = (phoneNumber: string): boolean => {
    if (!phoneNumber || typeof phoneNumber !== "string") {
      return false;
    }

    // Check if phone number starts with +60 and has the correct format
    if (!phoneNumber.startsWith("+60 ")) {
      return false;
    }

    // Extract the numeric part after +60
    const numericPart = phoneNumber.substring(4).replace(/[^0-9]/g, "");

    // Malaysian phone numbers must have exactly 10 digits after +60
    return numericPart.length === 9;
  };

  // Malaysian mobile validation function
  const isValidMalaysiaMobile = (number: string): boolean => {
    if (!number.startsWith("+60")) return false;
    const rest = number.slice(3).replace(/[^0-9]/g, "");
    if (rest.startsWith("11")) {
      return rest.length === 10; // 11 + 8 digits
    }
    if (/^1(0|2|3|4|6|7|8|9)/.test(rest)) {
      return rest.length === 9; // 1X + 7 digits
    }
    return false;
  };

  const isStepValid = () => {
    // For step 4 (Review & Submit), always return true since all data should be valid
    if (currentPosition === 3) {
      return true;
    }

    const currentStepFields = companyDetails.steps[currentPosition]?.input;

    // Special handling for step 3 (file uploads)
    if (currentPosition === 2) {
      if (!formData.entityType) return false;
      return checkRequiredFiles(formData);
    }

    // For other steps, check required fields
    return currentStepFields?.every((field: any) => {
      if (!field.isMandatory) return true;
      // console.log("formData.country????",formData.country)
      if (formData.country === "Select" || formData.state === "Select") {
        return false;
      }
      console.log(field)
      // Check for value in formData using all possible field identifiers
      const value =
        formData[field.valueField] ||
        formData[field.fieldName] ||
        formData[field.fieldLabel];

      // Special validation for phone number field
      if (
        field.fieldName === "phone" ||
        field.valueField === "businessContactNumber"
      ) {
        return isPhoneValid && phone.length > 0;
      }

      if (field.valueField === "zipcode" || field.fieldName === "pincode") {
        const zip = formData.pincode as string;
        console.log("zip>>>", zip)
        return zip?.length === 5 && zipValidation.isValid === true;
      }

      console.log(zipValidation, formData.pincode)
      // If value exists and is not empty, or if it's a file object, consider it valid
      return (
        (value && (typeof value === "string" ? value.trim() !== "" : true)) ||
        (typeof value === "object" && "uri" in value)
      );
    });
  };

  const fillFinalReviewFields = (details: { [key: string]: StepDetails }) => {
    const allFields: FormData = {};

    Object.entries(details).forEach(([stepKey, stepData]) => {
      Object.entries(stepData).forEach(([fieldKey, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          if (fieldKey === "phone") {
            // Always use the phone as returned from API (already in international format)
            allFields[fieldKey] = value;
          } else {
            allFields[fieldKey] = value;
          }
        }
      });
    });

    setFinalReviewFields(allFields);
  };

  // Utility to download a file from a URL to a local path
  async function downloadFileToLocal(url: string, filename: string): Promise<string> {
    const localUri = FileSystem.cacheDirectory + filename;
    const downloadResumable = FileSystem.createDownloadResumable(
      url,
      localUri
    );
    await downloadResumable.downloadAsync();
    return localUri;
  }

  // Helper to append file, downloading if needed
  async function appendFileToFormData(
    formDataObj: globalThis.FormData,
    field: string,
    fileData: FileData | string
  ): Promise<string | null> {
    if (typeof fileData === 'string' && fileData.startsWith('http')) {
      const fileName = fileData.split('/').pop() || 'file.pdf';
      const localUri = await downloadFileToLocal(fileData, fileName);
      formDataObj.append(field, {
        uri: localUri,
        name: fileName,
        type: 'application/pdf', // You may want to infer type
      } as any);
      return fileName;
    } else if (fileData && (fileData as FileData).uri) {
      const fd = fileData as FileData;
      formDataObj.append(field, {
        uri: fd.uri,
        name: fd.name,
        type: fd.type,
      } as any);
      return fd.name;
    }
    return null;
  }

  const handleNext = async () => {
    if (currentPosition < companyDetails.steps.length - 1) {
      try {
        setIsLoading(true);
        // Get the current step's input fields
        const currentStepFields = companyDetails.steps[currentPosition]?.input;
        console.log("phone>>>", phone)
        // Create a clean payload with all required fields
        const stepData: { [key: string]: any } = {
          // Include all required fields with default empty values
          name: formData.name || "",
          companyName: formData.companyName || "",
          email: formData.email || "",
          tin: formData.tin || "",
          brn: formData.brn || "",
          nricNumber: formData.nricNumber || "",
          obrn: formData.obrn || "",
          sst: formData.sst || "",
          country: "Malaysia",
          state: formData.state || "",
          city: formData.city || "",
          pincode: formData.pincode || "",
          businessAddress: formData.businessAddress || "",
          businessDescription: formData.businessDescription || "",
          msicCodes: formData.msicCodes || "",
          // Always use the full international phone number for submission
          phone: (() => {
            const dialCode = phoneInput.current?.getCallingCode();
            let localNumber = phone;
            // Remove leading zero if present (for submission)
            if (localNumber && localNumber.startsWith('0')) {
              localNumber = localNumber.slice(1);
            }
            // If already starts with +, don't prepend
            if (localNumber && localNumber.startsWith('+')) {
              return localNumber;
            }
            // If already starts with dial code, prepend +
            if (dialCode && localNumber && localNumber.startsWith(dialCode)) {
              return `+${localNumber}`;
            }
            return dialCode ? `+${dialCode}${localNumber}` : localNumber || "";
          })(),
        };

        // Map the current step's fields to the payload
        currentStepFields.forEach((field) => {
          const fieldKey = field.valueField;
          if (fieldKey && formData[fieldKey]) {
            stepData[fieldKey] = formData[fieldKey];
          }
        });

        let payload: any;
        let headers: any = {
          "Content-Type": "application/json",
        };

        if (currentPosition === 2) {
          // Step 3 - Handle file uploads
          const formDataToSend = new FormData();

          // Add entity type
          formDataToSend.append("entityType", formData.entityType as string);

          // Create details object with file information
          const details: { [key: string]: any } = {
            ...stepData,
            entityType: formData.entityType,
          };

          // Handle files based on company type
          const fileFields = formData.entityType === "Limited Liability Company"
            ? ["form9", "form24", "form49", "memArticle", "lan", "bankStatement", "icCard"]
            : ["formD", "form1", "icCard"];

          for (const field of fileFields) {
            const fileData = formData[field];
            if (fileData) {
              const fileName = await appendFileToFormData(formDataToSend, field, fileData);
              if (fileName) {
                details[field] = fileName;
              }
            }
          }

          // Check if we have mandatory files (either URLs or new uploads)
          const hasMandatoryFiles = checkRequiredFiles(formData);
          if (!hasMandatoryFiles) {
            showError(
              "Error",
              "Please ensure you have uploaded all mandatory documents"
            );
            return;
          }

          // Stringify the details object and append to FormData
          formDataToSend.append("details", JSON.stringify(details));
          formDataToSend.append("currentStep", "3");

          payload = formDataToSend;
          console.log("payload><<><", payload)
          // Remove Content-Type header for FormData
          headers = {
            Accept: "application/json",
          };
        } else {
          // Step 1 and 2 - Regular JSON payload
          payload = {
            currentStep: currentPosition + 1,
            details: JSON.stringify(stepData),
          };
          console.log("payload>>>><<<<>", payload)
        }

        console.log("Sending payload:", payload);
        const customerId = await AsyncStorage.getItem("accountNumber");

        // Log the request details for debugging
        console.log("Request URL:", `${process.env.EXPO_PUBLIC_COMPANY_REGISTRATION_API_URL}/${Number(customerId)}`);
        console.log("Request method: POST");
        console.log("Request headers:", headers);
        console.log("Request body type:", currentPosition === 2 ? "FormData" : "JSON");

        // Send the data to the API
        const response = await fetch(
          `${process.env.EXPO_PUBLIC_COMPANY_REGISTRATION_API_URL}/${Number(
            customerId
          )}`,
          {
            method: "POST",
            headers: headers,
            body: currentPosition === 2 ? payload : JSON.stringify(payload),
          }
        );

        console.log("Response status:", response.status);
        console.log("Response headers:", response.headers);

        const result = await response.json();
        console.log("API Response:", result);

        // Check if the response indicates success
        if (result.status === "success" || result.success) {
          // Move to next step sequentially
          setCurrentPosition(currentPosition + 1);

          // Fetch updated data after successful submission
          await fetchAndFillFormData();
        } else {
          // Handle error response
          const errorMessage =
            result.errors?.[0]?.message ||
            result.message ||
            "Failed to save form data. Please try again.";
          console.error("API Error:>>", result.errors[0].shopifyErrors[0].message);
          showError("Error", result.errors[0].shopifyErrors[0].message);
        }
      } catch (error) {
        console.error("Error saving form data:", error);
        showError("Error", "Phone has alreay been taken");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleBack = () => {
    if (currentPosition > 0) {
      setCurrentPosition(currentPosition - 1);
    }
  };

  const renderStep3Content = () => {
    return (
      <>
        <View style={styles?.inputContainer}>
          <Text style={styles?.inputLabel}>Select Company Type *</Text>
          {Platform.OS === "ios" ? (
            <SelectDropdown
              data={[
                {
                  label: "Select type of your company",
                  value: "Select type of your company",
                },
                {
                  label: "Limited Liability Company",
                  value: "Limited Liability Company",
                },
                { label: "Sole Proprietor", value: "Sole Proprietor" },
              ]}
              onSelect={(selectedItem) => {
                handleInputChange("entityType", selectedItem.value);
              }}
              renderButton={(selectedItem, isOpened) => {
                return (
                  <View
                    style={[
                      styles.dropdownButtonStyle,
                      {
                        borderWidth: 1,
                        borderRadius: 10,
                        borderColor: "lightgray",
                        height: 55,
                        backgroundColor: COLORS.background,
                      },
                    ]}
                  >
                    <Text style={[styles.dropdownButtonTxtStyle, FONTS.fontSm]}>
                      {(selectedItem && selectedItem.label) ||
                        (formData.entityType as string) ||
                        "Select type of your company"}
                    </Text>
                    <Image
                      source={ChevronICon}
                      style={[
                        styles.dropdownButtonArrowStyle,
                        {
                          width: 24,
                          height: 24,
                        },
                      ]}
                    />
                  </View>
                );
              }}
              renderItem={(item, index, isSelected) => {
                return (
                  <View
                    style={[
                      styles.dropdownItemStyle,
                      { backgroundColor: isSelected ? "#E7E7E7" : "white" },
                    ]}
                  >
                    <Text style={styles.dropdownItemTxtStyle}>
                      {item.label}
                    </Text>
                  </View>
                );
              }}
              showsVerticalScrollIndicator={false}
              dropdownStyle={styles.dropdownMenuStyle}
            />
          ) : (
            <View
              style={{
                borderWidth: 1,
                borderRadius: 10,
                borderColor: "lightgray",
                ...FONTS.fontSm,
                height: 55,
              }}
            >
              <Picker
                selectedValue={
                  (formData.entityType as string) ||
                  "Select type of your company"
                }
                onValueChange={(value) =>
                  handleInputChange("entityType", value)
                }
                style={{ borderWidth: 1, borderColor: "red", ...FONTS.font }}
                dropdownIconColor="white"
              >
                <Picker.Item
                  label="Select type of your company"
                  value="Select type of your company"
                  style={{ ...FONTS.font }}
                />
                <Picker.Item
                  label="Limited Liability Company"
                  value="Limited Liability Company"
                  style={{ ...FONTS.font }}
                />
                <Picker.Item
                  label="Sole Proprietor"
                  value="Sole Proprietor"
                  style={{ ...FONTS.font }}
                />
              </Picker>
              <Image
                source={ChevronICon}
                style={{
                  width: 24,
                  height: 24,
                  position: "absolute",
                  top: 15,
                  right: 10,
                }}
              />
            </View>
          )}
        </View>

        {formData.entityType === "Limited Liability Company" && (
          <>
            {limitedLiabilityFileFields.map(field =>
              renderFileUploadField(field.key, field.label, field.isMandatory)
            )}
          </>
        )}

        {formData.entityType === "Sole Proprietor" && (
          <>
            {soleProprietorFileFields.map(field =>
              renderFileUploadField(field.key, field.label, field.isMandatory)
            )}
          </>
        )}
      </>
    );
  };

  const renderFileUploadField = (
    field: string,
    label: string,
    isMandatory: boolean
  ) => {
    const fileData = formData[field] as FileData | string;
    return (
      <View key={field} style={styles.fileUploadContainer}>
        <Text style={styles?.inputLabel}>
          {label}
          {isMandatory ? "*" : ""}
        </Text>
        <TouchableOpacity
          style={[
            styles.uploadButton,
            isPickingDocument && styles.disabledUploadButton,
          ]}
          onPress={() => handleFileUpload(field)}
          disabled={isPickingDocument}
        >
          <Image
            source={{
              uri: "https://cdn.shopify.com/s/files/1/0741/2259/2535/files/gallery-export.png?v=1712903122",
            }}
            style={{ width: 24, height: 24 }}
          />
          <Text style={styles.uploadText}>Upload File</Text>
        </TouchableOpacity>
        <Text style={styles.fileSizeText}>Maximum file size:<Text style={{ color: "red" }}> 2MB</Text></Text>

        {fileData && (
          <View style={styles.filePreview}>
            <View style={styles.filePreviewRow}>
              <View style={styles.fileInfoContainer}>
                <Feather name="file" size={20} color="#1E60AE" />
                <Text
                  style={styles.fileName}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {typeof fileData === "object" && fileData.name
                    ? fileData.name
                    : typeof fileData === "string"
                      ? fileData.split("/").pop()
                      : "Unknown file"}
                </Text>
              </View>
              {typeof fileData === "string" && fileData.startsWith("http") ? (
                <Text style={{ color: "green", fontSize: 12 }}>Uploaded</Text>
              ) : (
                <TouchableOpacity onPress={() => removeFile(field)}>
                  <Feather name="x" size={20} color="gray" />
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}
      </View>
    );
  };

  const renderStep4Content = () => {
    return (
      <View style={styles.reviewContainer}>
        {/* Display all text fields from all steps */}
        {companyDetails.steps.slice(0, 3).map((step) =>
          step?.input.map((field: any) => {
            // Get value from formData using valueField, fieldName, or fieldLabel
            const value =
              formData[field.valueField] ||
              formData[field.fieldName] ||
              formData[field.fieldLabel];

            // Skip if no value or if it's a file object
            if (!value || (typeof value === "object" && "uri" in value))
              return null;

            return (
              <View key={field.valueField} style={styles?.inputContainer}>
                <Text style={styles?.inputLabel}>
                  {field.label}
                  {field.isMandatory ? "*" : ""}
                </Text>
                <TextInput
                  style={[styles?.input, styles.disabledInput]}
                  value={
                    field.valueField === "country"
                      ? "MY"
                      : (value as string)
                  }
                  editable={false}
                  selectTextOnFocus={false}
                />
              </View>
            );
          })
        )}

        {/* Display uploaded files */}
        {formData.entityType && (
          <View style={styles.reviewSection}>
            {/* <Text style={styles.reviewSectionTitle}>Uploaded Documents</Text> */}
            {formData.entityType === "Limited Liability Company" && (
              <>
                {renderFileReview("form9", "Form 9 Business Registration form")}
                {renderFileReview("form24", "Form 24 Allotment of Shares")}
                {renderFileReview(
                  "form49",
                  "Form 49 Directors Particulars and Company Secretory Info"
                )}
                {renderFileReview("memArticle", "Memorandom of Articles M&A")}
                {renderFileReview("lan", "Latest Annual Returns")}
                {renderFileReview(
                  "bankStatement",
                  "Last 3 Month Bank Statements"
                )}
                {renderFileReview(
                  "icCard",
                  "Identity Cards of Directors-IC Card"
                )}
              </>
            )}
            {formData.entityType === "Sole Proprietor" && (
              <>
                {renderFileReview(
                  "formD",
                  "Form D from ROC(SSM) -Peninsular Dealer"
                )}
                {renderFileReview(
                  "form1",
                  "Form 1 and Biz registration certificate (Sabah Sarawak)"
                )}
                {renderFileReview(
                  "icCard",
                  "Identity Cards of Directors-IC Card"
                )}
              </>
            )}
          </View>
        )}
      </View>
    );
  };

  const renderFileReview = (field: string, label: string) => {
    const fileData = formData[field] as FileData | string;
    if (!fileData) return null;

    const handleFilePress = async (url: string) => {
      try {
        if (!url) {
          console.error("No URL provided to open");
          showError("Error", "File URL is missing");
          return;
        }

        const supported = await Linking.canOpenURL(url);
        if (supported) {
          await Linking.openURL(url);
        } else {
          showError("Error", "Cannot open this file type");
        }
      } catch (error) {
        console.error("Error opening file:", error);
        showError("Error", "Failed to open file");
      }
    };

    // Check if fileData is a string (URL) or FileData object
    let displayText = "Unknown file";
    let urlToOpen = "";

    if (typeof fileData === "string") {
      displayText = fileData.split("/").pop() || "File";
      urlToOpen = fileData;
    } else if (fileData && typeof fileData === "object" && "uri" in fileData) {
      displayText = fileData.name || "File";
      urlToOpen = fileData.uri;
    }

    return (
      <View key={field} style={styles.reviewField}>
        <Text style={styles.reviewLabel}>{label}</Text>
        <View style={styles.fileReviewContainer}>
          <View style={styles.fileIconContainer}>
            <Feather name="file" size={20} color="#1E60AE" />
          </View>
          <TouchableOpacity
            style={styles.fileNameContainer}
          // onPress={() => handleFilePress(urlToOpen)}
          >
            <Text
              style={styles.fileNameReview}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {displayText}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderInputField = (inputField: any, index: number) => {
    if (currentPosition === 2) {
      // Step 3
      return renderStep3Content();
    }

    if (currentPosition === 3) {
      // Step 4
      return renderStep4Content();
    }

    // Handle other steps
    const fieldName = inputField.fieldName || inputField.fieldLabel;
    // Ensure we handle null/undefined values properly with empty string fallback
    const fieldValue =
      formData[fieldName] !== null && formData[fieldName] !== undefined
        ? (formData[fieldName] as string)
        : "";

    // console.log(`Rendering field: ${fieldName}, value: ${fieldValue}`);

    return (
      <View key={index} style={styles?.inputContainer}>
        <Text style={styles?.inputLabel}>
          {inputField.label}
          {inputField.isMandatory ? "*" : ""}
        </Text>

        {fieldName === "phone" ? (
          <>
            <View style={styles.container}>
              <PhoneInput
                ref={phoneInput}
                defaultValue={getLocalPhoneNumber(phone, phoneInput.current?.getCallingCode())}
                defaultCode={countryCode as any}
                layout="second"
                value={getLocalPhoneNumber(phone, phoneInput.current?.getCallingCode())}
                onChangeText={(text) => {
                  let localNumber = text;
                  const dialCode = phoneInput.current?.getCallingCode() || '';
                  // Remove + and dial code if user types/pastes it
                  if (dialCode && localNumber.startsWith('+' + dialCode)) {
                    localNumber = localNumber.slice(('+' + dialCode).length);
                  }
                  // Also remove just the + if user types it
                  if (localNumber.startsWith('+')) {
                    localNumber = localNumber.slice(1);
                  }
                  // Remove leading zero if present
                  if (localNumber.startsWith('0')) {
                    localNumber = localNumber.slice(1);
                  }
                  setPhone(localNumber);
                  setFormData({
                    ...formData,
                    phone: localNumber,
                    countryCode: countryCode,
                  });
                  const fullNumber = dialCode ? `+${dialCode}${localNumber}` : localNumber;
                  setIsPhoneValid(!!phoneInput.current?.isValidNumber(fullNumber));
                }}
                onChangeFormattedText={() => {}}
                onChangeCountry={(country) => {
                  setCountryCode(country.cca2);
                  if (phone) {
                    // Get the new dial code
                    const dialCode = phoneInput.current?.getCallingCode() || '';
                    let localNumber = phone;
                    // Remove + and any previous dial code if present
                    if (localNumber.startsWith('+')) {
                      // Remove + and all digits up to the first non-digit (or up to 4 digits)
                      localNumber = localNumber.replace(/^\+\d{1,4}/, '');
                    }
                    // Remove leading zero if present
                    if (localNumber.startsWith('0')) {
                      localNumber = localNumber.slice(1);
                    }
                    setPhone(localNumber);
                    setFormData({
                      ...formData,
                      phone: localNumber,
                      countryCode: country.cca2,
                    });
                    // Validate for the new country
                    const fullNumber = dialCode ? `+${dialCode}${localNumber}` : localNumber;
                    setIsPhoneValid(!!phoneInput.current?.isValidNumber(fullNumber));
                  }
                }}
                containerStyle={styles.phoneContainer}
                textContainerStyle={styles.textInput}
                textInputStyle={styles.textInputOnly}
                codeTextStyle={styles.codeText}
                flagButtonStyle={styles.flag}
                textInputProps={{
                  placeholder: "Enter phone number",
                  selectionColor: "#3e9ea3",
                  style: {
                    backgroundColor: COLORS.background,
                    color: "#000000",
                  }
                }}
              /></View>
            {!isPhoneValid && (
              <Text style={{ color: "red", fontSize: 12, marginTop: 4 }}>
                Invalid phone number for selected country.
              </Text>
            )}
          </>
        ) : inputField.isInputDropdown ? (
          Platform.OS === "ios" ? (
            <SelectDropdown
              data={inputField.options || []}
              onSelect={(selectedItem: any) => {
                handleInputChange(fieldName, selectedItem.value);
              }}
              renderButton={(selectedItem: any, isOpened: boolean) => {
                return (
                  <View
                    style={[
                      styles.dropdownButtonStyle,
                      {
                        borderWidth: 1,
                        borderRadius: 10,
                        borderColor: "lightgray",
                        height: 45,
                        backgroundColor: COLORS.background,
                      },
                    ]}
                  >
                    <Text style={[styles.dropdownButtonTxtStyle, FONTS.fontSm]}>
                      {(selectedItem && selectedItem.label) ||
                        fieldValue ||
                        "Select an option"}
                    </Text>
                    <Image
                      source={ChevronICon}
                      style={[
                        styles.dropdownButtonArrowStyle,
                        {
                          width: 24,
                          height: 24,
                        },
                      ]}
                    />
                  </View>
                );
              }}
              renderItem={(item: any, index: number, isSelected: boolean) => {
                return (
                  <View
                    style={[
                      styles.dropdownItemStyle,
                      { backgroundColor: isSelected ? "#E7E7E7" : "white" },
                    ]}
                  >
                    <Text style={styles.dropdownItemTxtStyle}>
                      {item.label}
                    </Text>
                  </View>
                );
              }}
              showsVerticalScrollIndicator={false}
              dropdownStyle={styles.dropdownMenuStyle}
            />
          ) : (
            <>
              <Picker
                selectedValue={fieldValue || "Select type of your company"}
                onValueChange={(value) => handleInputChange(fieldName, value)}
                style={{ ...FONTS.fontSm, fontSize: 11, color: "#6C7278" }}
                dropdownIconColor="white"
              >
                {inputField.options?.map((option: any, idx: number) => (
                  <Picker.Item
                    key={idx}
                    label={option.label}
                    value={option.value}
                  />
                ))}
              </Picker>
              <Image
                source={ChevronICon}
                style={[styles.icon, { width: 24, height: 24 }]}
              />
            </>
          )
        ) : fieldName !== "state" && fieldName !== "country" ? (
          <>
            <TextInput
              style={styles?.input}
              value={fieldName === "country" ? "Malesia" : fieldValue}
              onChangeText={(value) => handleInputChange(fieldName, value)}
              keyboardType={
                fieldName === "phone" ||
                  inputField.type === "NUMBER" ||
                  fieldName === "pincode"
                  ? "numeric"
                  : "default"
              }
              editable={fieldName !== "email"}
              maxLength={
                fieldName === "phone"
                  ? 13
                  : fieldName === "pincode"
                    ? 5
                    : undefined
              }
              placeholder={fieldName === "phone" ? "+60 " : undefined}
            />
          </>
        ) : Platform.OS === "ios" ? (
          fieldName === "country" ? (
            <SelectDropdown
              data={CountryData}
              onSelect={(selectedItem: any) => {
                console.log("fieldName>>>", selectedItem.value);
                handleInputChange(fieldName, selectedItem.value);
              }}
              renderButton={(selectedItem: any, isOpened: boolean) => {
                return (
                  <View
                    style={[
                      styles.dropdownButtonStyle,
                      {
                        borderWidth: 1,
                        borderRadius: 10,
                        borderColor: "lightgray",
                        height: 45,
                        backgroundColor: COLORS.background,
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.dropdownButtonTxtStyle,
                        FONTS.fontSm,
                        { fontSize: 11, color: "#6C7278" },
                      ]}
                    >
                      {(selectedItem && selectedItem.label) ||
                        fieldValue ||
                        "Select Country"}
                    </Text>
                    <Image
                      source={ChevronICon}
                      style={[
                        styles.dropdownButtonArrowStyle,
                        {
                          width: 24,
                          height: 24,
                        },
                      ]}
                    />
                  </View>
                );
              }}
              renderItem={(item: any, index: number, isSelected: boolean) => {
                return (
                  <View
                    style={[
                      styles.dropdownItemStyle,
                      { backgroundColor: isSelected ? "#E7E7E7" : "white" },
                    ]}
                  >
                    <Text
                      style={[
                        styles.dropdownItemTxtStyle,
                        { fontSize: 12, color: "#6C7278" },
                      ]}
                    >
                      {item.label}
                    </Text>
                  </View>
                );
              }}
              showsVerticalScrollIndicator={false}
              dropdownStyle={styles.dropdownMenuStyle}
            />
          ) : fieldName === "state" ? (
            <SelectDropdown
              data={StateData}
              onSelect={(selectedItem: any) => {
                console.log("fieldName>>>", selectedItem.value);
                handleInputChange(fieldName, selectedItem.value);
              }}
              renderButton={(selectedItem: any, isOpened: boolean) => {
                return (
                  <View
                    style={[
                      styles.dropdownButtonStyle,
                      {
                        borderWidth: 1,
                        borderRadius: 10,
                        borderColor: "lightgray",
                        height: 45,
                        backgroundColor: COLORS.background,
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.dropdownButtonTxtStyle,
                        FONTS.fontSm,
                        { fontSize: 11, color: "#6C7278" },
                      ]}
                    >
                      {(selectedItem && selectedItem.label) ||
                        fieldValue ||
                        "Select state"}
                    </Text>
                    <Image
                      source={ChevronICon}
                      style={[
                        styles.dropdownButtonArrowStyle,
                        {
                          width: 24,
                          height: 24,
                        },
                      ]}
                    />
                  </View>
                );
              }}
              renderItem={(item: any, index: number, isSelected: boolean) => {
                return (
                  <View
                    style={[
                      styles.dropdownItemStyle,
                      { backgroundColor: isSelected ? "#E7E7E7" : "white" },
                    ]}
                  >
                    <Text
                      style={[
                        styles.dropdownItemTxtStyle,
                        { fontSize: 12, color: "#6C7278" },
                      ]}
                    >
                      {item.label}
                    </Text>
                  </View>
                );
              }}
              showsVerticalScrollIndicator={false}
              dropdownStyle={styles.dropdownMenuStyle}
            />
          ) : null
        ) : (
          <View
            style={{
              borderWidth: 1,
              borderRadius: 10,
              borderColor: "lightgray",
              ...FONTS.fontSm,
              height: 45,
            }}
          >
            <Picker
              selectedValue={fieldValue}
              onValueChange={(value) => {
                console.log("fieldName>>>", value);
                handleInputChange(fieldName, value);
              }}
              dropdownIconColor="black"
              style={{ ...FONTS.fontSm, fontSize: 11, color: "#6C7278" }}
            >
              {fieldName === "country" &&
                CountryData?.map((option: any, idx: number) => (
                  <Picker.Item
                    key={idx}
                    style={{ fontSize: 12, color: "#6C7278" }}
                    label={option.label}
                    value={option.value}
                  />
                ))}

              {fieldName === "state" &&
                StateData?.map((option: any, idx: number) => (
                  <Picker.Item
                    key={idx}
                    style={{ fontSize: 12, color: "#6C7278" }}
                    label={option.label}
                    value={option.value}
                  />
                ))}
            </Picker>
          </View>
        )}
        {fieldName === "pincode" && zipValidation.message ? (
          <Text
            style={{
              ...FONTS.fontSm,
              color: zipValidation.isValid ? "green" : "red",
              marginTop: 5,
            }}
          >
            {zipValidation.message}
          </Text>
        ) : null}
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      {isPageLoading ? (
        <>
          <View style={styles.container}>
            <ActivityIndicator size="large" color={COLORS.primary} />
          </View>
        </>
      ) : (
        <View style={styles.container}>
          <View style={styles.headerContainer}>
            <HeaderStyle4 title={companyDetails.headerTitle}  />
            <Text style={styles.pageDescription}>
              {companyDetails.pageDescription}
            </Text>
            <CustomStepIndicator
              currentPosition={currentPosition}
              stepCount={companyDetails.steps.length}
            />
          </View>

          <View style={styles.formContainer}>
            <View>
              <Text style={styles.stepLabel}>
                {companyDetails.steps[currentPosition]?.title}
              </Text>
            </View>
            <ScrollView
              style={{ flex: 1 }}
              showsVerticalScrollIndicator={false}
            >
              {currentPosition === 2
                ? renderStep3Content()
                : currentPosition === 3
                  ? renderStep4Content()
                  : companyDetails.steps[currentPosition]?.input.map(
                    renderInputField
                  )}
            </ScrollView>

            <View
              style={[
                styles.buttonContainer,
                currentPosition > 0 && {
                  flexDirection: "row",
                  gap: 10,
                  justifyContent: "center",
                },
              ]}
            >
              {currentPosition > 0 && (
                <TouchableOpacity
                  onPress={handleBack}
                  style={styles.backButton}
                  disabled={isLoading}
                >
                  <Text style={styles.backLabel}>Back</Text>
                </TouchableOpacity>
              )}
              {currentPosition === companyDetails.steps.length - 1 ? (
                <TouchableOpacity
                  onPress={async () => {
                    await AsyncStorage.setItem(
                      "isCompanyFormSubmitted",
                      "true"
                    );
                    navigation.dispatch(
                      CommonActions.reset({
                        index: 0,
                        routes: [{ name: "ApplicationSubmitted" }],
                      })
                    );
                  }}
                  style={[
                    styles.continueButton,
                    {
                      width: currentPosition > 0 ? "50%" : "100%",
                      alignSelf: "center",
                      borderWidth: 1,
                      borderColor: "#1E60AE",
                      backgroundColor: "#1E60AE",
                      justifyContent: "center",
                      alignItems: "center",
                      borderRadius: 8,
                      height: 56,
                      opacity: isStepValid() ? 1 : 0.5,
                    },
                  ]}
                  disabled={isLoading || !isStepValid()}
                >
                  {isLoading ? (
                    <ActivityIndicator color="#FFF" />
                  ) : (
                    <Text style={styles.continueLabel}>{staticLabels?.submit}</Text>
                  )}
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  onPress={handleNext}
                  style={[
                    styles.continueButton,
                    {
                      width: currentPosition > 0 ? "50%" : "100%",
                      opacity: isStepValid() ? 1 : 0.5,
                    },
                  ]}
                  disabled={isLoading || !isStepValid()}
                >
                  {isLoading ? (
                    <ActivityIndicator color="#FFF" />
                  ) : (
                    <Text style={styles.continueLabel}>{locale === "en" ? "Continue" : "Teruskan"}</Text>
                  )}
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
      )}

      {/* Custom Alert */}
      {alertConfig && (
        <CustomAlert
          visible={alertVisible}
          title={alertConfig.title}
          message={alertConfig.message}
          type={alertConfig.type}
          buttons={alertConfig.buttons}
          onClose={hideAlert}
        />
      )}
    </KeyboardAvoidingView>
  );
};

const StepDot = ({ index, active, completed }: any) => {
  const backgroundColor = active
    ? "#1E60AE"
    : completed
      ? "#222222"
      : "#FFFFFF";

  const textColor = active || completed ? "#FFF" : "#000";
  const borderColor = active ? "#1E60AE" : completed ? "#222222" : "#D3D3D3";

  return (
    <View
      style={[
        styles.stepDot,
        {
          backgroundColor,
          borderColor,
        },
      ]}
    >
      <Text style={[styles.dotLabel, { color: textColor }]}>{index + 1}</Text>
    </View>
  );
};

const DottedLine = ({ completed }: any) => {
  return (
    <View style={styles.dottedLine}>
      {Array.from({ length: 3 }).map((_, index) => (
        <View
          key={index}
          style={[
            styles.dot,
            {
              backgroundColor: completed ? "black" : "gray",
              // marginHorizontal: 3,
            },
          ]}
        />
      ))}
    </View>
  );
};

const CustomStepIndicator = ({ currentPosition, stepCount }: any) => {
  return (
    <View style={styles.containerNew}>
      <View style={styles.row}>
        {Array.from({ length: stepCount }).map((_, index) => (
          <React.Fragment key={index}>
            <StepDot
              index={index}
              active={index === currentPosition}
              completed={index < currentPosition}
            />
            {index < stepCount - 1 && (
              <DottedLine completed={index < currentPosition} />
            )}
          </React.Fragment>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    padding: 20,
    gap: 10,
  },
  pageDescription: {
    ...FONTS.fontRegular,
    textAlign: "center",
    color: "#808080",
  },
  formContainer: {
    flex: 1,
    padding: 30,
    gap: 10,
    borderWidth: 1,
    borderColor: "#E7E7E7",
    borderTopLeftRadius: 32,
    borderTopRightRadius: 32,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    // shadowOpacity: 0.23,
    shadowRadius: 5.46,
    elevation: 4,
    overflow: "hidden",
  },
  stepLabel: {
    ...FONTS.fontMedium,
    fontSize: 20,
  },
  inputContainer: {
    marginBottom: 15,
  },
  inputLabel: {
    ...FONTS.fontSm,
    marginBottom: 5,
    color: "#6C7278",
  },
  input: {
    borderWidth: 1,
    borderRadius: 10,
    borderColor: "lightgray",
    fontSize:13,
    fontFamily: Platform.OS === 'android' ? 'DMSans' : undefined,
    padding: 10,
  },
  buttonContainer: {
    marginTop: 20,
  },
  continueButton: {
    backgroundColor: "#1E60AE",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    height: 56,
  },
  continueLabel: {
    ...FONTS.font,
    color: "#FFF",
    fontSize: 16,
    ...SORA.SoraRegular,
  },
  backButton: {
    backgroundColor: "#FFF",
    borderWidth: 1,
    borderColor: "#1E60AE",
    width: "50%",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    height: 56,
  },
  backLabel: {
    color: "#1E60AE",
    fontWeight: "600",
    fontSize: 16,
    ...SORA.SoraRegular,
  },
  containerNew: {
    paddingVertical: 16,
    alignItems: "center",
  },
  row: {
    flexDirection: "row",
    width: "50%",
    justifyContent: "space-around",
    alignItems: "center",
  },
  stepDot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  dotLabel: {
    color: "#FFF",
    fontSize: 10,
  },
  dottedLine: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: 5,
    gap: 1,
  },
  dot: {
    width: 3,
    height: 3,
    borderRadius: 34,
    marginHorizontal: 1,
  },
  pickerContainer: {
    position: "relative",
    borderWidth: 1,
    borderColor: "lightgray",
    borderRadius: 5,
    marginBottom: 10,
    overflow: "hidden",
  },
  picker: {
    height: 40,
    width: "100%",
    // backgroundColor:"red",
    borderWidth: 10,
  },
  icon: {
    position: "absolute",
    right: 10,
    top: "65%",
    transform: [{ translateY: -10 }],
    color: "#333",
    pointerEvents: "none",
  },
  fileUploadContainer: {
    marginBottom: 20,
  },
  uploadButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1,
    borderColor: "gray",
    borderRadius: 8,
    padding: 12,
    marginTop: 8,
  },
  disabledUploadButton: {
    opacity: 0.5,
  },
  uploadText: {
    marginLeft: 8,
    ...FONTS.font,
    color: "#1E60AE",
  },
  fileSizeText: {
    ...FONTS.fontSm,
    color: "gray",
    textAlign: "center",
    marginTop: 4,
  },
  filePreview: {
    marginTop: 15,
    backgroundColor: Platform.OS === "android" ? "#F5F5F5" : "",
    borderRadius: 8,
    // padding: 8,
    // borderWidth: 1,
  },
  filePreviewRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  fileInfoContainer: {
    flexDirection: "row",
    alignItems: "center",
    // flex: 1,
    gap: 8,
    width: "80%",
  },
  fileName: {
    flex: 1,
    ...FONTS.font,
    color: "#1E60AE",
  },
  reviewContainer: {
    padding: 16,
  },
  reviewSection: {
    // marginTop: 20,
    borderTopWidth: 1,
    borderTopColor: "#EDF1F3",
    paddingTop: 16,
  },
  reviewSectionTitle: {
    ...FONTS.fontMedium,
    fontSize: 18,
    marginBottom: 16,
    color: "#1E60AE",
  },
  reviewField: {
    marginBottom: 16,
  },
  reviewLabel: {
    ...FONTS.fontSm,
    color: "#6C7278",
    marginBottom: 4,
  },
  reviewValue: {
    ...FONTS.font,
    color: "#222222",
    fontSize: 16,
  },
  fileReviewContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    padding: 12,
    borderRadius: 8,
    marginTop: 4,
  },
  fileIconContainer: {
    marginRight: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  fileNameContainer: {
    // flex: 1,
    width: "80%",
  },
  fileNameReview: {
    ...FONTS.font,
    color: "#1E60AE",
    textDecorationLine: "underline",
  },
  disabledInput: {
    backgroundColor: "#F5F5F5",
    color: "#222222",
    borderColor: "lightgray",
  },
  stepTitle: {
    ...FONTS.fontMedium,
    fontSize: 18,
    color: "#1E60AE",
    marginTop: 20,
    marginBottom: 10,
  },
  // SelectDropdown styles
  dropdownButtonStyle: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  dropdownButtonTxtStyle: {
    flex: 1,
    fontSize: 14,
    color: "#6C7278",
  },
  dropdownButtonArrowStyle: {
    marginLeft: 8,
  },
  dropdownMenuStyle: {
    backgroundColor: "white",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E7E7E7",
  },
  dropdownItemStyle: {
    marginBottom: 10,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#F0F0F0",
  },
  dropdownItemTxtStyle: {
    fontSize: 14,
    color: "#333333",
  },
  phoneContainer: {
    width: "100%",
    height: 42,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#ccc",
    backgroundColor: COLORS.background,
  },
  textInput: {
    paddingVertical: 0,
    backgroundColor: COLORS.background,
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
    color: COLORS.background
  },
  textInputOnly: {
    fontSize: 16,
    color: "#333",
  },
  codeText: {
    fontSize: 14,
    color: "#000",
  },
  flag: {
    marginLeft: 8,
  },
});
export default CompanyRegistration;
