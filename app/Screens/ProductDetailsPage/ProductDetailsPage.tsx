import {
  View,
  Text,
  ScrollView,
  SafeAreaView,
  Image,
  Dimensions,
  TouchableOpacity,
  Animated,
  Platform,
  UIManager,
  Easing,
  Share,
  TextInput,
  StyleProp,
  TextStyle,
  Keyboard,
} from "react-native";
import React, { useCallback, useEffect, useRef, useState } from "react";
import Header from "../../layout/Header";
import {
  ButtonLabel,
  COLORS,
  DMSansFONTS,
  FONTS,
  FONTWEIGHT,
  SIZES,
} from "../../constants/theme";
import CustomAlert from "../../components/CustomAlert";
import { useCustomAlert } from "../../hooks/useCustomAlert";
import ScrollIndicator from "../../components/ScrollIndicator/ScrollIndicator";
import Badge from "../../components/Badge/Badge";
import Button from "../../components/Button/Button";
import QuantitySelector from "../../components/QuantitySelector/QuantitySelector";
import { white } from "react-native-paper/lib/typescript/styles/themes/v2/colors";
import { useMutation, useQuery } from "@apollo/client";
import {
  GET_CATEGORY_PRODUCTS,
  GET_SINGLE_PRODUCT,
} from "../../api/viewCategoryProductQuery";
import { IMAGES } from "../../constants/Images";
import SizeGuideIcon from "../../assets/icons/size.png";
import BottomSheetNew from "../../components/BottomSheetNew/BottomSheetNew";
import BackArrow from "../../assets/icons/arrowback.png";
import ForwardArrow from "../../assets/icons/arrowforward.png";
import { ActivityIndicator } from "react-native-paper";
import VariantCardsContainer from "../../components/VariantCardsContainer/VariantCardsContainer";
import { useDispatch, useSelector } from "react-redux";
import { ADD_LINES_TO_CART, CREATE_CART } from "../../api/cartQuery";
import { setCartId } from "../../redux/reducer/cartReducer";
import "react-native-gesture-handler";
import { LanguageState } from "@/app/redux/reducer/languageReducer";
import { staticTranslations } from "@/app/constants/staticTranslations";
if (
  Platform.OS === "android" &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
type AccordianProps = {
  navigation?: any;
  menuItems: any;
  styles?: any;
  accordianWithoutImg?: any;
};
export default function ProductDetailsPage({ navigation, route }: any) {
  const params = route?.params;
  const scrollRef = useRef(null);
  const { width, height } = Dimensions.get("window");
  const [modalVisible, setModalVisible] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const { locale }: LanguageState = useSelector((state: any) => state.language);
  const staticLabels = locale === "en" ? staticTranslations.english : staticTranslations.malay;
  const [filterSortOptions, setFilterSortOptions] = useState(false);
  const [createCart, { loading: createLoading }] = useMutation(CREATE_CART);
  const [addCart, { loading: addLoading }] = useMutation(ADD_LINES_TO_CART);
  const dispatch = useDispatch();
  const { cartId } = useSelector((state: any) => state.cart);
  const [quantity, setQuantity] = useState(0);
  const screenHeight = Dimensions.get("screen").height;
  const { width: screenWidth } = Dimensions.get("window");
  const [isLoading, setIsLoading] = useState(false);
  const [variantLoading, setVariantLoading] = useState(false);
  // Custom Alert Hook
  const {
    alertConfig,
    visible: alertVisible,
    showSuccess,
    showError,
    hideAlert,
  } = useCustomAlert();
  if (!params?.handle) return null;

  const { loading, data, error } = useQuery(GET_SINGLE_PRODUCT, {
    variables: { handle: params?.handle },
  });
  const [selectedSize, setSelectedSize] = useState<string | null>(null);
  const [activeSizeIndex, setActiveSizeIndex] = useState<number | null>(null);
  const [availableSizes, setAvailableSizes] = useState<any[]>([]);
  const [selectedVariantId, setSelectedVariantId] = useState();
  const [selectedColor, setSelectedColor] = useState<string | null>(null);
  const [keyboardOpen, setKeyboardOpen] = useState(false);
  const SCREEN_WIDTH = Dimensions.get("window").width;
  const COLOR_CARD_WIDTH = SCREEN_WIDTH / 4.0; // Adjust ratio for 2–3 columns per row
  // 🔁 Update available sizes based on selected color
  useEffect(() => {
    if (!selectedVariantId) return;

    const selectedVariant = productData?.variants?.find(
      (v: any) => v.id === selectedVariantId
    );

    const selectedColor = selectedVariant?.selectedOptions?.find(
      (o: any) => o.name.toLowerCase() === "color"
    )?.value;

    const matchingVariants = productData?.variants?.filter((variant: any) => {
      const color = variant.selectedOptions?.find(
        (o: any) => o.name.toLowerCase() === "color"
      )?.value;
      return color === selectedColor;
    });

    const sizes = matchingVariants.map((variant: any) => {
      const size = variant.selectedOptions?.find(
        (o: any) => o.name.toLowerCase() === "size"
      )?.value;
      return {
        size,
        available: variant.availableForSale,
        variantId: variant.id,
      };
    });

    const uniqueSizes = sizes.filter(
      (s, index, self) =>
        s.size && index === self.findIndex((t) => t.size === s.size)
    );

    setAvailableSizes(uniqueSizes);
  }, [selectedVariantId, productData]);
  const scrollX = useRef(new Animated.Value(0)).current;
  if (error) {
    return <Text>{error?.message}</Text>;
  }
  useEffect(() => {
    const showSub = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillShow" : "keyboardDidShow",
      () => setKeyboardOpen(true)
    );
    const hideSub = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillHide" : "keyboardDidHide",
      () => setKeyboardOpen(false)
    );

    return () => {
      showSub.remove();
      hideSub.remove();
    };
  }, []);
  const product = data?.productByHandle;

  const productData: {
    id: string;
    title: string;
    handle: string;
    description: string;
    images: any[];
    variants: any[];
    selectedVariantId: string | undefined;
    sizes: string[];
    tags: string[];
  } = {
    id: product?.id || "",
    title: product?.title || "",
    handle: product?.handle || "",
    description: product?.description || "",
    images: product?.images?.edges?.map((edge: any) => edge.node) || [],
    variants: product?.variants?.edges?.map((edge: any) => edge.node) || [],
    selectedVariantId: product?.selectedOrFirstAvailableVariant?.id,
    sizes:
      product?.variants?.edges
        ?.map((edge: any) =>
          edge?.node?.selectedOptions?.find(
            (option: any) => option.name.toLowerCase() === "size"
          )?.value
        )
        .filter(Boolean)
        .filter((value: string, index: number, self: string[]) => self.indexOf(value) === index) || [],
    tags: Array.isArray(product?.tags) ? product.tags : [],
  };
  const isPreorder = productData.tags && productData.tags.includes("preorder");
  const accodianData = [
    {
      title: "Product Decripion",
      description: productData.description,
    },
    {
      title: "Specifications",
      description: productData.description,
    },
    {
      title: "Material & Care",
      description: productData.description,
    },
  ];
  const handleScrollTo = (direction: "left" | "right") => {
    let newIndex = activeIndex + (direction === "left" ? -1 : 1);
    newIndex = Math.max(0, Math.min(productData?.images.length - 1, newIndex)); // Clamp index
    setActiveIndex(newIndex);
    scrollRef.current?.scrollTo({ x: newIndex * width, animated: true });
  };


  // const handleVariantChange = useCallback(
  //   (id: string) => {
  //     setIsLoading(true)
  //     setTimeout(() => {
  //       setIsLoading(false)
  //     }, 400)
  //     const variant = productData.variants.find(
  //       (variant: any) => id === variant.id
  //     );
  //     if (variant) {
  //       setSelectedVariantId(variant.id);
  //     }
  //   },
  //   [productData, selectedVariantId]
  // );

  const handleAddToBag = () => {
    setIsLoading(true);
    const lines = [{
      quantity,
      merchandiseId: selectedVariantId,
      attributes: selectedSize ? [{ key: "Size", value: selectedSize }] : [],
    }];
    if (!selectedSize) {
      setIsLoading(false)
      return showError(
        "Error",
        "Select the size"
      );
    }
    if (cartId) {
      addCart({
        variables: {
          cartId,
          lines: lines,
        },
      })
        .then((response: any) => {
          if (response.data.cartLinesAdd.userErrors.length > 0) {
            setIsLoading(false);
            showError(
              "Error",
              response.data.cartLinesAdd.userErrors[0].message
            );
          } else {
            setIsLoading(false);
            showSuccess("Success", staticLabels?.Added_tocart_successfully, [
              {
                text: "OK",
                onPress: () => {
                  const cartId = response.data.cartLinesAdd.cart.id;
                  // console.log("Cart Id: ", cartId);
                  dispatch(setCartId(cartId));
                  navigation.push("MyCart", {
                    cartId,
                    lines,
                  });
                  // closeBottomSheet();
                },
              },
            ]);
            // closeBottomSheet()
          }
        })
        .catch((err) => {
          setIsLoading(false);
          showError("Error", "Something went wrong. Please try again.");
          console.error("Cart addition error:", err);
        });
    } else {
      createCart({
        variables: {
          lines,
        },
      })
        .then((response: any) => {
          if (response.data.cartCreate.userErrors.length > 0) {
            setIsLoading(false);
            showError("Error", response.data.cartCreate.userErrors[0].message);
          } else {
            setIsLoading(false);
            showSuccess("Success", staticLabels?.Added_tocart_successfully, [
              {
                text: "OK",
                onPress: () => {
                  const cartId = response.data.cartCreate.cart.id;
                  // console.log("Cart Id: ", cartId);
                  dispatch(setCartId(cartId));
                  navigation.push("MyCart", {
                    cartId,
                    lines,
                  });
                  // closeBottomSheet();
                },
              },
            ]);
            // closeBottomSheet()
          }
        })
        .catch((err) => {
          setIsLoading(false);
          showError("Error", "Something went wrong. Please try again.");
          console.error("Cart creation error:", err);
        });
    }
  };

  const handleIncrease = () => {
    setQuantity((pre: number) => pre + 1);
  };

  const handleDecrease = () => {

    console.log("ProductDetailsPage handleDecrease called");
    setQuantity((pre: number) => {
      if (pre <= 1) {
        return 0;
      }
      return pre - 1;
    });
  };

  const handleShare = async () => {
    try {
      const productUrl = `https://sunrise-trade.myshopify.com/products/${productData?.handle}`;
      const price =
        productData.variants.find(
          (variant: any) => variant.id === selectedVariantId
        )?.price.amount || "N/A";

      // Include URL in message for better compatibility across platforms
      const shareMessage = `${productData?.title || "Amazing Product"}\n\n${productData?.description || "Great product available now!"}\n\nPrice: $${price}\n\nView Product: ${productUrl}`;

      const shareContent = {
        title: productData?.title || "Check out this product!",
        message: shareMessage,
        url: productUrl, // iOS will use this
      };

      const result = await Share.share(shareContent);
      console.log("result.>>", result);
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // Shared via activity type
          console.log("Shared via:", result.activityType);
        } else {
          // Shared
          console.log("Product shared successfully");
        }
      } else if (result.action === Share.dismissedAction) {
        // Dismissed
        console.log("Share dismissed");
      }
    } catch (error) {
      console.error("Error sharing product:", error);
      showError("Error", "Unable to share product. Please try again.");
    }
  };

  const handleVariantChange = (variantId: string) => {
    setVariantLoading(true);
    const variant = productData.variants.find((v: any) => v.id === variantId);
    if (variant) {
      const color = variant.selectedOptions?.find(
        (o: any) => o.name.toLowerCase() === "color"
      )?.value;
      setSelectedColor(color);
    }
    setSelectedVariantId(variantId);
    setSelectedSize(null);
    setActiveSizeIndex(null);
    setTimeout(() => setVariantLoading(false), 400); // Simulate loading
  };

  const handleSizeChange = (size: string, index: number) => {
    setVariantLoading(true);
    setSelectedSize(size);
    setActiveSizeIndex(index);

    const matched = productData.variants.find((variant: any) => {
      const variantSize = variant.selectedOptions?.find(
        (o: any) => o.name.toLowerCase() === "size"
      )?.value;
      const variantColor = variant.selectedOptions?.find(
        (o: any) => o.name.toLowerCase() === "color"
      )?.value;

      const currentColor = productData.variants.find(
        (v: any) => v.id === selectedVariantId
      )?.selectedOptions?.find((o: any) => o.name.toLowerCase() === "color")
        ?.value;

      return (
        variantSize === size &&
        variantColor === currentColor
      );
    });

    if (matched?.id) {
      setSelectedVariantId(matched.id);
    }
    setTimeout(() => setVariantLoading(false), 400); // Simulate loading
  };
  // Helper to get unique color variants
  const uniqueColorVariants = React.useMemo(() => {
    const seenColors = new Set();
    return productData.variants.filter((variant: any) => {
      console.log("varientcolor", variant)
      const color = variant.selectedOptions?.find(
        (o: any) => o.name.toLowerCase() === "color"
      )?.value;
      if (!color || seenColors.has(color)) return false;
      seenColors.add(color);
      return true;
    });
  }, [productData.variants]);

  // After building productData:
  const colorSizeMap: Record<string, { size: string; quantity: number; variantId: string }[]> = {};
  const uniqueColorTracker: Record<string, boolean> = {};
  const uniqueColors: { color: string; image: string | null; variantId: string }[] = [];

  productData.variants.forEach((variant: any) => {
    const color = variant.selectedOptions.find((o: any) => o.name.toLowerCase() === "color")?.value;
    const size = variant.selectedOptions.find((o: any) => o.name.toLowerCase() === "size")?.value;
    if (!color || !size) return;
    if (!colorSizeMap[color]) colorSizeMap[color] = [];
    colorSizeMap[color].push({
      size,
      quantity: variant.quantityAvailable,
      variantId: variant.id,
    });
    if (!uniqueColorTracker[color]) {
      uniqueColorTracker[color] = true;
      uniqueColors.push({
        color,
        image: variant?.image?.src || null,
        variantId: variant.id,
      });
    }
  });

  // After building uniqueColors, set default selection on mount
  useEffect(() => {
    if (uniqueColors.length > 0 && !selectedColor && !selectedVariantId) {
      setSelectedColor(uniqueColors[0].color);
      setSelectedVariantId(uniqueColors[0].variantId);
      setSelectedSize(null);
      setActiveSizeIndex(null);
    }
  }, [uniqueColors, selectedColor, selectedVariantId]);

  // Helper to get the image for the selected variant
  const selectedVariant = productData.variants.find(
    (v: any) => v.id === selectedVariantId
  );
  const mainImage = selectedVariant?.image?.src || productData?.images?.[0]?.src || null;
  const galleryImages = selectedVariant?.image?.src
    ? [selectedVariant.image.src, ...productData.images.filter((img: any) => img.src !== selectedVariant.image.src).map((img: any) => img.src)]
    : productData.images.length > 0 ? productData.images.map((img: any) => img.src) : ["https://t3.ftcdn.net/jpg/02/01/90/26/360_F_201902639_gZhcd695qjLUnVtJPXqlWpxNj5TvVHw2.jpg"];
  const defaultImage = "https://t3.ftcdn.net/jpg/02/01/90/26/360_F_201902639_gZhcd695qjLUnVtJPXqlWpxNj5TvVHw2.jpg"
  return (
    <SafeAreaView style={{ backgroundColor: COLORS.background, flex: 1 }}>
      <Header
        title={staticLabels?.proudct_details}
        // rightIcon={"cart"}
        leftIcon={"back"}
        titleLeft
        paddingLeft
      />
      {loading ? (
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            height: 600,
          }}
        >
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      ) : (
        <>
          <ScrollView showsVerticalScrollIndicator={false}>
            <View
              style={{
                alignItems: "center",
                // marginTop: 20,
                marginHorizontal: 10,
              }}
            >
              {
                isPreorder && (
                  <Badge
                    title="Pre order"
                    color={COLORS.badgeBackgroundColor}
                    size="md"
                    style={{
                      position: "absolute",
                      zIndex: 10000,
                      borderRadius: 8,
                      top: Platform.OS === "ios" ? 8 : 5,
                      left: width * 0.03, // 3% from left
                      paddingHorizontal: width * 0.015,
                      paddingVertical: 4,
                    }}
                  />
                )
              }
              <ScrollView
                horizontal
                pagingEnabled
                ref={scrollRef}
                showsHorizontalScrollIndicator={false}
                scrollEventThrottle={16}
                contentContainerStyle={{
                  width: width * galleryImages.length,
                }}
                onScroll={Animated.event(
                  [{ nativeEvent: { contentOffset: { x: scrollX } } }],
                  {
                    useNativeDriver: false,
                    listener: (event: any) => {
                      const xOffset = event.nativeEvent.contentOffset.x;
                      const newIndex = Math.round(xOffset / width);
                      setActiveIndex(newIndex);
                    },
                  }
                )}
              >
                {galleryImages.map((imgSrc: string, index: number) => (
                  <Image
                    key={index}
                    source={{ uri: imgSrc }}
                    style={{
                      width: width - 20,
                      height: 380,
                      objectFit: "fill",
                      borderRadius: 15,
                    }}
                  />
                ))}
                {
                  galleryImages.length <= 0 &&
                  <Image
                    source={{ uri: "https://t3.ftcdn.net/jpg/02/01/90/26/360_F_201902639_gZhcd695qjLUnVtJPXqlWpxNj5TvVHw2.jpg" }}
                    style={{
                      width: width - 20,
                      height: 380,
                      objectFit: "contain",
                      borderRadius: 15,
                    }}
                  />
                }
              </ScrollView>
              <ScrollIndicator
                images={galleryImages.map((src) => ({ src }))}
                activeIndex={activeIndex}
                styles={{ top: 340, marginTop: 20 }}
              />
              <TouchableOpacity
                onPress={() => {
                  setModalVisible(true);
                }}
                style={{
                  flexDirection: "row",
                  marginTop: 10,
                  position: "absolute",
                  top: 300,
                  right: 10,
                  backgroundColor: "white",
                  padding: 10,
                  borderRadius: 20,
                }}
              >
                <Image source={IMAGES.zoom} style={{ width: 25, height: 25 }} />
              </TouchableOpacity>
            </View>
            <View style={{ width: "100%" }}>
              <View
                style={{
                  gap: 10,
                  borderTopLeftRadius: 30,
                  borderTopRightRadius: 30,
                  zIndex: 10000,
                  backgroundColor: COLORS.backgroundColor,
                  padding: 5,
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <View>
                    <Text
                      style={{
                        color: COLORS.textBrandName,
                        marginHorizontal: 10,
                        marginTop: 10,
                        ...FONTS.fontSemiBoldPop,
                      }}
                    >
                      Yonex
                    </Text>
                  </View>
                  <TouchableOpacity onPress={handleShare}>
                    <Image
                      source={IMAGES.share}
                      style={{
                        width: 24,
                        height: 24,
                        marginHorizontal: 15,
                        marginTop: 6,
                      }}
                    />
                  </TouchableOpacity>
                </View>
                <View style={{}}>
                  <Text
                    style={{
                      fontSize: SIZES.h22,
                      lineHeight: 25,
                      marginHorizontal: 10,
                      fontFamily: "DMSansMedium",
                      // ...FONTWEIGHT.Normal
                    }}
                  >
                    {productData?.title}
                  </Text>
                </View>
                <View
                  style={{
                    flexDirection: "row",
                    gap: 10,
                    alignItems: "center",
                  }}
                >
                  <View style={{}}>
                    <View
                      style={{
                        flexDirection: "row",
                        gap: 10,
                        alignItems: "center",
                      }}
                    >
                      <Text
                        style={{
                          fontSize: SIZES.h22,
                          marginLeft: 10,
                          fontFamily: "DMSansExtraBold",
                        }}
                      >
                        $
                        {productData.variants.find(
                          (variant: any) => variant.id === selectedVariantId
                        )?.price.amount || "N/A"}
                      </Text>
                      <Text
                        style={{
                          color: COLORS.gray,
                          textDecorationLine: "line-through",
                          fontFamily: "DMSansRegular",
                          fontSize: SIZES.font,
                        }}
                      >
                        $186.00
                      </Text>
                      <Text
                        style={{
                          ...FONTS.fontXs,
                          color: COLORS.red,
                          ...FONTS.fontTitle,
                        }}
                      >{`(30% Off)`}</Text>
                    </View>
                    <View>
                      <Text
                        style={{
                          marginHorizontal: 10,
                          fontFamily: "DMSansRegular",
                          fontSize: SIZES.fontXxs,
                        }}
                      >
                        {staticLabels.inclAllTaxes}
                      </Text>
                    </View>
                  </View>
                </View>
                <View style={{ marginTop: 20 }}>
                  <View style={{ flexDirection: "row", marginHorizontal: 10 }}>
                    <Text
                      style={{ fontFamily: "DMSansBold", fontSize: SIZES.font }}
                    >
                      {staticLabels?.choose_Color}
                    </Text>
                    <Text
                      style={{
                        fontWeight: "400",
                        color: "#1C1B1B",
                        fontSize: SIZES.font,
                      }}
                    >{`  (${uniqueColors?.length} Colors)`}</Text>
                  </View>

                  <View
                    style={{
                      marginTop: 20,
                      marginHorizontal: 10,
                    }}
                  >
                    <ScrollView
                      horizontal
                      ref={scrollRef}
                      showsHorizontalScrollIndicator={false}
                      scrollEventThrottle={16}
                      contentContainerStyle={{ gap: 10 }}
                    >
                      {/* <VariantCardsContainer
                        variants={uniqueColors}
                        selectedVariantId={selectedVariantId}
                        handleVariantChange={handleVariantChange}
                      /> */}

                      {/* <View style={{ gap: 8, flexDirection: "row" }}>
                        {productData?.variants.map(
                          (variant: any, index: any) => {
                            console.log("single varient data", variant);
                            return (
                              <TouchableOpacity
                                key={index}
                                style={{
                                  alignItems: "center",
                                  // marginHorizontal: 5,
                                  borderWidth: 0.1,
                                  borderColor: COLORS.gray,
                                  // padding: 10,
                                  borderRadius: 10,
                                  height: 100,
                                }}
                              >
                                <Image
                                  source={{ uri: variant?.image.src }}
                                  style={{
                                    width: 80,
                                    height: 80,
                                    borderWidth: 1,
                                    aspectRatio: 1 / 1,
                                    objectFit: "contain",
                                    borderRadius: 15,
                                  }}
                                />
                                <View>
                                  <Text
                                    style={{
                                      ...FONTS.font,
                                      ...FONTWEIGHT.SemiBold,
                                    }}
                                  >
                                    {variant?.title}
                                  </Text>
                                </View>
                              </TouchableOpacity>
                            );
                          }
                        )}
                      </View> */}
                      <View style={{ flexDirection: "row", flexWrap: "wrap", justifyContent: "space-between" }}>
                        {uniqueColors?.map((colorEntry, idx) => (
                          <View key={colorEntry.variantId} style={{ width: COLOR_CARD_WIDTH, marginBottom: 16 }}>
                            <TouchableOpacity
                              onPress={() => handleVariantChange(colorEntry.variantId)}
                              style={{
                                alignItems: "center",
                                borderRadius: 10,
                                padding: 8,
                                gap: 8
                              }}
                            >
                              <Image
                                source={{ uri: colorEntry.image ?? "https://t3.ftcdn.net/jpg/02/01/90/26/360_F_201902639_gZhcd695qjLUnVtJPXqlWpxNj5TvVHw2.jpg" }}
                                style={{
                                  borderColor: "#222222",
                                  borderWidth: selectedColor === colorEntry.color ? 1 : 0,
                                  width: COLOR_CARD_WIDTH - 20,
                                  height: COLOR_CARD_WIDTH - 20,
                                  borderRadius: 10,
                                  aspectRatio: 1,
                                  objectFit: "fill",
                                }}
                              />
                              <Text style={{ ...FONTS.fontXs, textAlign: "center", marginTop: 6 }}>
                                {colorEntry.color}
                              </Text>
                            </TouchableOpacity>
                          </View>
                        ))}
                      </View>
                    </ScrollView>
                  </View>

                </View>

                {availableSizes.length > 0 && (
                  <View style={{ marginTop: 10, marginHorizontal: 10 }}>
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "space-between",
                        alignItems: "center",
                      }}
                    >
                      <Text
                        style={{
                          fontFamily: "DMSansBold",
                          fontSize: SIZES.font,
                        }}
                      >
                        {staticLabels?.choose_size}
                      </Text>
                      {/* <View style={{ flexDirection: "row", gap: 3 }}>
                        <Image
                          source={SizeGuideIcon}
                          style={{ width: 14, height: 14, marginTop: 1 }}
                        />
                        <Text
                          style={{
                            fontFamily: "DMSansSemiBold",
                            fontSize: SIZES.fontXs,
                            textDecorationLine: "underline",
                          }}
                        >
                          {staticLabels?.size_guide}
                        </Text>
                      </View> */}
                    </View>
                    <View
                      style={{
                        flexDirection: "row",
                        gap: 10,
                        flexWrap: "wrap",
                      }}
                    >
                      {productData.sizes.map((size, idx) => {
                        // const availableSizes = selectedColor ? (colorSizeMap[selectedColor] || []) : [];
                        // const sizeEntry = availableSizes.find(s => s.size === size);
                        // const isDisabled = !sizeEntry || sizeEntry.quantity === 0;
                        return (
                          <TouchableOpacity
                            key={size}
                            onPress={() => handleSizeChange(size, idx)}
                            style={{
                              marginTop: 10,
                              borderWidth: 1,
                              borderRadius: 10,
                              paddingVertical: 10,
                              width: 80,
                              // opacity: isDisabled ? 0.2 : 1,
                              backgroundColor: activeSizeIndex === idx ? COLORS.black : COLORS.white,
                            }}
                          >
                            <Text
                              style={{
                                textAlign: "center",
                                ...FONTS.font,
                                color: activeSizeIndex === idx ? COLORS.white : COLORS.black,
                                // textDecorationLine: isDisabled ? "line-through" : "none",
                              }}
                            >
                              {size}
                            </Text>
                            {/* {isDisabled && (
                              <View
                                style={{
                                  position: "absolute",
                                  width: "105%",
                                  height: 2,
                                  backgroundColor: "rgba(0,0,0,0.5)",
                                  top: "95%",
                                  left: "0%",
                                  transform: [{ rotate: "-25deg" }],
                                }}
                              />
                            )} */}
                          </TouchableOpacity>
                        );
                      })}
                    </View>
                  </View>
                )}
                <View style={{ marginTop: 10, marginHorizontal: 10 }}>
                  {accodianData.map((accordion) => {
                    console.log("accordion", accordion);
                    return (
                      accordion?.description !== "" &&
                      accordion?.title !== "" && (
                        <Accordion
                          menuItems={accordion}
                          accordianWithoutImg={true}
                          key={accordion.title}
                        />
                      )
                    );
                  })}
                </View>
              </View>
            </View>
            <View style={{ justifyContent: "center", flexDirection: "row" }}>
              <View
                style={{
                  marginVertical: 40,
                  backgroundColor: "#00838B",
                  width: "90%",
                  borderRadius: 15,
                }}
              >
                <View
                  style={{ flexDirection: "row", justifyContent: "center" }}
                >
                  <Image
                    source={{
                      uri: "https://m.media-amazon.com/images/I/514Gy2Ggs9L._AC_UF1000,1000_QL80_.jpg",
                    }}
                    style={{
                      width: "100%",
                      height: 200,
                      objectFit: "cover",
                      borderTopLeftRadius: 15,
                      borderTopRightRadius: 15,
                    }}
                  />
                </View>
                <View
                  style={{
                    padding: 5,
                    gap: 10,
                    marginHorizontal: 10,
                    marginVertical: 10,
                  }}
                >
                  <View>
                    <Text
                      style={{
                        color: COLORS.white,
                        ...FONTWEIGHT.SemiBold,
                        ...FONTS.fontMedium,
                        fontSize: SIZES.fontLg,
                      }}
                    >
                      Yonex
                    </Text>
                  </View>
                  <View>
                    <Text
                      style={{
                        fontSize: 25,
                        fontFamily: "DMSansRegular",
                        color: COLORS.white,
                        ...FONTWEIGHT.Normal,
                      }}
                    >
                      Lorem Ipsum
                    </Text>
                  </View>
                  <View>
                    <Text
                      style={{
                        ...FONTS.font,
                        color: COLORS.white,
                        textAlign: "justify",
                        lineHeight: 20,
                      }}
                    >
                      Lorem ipsum dolor sit amet consectetur adipisicing elit.
                      Accusantium explicabo corrupti ipsa magnam, officiis
                      ratione tempore quasi inventore suscipit laborum enim amet
                      itaque vitae voluptatibus exercitationem .
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </ScrollView>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: Platform.OS === "ios" && keyboardOpen ? "flex-start" : "center",
              backgroundColor: COLORS.white,
              borderTopColor: COLORS.gray,
              height: Platform.OS === "ios" && keyboardOpen ? 400 : 80,
              paddingHorizontal: 16,
              paddingVertical: 10,
              width: "100%",
            }}
          >
            <View style={{ width: "35.5%" }}>
              <View>
                <View
                  style={[
                    {
                      height: 60,
                      paddingHorizontal: 25,
                      paddingVertical: 13,
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                      borderRadius: 28,
                      backgroundColor: COLORS.white,
                      borderWidth: 1,
                      borderColor: COLORS.darkgray,
                    },
                  ]}
                >
                  <View
                    style={{
                      justifyContent: "center",
                      alignItems: "center",
                      flexDirection: "row",
                      width: "100%",
                      gap: 10,
                    }}
                  >
                    <TouchableOpacity
                      style={{
                        justifyContent: "center",
                        alignItems: "center",
                        minWidth: 30,
                        minHeight: 30,
                      }}
                      onPress={handleDecrease}
                      activeOpacity={0.7}
                      hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
                    >
                      <Text
                        style={[{ fontWeight: "500", fontSize: 25, width: 30, textAlign: "center", color: COLORS.title } as StyleProp<TextStyle>]}
                      >
                        -
                      </Text>
                    </TouchableOpacity>
                    <TextInput
                      style={[
                        { fontSize: 16, width: 40, textAlign: "center", paddingVertical: 2, paddingHorizontal: 4, fontWeight: "500", color: COLORS.black, fontFamily: FONTS.font.fontFamily, backgroundColor: "transparent" }
                      ]}
                      keyboardType="numeric"
                      value={String(quantity)}
                      onChangeText={(text) => {
                        const numeric = text.replace(/[^0-9]/g, "");
                        let parsedValue = parseInt(numeric, 10);
                        if (isNaN(parsedValue) || parsedValue < 1) parsedValue = 0;
                        setQuantity(parsedValue);
                      }}
                      onBlur={() => {
                        if (!quantity || quantity < 1) setQuantity(0);
                      }}
                      maxLength={6}
                      returnKeyType="done"
                    />
                    <TouchableOpacity
                      style={{
                        justifyContent: "center",
                        alignItems: "center",
                        minWidth: 30,
                        minHeight: 30,
                      }}
                      onPress={handleIncrease}
                      activeOpacity={0.7}
                      hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
                    >
                      <Text
                        style={[{ fontWeight: "500", fontSize: 20, width: 30, textAlign: "center", color: COLORS.title } as StyleProp<TextStyle>]}
                      >
                        +
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>

            <Button
              title={
                isPreorder ? "Pre Order" : staticLabels?.add_to_bag
              }
              onPress={handleAddToBag}
              btnRounded={true}
              style={{
                width: "57%",
                height: 60,
                borderRadius: 28,
              }}
              loading={isLoading}
              disabled={quantity === 0}
            />
          </View>
          <BottomSheetNew
            modalVisible={modalVisible}
            currentProductDetails={[]}
            setModalVisible={setModalVisible}
            buttonWidth={200}
            // headerEnabled={false}
            clearAllBtn={true}
            planBottomSheet={true}
            height={screenHeight}
            navbarTitle=""
            isCloseButtonRequired={false}
            isBackBtnRequired={true}
          >
            {/* <ScrollView > */}
            <View
              style={{
                position: "relative",
                backgroundColor: COLORS.background,
                height: screenHeight * 0.65, // ~65% of screen height
              }}
            >
              <ScrollView
                horizontal
                pagingEnabled
                ref={scrollRef}
                showsHorizontalScrollIndicator={false}
                scrollEventThrottle={16}
                contentContainerStyle={{
                  width: screenWidth * (productData?.images?.length || 1),
                }}
                onScroll={Animated.event(
                  [{ nativeEvent: { contentOffset: { x: scrollX } } }],
                  {
                    useNativeDriver: false,
                    listener: (event: any) => {
                      const xOffset = event.nativeEvent.contentOffset.x;
                      const newIndex = Math.round(xOffset / screenWidth);
                      setActiveIndex(newIndex);
                    },
                  }
                )}
              >
                {(productData?.images?.length > 0 ? productData?.images : [defaultImage]).map((img: any, index: number) => (
                  <Image
                    key={index}
                    source={{ uri: img?.src ?? "https://t3.ftcdn.net/jpg/02/01/90/26/360_F_201902639_gZhcd695qjLUnVtJPXqlWpxNj5TvVHw2.jpg" }}
                    style={{
                      width: screenWidth,
                      height: screenHeight * 0.75,
                      resizeMode: "contain",
                      borderRadius: 12,
                    }}
                  />
                ))}
              </ScrollView>


            </View>
            <View style={{ position: "relative", width: screenWidth }}>
              {activeIndex > 0 && (
                <TouchableOpacity
                  onPress={() => handleScrollTo("left")}
                  style={{
                    position: "absolute",
                    left: screenWidth * 0.04, // 4% from left
                    bottom: screenHeight * 0.28,
                    width: 40,
                    height: 40,
                    justifyContent: "center",
                    alignItems: "center",
                    zIndex: 100,
                  }}
                >
                  <Image source={BackArrow} style={{ width: 13, height: 23 }} />
                </TouchableOpacity>
              )}

              {activeIndex < productData?.images.length - 1 && (
                <TouchableOpacity
                  onPress={() => handleScrollTo("right")}
                  style={{
                    position: "absolute",
                    right: screenWidth * 0.04,
                    bottom: screenHeight * 0.28,
                    width: 40,
                    height: 40,
                    justifyContent: "center",
                    alignItems: "center",
                    zIndex: 100,
                  }}
                >
                  <Image source={ForwardArrow} style={{ width: 13, height: 23 }} />
                </TouchableOpacity>
              )}
            </View>

            {/* <View
                style={{
                  flexDirection: "row",
                  // justifyContent: "space-around",
                  alignItems: "center",
                  backgroundColor: COLORS.card,
                  // borderTopWidth: 1,
                  borderTopColor: COLORS.gray,
                  height: 120,
                  marginTop: 110,
                }}
              >
                <View
                  style={{
                    marginHorizontal: 10,
                  }}
                >
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    scrollEventThrottle={16}
                  >
                    <View
                      style={{
                        gap: 11,
                        flexDirection: "row",
                        // alignItems: "center",
                      }}
                    >
                      {productData?.images.map((img: any, index: any) => (
                        <>
                          <TouchableOpacity
                            key={index}
                            onPress={() => {
                              scrollRef.current?.scrollTo({
                                x: index * width,
                                animated: true,
                              });
                              setActiveIndex(index);
                            }}
                            style={{
                              alignItems: "center",
                              // marginHorizontal: 5,
                              borderWidth: 0.1,
                              borderColor: COLORS.gray,
                              // padding: 10,
                              // borderRadius: 4,
                              height: 100,
                            }}
                          >
                            <Image
                              source={{ uri: img?.src }}
                              style={{
                                width: 90,
                                height: 90,
                                borderWidth: 1,
                                aspectRatio: 1 / 1,
                                borderColor: COLORS.lightgray,
                                objectFit: "cover",
                                borderRadius: 4,
                              }}
                            />
                          </TouchableOpacity>
                        </>
                      ))}
                    </View>
                  </ScrollView>
                </View>
              </View> */}
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-around",
                alignItems: "center",
                backgroundColor: COLORS.card,
                borderTopColor: COLORS.gray,
                height: screenHeight * 0.15,
                marginTop: screenHeight * 0.09,
              }}
            >
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                scrollEventThrottle={16}
                contentContainerStyle={{
                  paddingHorizontal: screenWidth * 0.03,
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    gap: screenWidth * 0.03,
                    alignItems: "center",
                  }}
                >
                  {productData?.images.length > 0 ? productData?.images.map((img: any, index: number) => (
                    <TouchableOpacity
                      key={index}
                      onPress={() => {
                        scrollRef.current?.scrollTo({
                          x: index * screenWidth,
                          animated: true,
                        });
                        setActiveIndex(index);
                      }}
                      style={{
                        alignItems: "center",
                        borderColor: COLORS.gray,
                        borderRadius: 4,
                        height: screenHeight * 0.12,
                      }}
                    >
                      <Image
                        source={{ uri: img?.src }}
                        style={{
                          width: screenWidth * 0.2, // ~20% of screen width
                          height: screenWidth * 0.2, // square thumbnail
                          borderRadius: 4,
                          resizeMode: "cover",
                          borderColor: COLORS.lightgray,
                          borderWidth: 1,
                        }}
                      />
                    </TouchableOpacity>
                  )) : <TouchableOpacity
                    onPress={() => {
                      scrollRef.current?.scrollTo({
                        animated: true,
                      });
                    }}
                    style={{
                      alignItems: "center",
                      borderColor: COLORS.gray,
                      borderRadius: 4,
                      height: screenHeight * 0.12,
                    }}
                  >
                    <Image
                      source={{ uri: "https://t3.ftcdn.net/jpg/02/01/90/26/360_F_201902639_gZhcd695qjLUnVtJPXqlWpxNj5TvVHw2.jpg" }}
                      style={{
                        width: screenWidth * 0.2, // ~20% of screen width
                        height: screenWidth * 0.2, // square thumbnail
                        borderRadius: 4,
                        resizeMode: "cover",
                        borderColor: COLORS.lightgray,
                        borderWidth: 1,
                      }}
                    />
                  </TouchableOpacity>}
                </View>
              </ScrollView>
            </View>

            {/* </ScrollView> */}
          </BottomSheetNew>
        </>
      )}

      {/* Custom Alert */}
      {alertConfig && (
        <CustomAlert
          visible={alertVisible}
          title={alertConfig.title}
          message={alertConfig.message}
          type={alertConfig.type}
          buttons={alertConfig.buttons}
          onClose={hideAlert}
        />
      )}
      {/* Transparent loader overlay for variant change */}
      {variantLoading && (
        <View style={{
          position: 'absolute',
          top: 0, left: 0, right: 0, bottom: 0,
          backgroundColor: 'rgba(255,255,255,0.4)',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 9999,
        }}>
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      )}
    </SafeAreaView>
  );
}

export const Accordion = ({
  menuItems,
  navigation,
  styles,
}: AccordianProps) => {
  const [isActive, setIsActive] = useState(false);
  const animation = useRef(new Animated.Value(0)).current;
  const [textHeight, setTextHeight] = useState(0);
  const toggleAccordion = () => {
    Animated.timing(animation, {
      toValue: isActive ? 0 : 1,
      duration: 400, // smoother open/close speed
      easing: Easing.inOut(Easing.ease), // smooth easing
      useNativeDriver: false,
    }).start();

    setIsActive(!isActive);
  };
  const animatedHeight = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, textHeight > 50 ? textHeight : 50]
  });

  return (
    <View>
      <TouchableOpacity
        onPress={() => {
          toggleAccordion();
        }}
        style={
          styles
            ? { ...styles }
            : {
              flexDirection: "row",
              backgroundColor: COLORS.backgroundColor,
              // borderRadius: 10,
              width: "100%",
              borderWidth: Platform.OS === "android" ? 1 : 0,
              borderColor: "white",
              borderBottomColor: COLORS.gray,
            }
        }
      >
        <View style={{ flexDirection: "row", width: "100%" }}>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              width: "100%",
              marginVertical: 16,
              borderWidth: 1,
              paddingTop: 5,
              paddingBottom: 16,
              borderBottomColor: "#EEEEEE",
              borderTopColor: COLORS.white,
              borderLeftColor: COLORS.white,
              borderRightColor: COLORS.white,
            }}
          >
            <Text
              style={{
                ...FONTS.fontLg,
                ...FONTS.fontBold,
                marginHorizontal: 4,
              }}
            >
              {menuItems.title}
            </Text>
            <Image
              source={isActive ? IMAGES.minus : IMAGES.plus}
              style={{
                width: 15,
                height: 15,
                objectFit: "contain",
              }}
            />
          </View>
        </View>
      </TouchableOpacity>

      <Animated.View
        style={{
          height: animatedHeight,
          overflow: "hidden",
          backgroundColor: COLORS.backgroundColor,
          marginHorizontal: 5,
          // borderRadius: 10,
        }}
      >
        <ScrollView contentContainerStyle={{ width: "100%" }}>
          <View>
            <Text
              style={{
                ...FONTS.font,
                ...FONTWEIGHT.SemiBold,
                color: COLORS.gray,
                textAlign: "justify",
              }}
              onLayout={(e) => {
                const height = e.nativeEvent.layout.height;
                setTextHeight(height);
              }}
            >
              {menuItems.description}
            </Text>
          </View>
        </ScrollView>
      </Animated.View>
    </View>
  );
};
