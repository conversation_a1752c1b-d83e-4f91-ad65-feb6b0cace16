import { useFocusEffect, useIsFocused, useTheme } from "@react-navigation/native";
import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Animated,
  Dimensions,
} from "react-native";
import Header from "../../layout/Header";
import { GlobalStyleSheet } from "../../constants/StyleSheet";
import {
  ButtonLabel,
  COLORS,
  FONTS,
  FONTWEIGHT,
  SIZES,
} from "../../constants/theme";
import { ScrollView } from "react-native-gesture-handler";
import { IMAGES } from "../../constants/Images";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList } from "../../Navigations/RootStackParamList";
import { transform } from "@babel/core";
import CardSlider, {
  CardSliderCatetgory,
} from "../../components/CardSlider/CardSlider";
import { useQuery } from "@apollo/client";
import { GET_MENU_ITEMS } from "../../api/categorypageQuery";
import Button from "../../components/Button/Button";
import { ActivityIndicator } from "react-native-paper";
import "react-native-gesture-handler";
import { staticTranslations } from "@/app/constants/staticTranslations";
import { LanguageState } from "@/app/redux/reducer/languageReducer";
import { useSelector } from "react-redux";

type CategoryScreenProps = StackScreenProps<RootStackParamList, "Category">;
type AccordianProps = {
  navigation?: any;
  menuItems: any;
  styles?: any;
  accordianWithoutImg?: any;
  handle: any;
};
const Category = ({ navigation }: CategoryScreenProps) => {
  const { loading, error, data } = useQuery(GET_MENU_ITEMS);
  const { locale }: LanguageState = useSelector((state: any) => state.language);
  const staticLabels = locale === "en" ? staticTranslations.english : staticTranslations.malay;
  // if (loading) {
  //   return (
  //     <View
  //       style={{
  //         justifyContent: "center",
  //         alignItems: "center",
  //         height: 600,
  //       }}
  //     >
  //       <ActivityIndicator size="large" color={COLORS.primary} />
  //     </View>
  //   );
  // }
  if (error) {
    return (
      <View>
        <Text>{error.message}</Text>
      </View>
    );
  }
  const menus = data?.menu.items.map((menuItems: any) => {
    const title = menuItems.title;
    const redirectionUrl = menuItems.url;
    const allMenuItems = menuItems?.items.map((item: any) => {
      return item.items;
    });

    return [
      {
        title: title,
        redirectionUrl: redirectionUrl ? redirectionUrl : "",
        allMenuItems: allMenuItems,
      },
    ];
  });
  return (
    <SafeAreaView style={{ backgroundColor: COLORS.background, flex: 1 }}>
      <Header
        title={staticLabels.categories}
        // rightIcon={"cart"}
        leftIcon={"back"}
        titleLeft
        paddingLeft
      />
      {loading ? (
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            height: 600,
          }}
        >
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      ) : (
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={{ gap: 10, marginTop: 16 }}>
            {menus?.map((menuItems: any, index: Number): any => {
              // console.log("menuItmes", menuItems);

              const itemHandle = menuItems[0]?.redirectionUrl
                ?.split("/")
                ?.pop();
              return (
                <>
                  <Accordian
                    key={index}
                    menuItems={menuItems}
                    navigation={navigation}
                    handle={itemHandle}
                  />
                </>
              );
            })}
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
};

export const Accordian = ({
  menuItems,
  navigation,
  styles,
  accordianWithoutImg = false,
  handle,
}: any) => {
  const [isActive, setIsActive] = useState(false);
  const [activeTitle, setActiveTitle] = useState({});
  const animation = useRef(new Animated.Value(0)).current;

  useFocusEffect(
    useCallback(() => {
      // When screen is focused
      return () => {
        // When screen is unfocused (navigated away)
        setIsActive(false);
        Animated.timing(animation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: false,
        }).start();
      };
    }, [])
  );

  const toggleAccordion = (title: any, redirectionUrl: any) => {
    if (isActive) {
      Animated.timing(animation, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else {
      const redirectionUrlHandle = redirectionUrl?.split("/")?.pop();
      setActiveTitle({ title, redirectionUrl: redirectionUrlHandle });
      Animated.timing(animation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
    setIsActive(!isActive);
  };

  const animatedHeight = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [
      0,
      menuItems[0]?.allMenuItems?.flat()?.length > 4 ? 300 : 180,
    ],
  });

  const SCREEN_WIDTH = Dimensions.get("screen").width;
  const flattenedMenuItems = menuItems[0]?.allMenuItems?.flat() || [];

  return (


    <View style={{ gap: 10, width: "100%", paddingHorizontal: SCREEN_WIDTH * 0.04 }}>
      <View>
        {menuItems?.map((menuItem: any, index: any) => {
          const imageWidth = SCREEN_WIDTH * 0.38;
          const imageHeight = imageWidth * 0.65;

          return (
            menuItem?.allMenuItems?.length > 0 && (
              <TouchableOpacity
                key={menuItem.title + index}
                onPress={() =>
                  toggleAccordion(menuItem?.title, menuItem?.redirectionUrl)
                }
                style={{
                  flexDirection: "row",
                  backgroundColor: COLORS.backgroundColor,
                  borderRadius: 8,
                  width: "100%",
                  paddingHorizontal: SCREEN_WIDTH * 0.02,
                  // paddingVertical: 10,
                }}
              >
                <View style={{ flexDirection: "row", width: "100%", justifyContent: "space-between" }}>
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      width: "60%",
                    }}
                  >
                    <Text
                      style={{
                        fontSize: SIZES.h4,
                        fontWeight: "500",
                        marginHorizontal: 15,
                        flexShrink: 1,
                        ...FONTS.fontTitle,
                      }}
                      numberOfLines={2}
                    >
                      {menuItem.title}
                    </Text>
                    <Image
                      source={{
                        uri: "https://cdn-icons-png.flaticon.com/512/2985/2985150.png",
                      }}
                      style={{
                        width: 15,
                        height: 15,
                        resizeMode: "contain",
                        transform: [
                          {
                            rotateZ:
                              isActive && menuItems[0].allMenuItems?.length > 0
                                ? "180deg"
                                : "0deg",
                          },
                        ],
                      }}
                    />
                  </View>

                  {!accordianWithoutImg && (
                    <TouchableOpacity
                      onPress={() =>
                        navigation.navigate("ProductListingPage", { handle })
                      }
                      style={{
                        width: imageWidth,
                      }}
                    >
                      <Image
                        source={{
                          uri: "https://m.media-amazon.com/images/S/aplus-media-library-service-media/0d1ccb79-bb89-477c-b6b8-29da21a486f8.__CR0,0,970,300_PT0_SX970_V1___.jpg",
                        }}
                        style={{
                          width: imageWidth,
                          height: imageHeight,
                          borderTopRightRadius: 5,
                          borderBottomRightRadius: 5,
                          resizeMode: "fill",
                        }}
                      />
                    </TouchableOpacity>
                  )}
                </View>
              </TouchableOpacity>
            )
          );
        })}
      </View>

      {flattenedMenuItems.length > 0 && (
        <Animated.View
          style={{
            height: animatedHeight,
            overflow: "hidden",
            backgroundColor: COLORS.backgroundColor,
            // paddingHorizontal: SCREEN_WIDTH * 0.04,
            borderRadius: 8,
          }}
        >
          <ScrollView contentContainerStyle={{ width: "100%" }}>
            <View
              style={{
                flexDirection: "row",
                flexWrap: "wrap",
                // gap: 10,
                width: "100%",

                marginVertical: 10,
              }}
            >
              <CardSliderCatetgory
                products={flattenedMenuItems}
                smallCard={true}
                navigation={navigation}
                categoryCards={true}
              />
            </View>
          </ScrollView>
          <Button
            title={`EXPLORE ALL ${activeTitle?.title?.toUpperCase()} ITEMS`}
            style={{ borderRadius: 0 }}
            onPress={() => {
              navigation.navigate("ProductListingPage", {
                handle: activeTitle?.redirectionUrl,
              });
            }}
          />
        </Animated.View>
      )}
    </View>
  );
};
export default Category;
