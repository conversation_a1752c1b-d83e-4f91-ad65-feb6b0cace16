import {
    CommonActions,
    useNavigation,
    useTheme,
  } from "@react-navigation/native";
  import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
  import {
    View,
    Text,
    SafeAreaView,
    Image,
    TouchableOpacity,
    SectionList,
    ScrollView,
    Platform,
    Button,
    StyleSheet,
    NativeModules,
  } from "react-native";
  import { LinearGradient } from "expo-linear-gradient";
  import { GlobalStyleSheet } from "../../constants/StyleSheet";
  import { FONTS, COLORS } from "../../constants/theme";
  
  import ListItem from "../../components/list/ListItem";
  import { IMAGES } from "../../constants/Images";
  import { StackScreenProps } from "@react-navigation/stack";
  import { RootStackParamList } from "../../Navigations/RootStackParamList";
  import "react-native-gesture-handler";
  import AsyncStorage from "@react-native-async-storage/async-storage";
  import WebView from "react-native-webview";
  import { ActivityIndicator } from "react-native-paper";
  import TaskModal from "@/app/components/Modal/TaskModal";
  import CookieManager from "@react-native-cookies/cookies";
  import NotificationService from "../../services/NotificationService";
  import { clearCart } from '../../redux/reducer/cartReducer';
import { useDispatch } from "react-redux";
  
  const { WebViewStorageManager } = NativeModules;
  const B2B_PERMISSION_ERROR_URLS: string[] = [
    "https://shopify.com/***********/account/locations",
  ];
  const clearWebViewDataScript = `
  (function() {
    console.log('🧹 AGGRESSIVE WebView data clearing started...');
  
    try {
      // Clear all localStorage
      if (typeof localStorage !== 'undefined') {
        const localStorageKeys = [];
        for (let i = 0; i < localStorage.length; i++) {
          localStorageKeys.push(localStorage.key(i));
        }
        localStorageKeys.forEach(key => {
          if (key) {
            localStorage.removeItem(key);
            console.log('🗑️ Cleared localStorage key:', key);
          }
        });
        localStorage.clear();
        console.log('✅ localStorage completely cleared');
      }
  
      // Clear all sessionStorage
      if (typeof sessionStorage !== 'undefined') {
        const sessionStorageKeys = [];
        for (let i = 0; i < sessionStorage.length; i++) {
          sessionStorageKeys.push(sessionStorage.key(i));
        }
        sessionStorageKeys.forEach(key => {
          if (key) {
            sessionStorage.removeItem(key);
            console.log('🗑️ Cleared sessionStorage key:', key);
          }
        });
        sessionStorage.clear();
        console.log('✅ sessionStorage completely cleared');
      }
  
      // Clear all cookies by setting them to expire
      if (typeof document !== 'undefined' && document.cookie) {
        const cookies = document.cookie.split(';');
        cookies.forEach(cookie => {
          const eqPos = cookie.indexOf('=');
          const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
          if (name) {
            // Clear for current domain
            document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
            document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=' + window.location.hostname;
            document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.' + window.location.hostname;
            // Clear for Shopify domain
            document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.myshopify.com';
            document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.shopify.com';
            console.log('🍪 Cleared cookie:', name);
          }
        });
        console.log('✅ All cookies cleared');
      }
  
      console.log('🎉 AGGRESSIVE WebView data clearing completed!');
  
      // Notify React Native that clearing is complete
      if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'WEBVIEW_DATA_CLEARED',
          success: true,
          timestamp: new Date().toISOString()
        }));
      }
  
    } catch (error) {
      console.error('❌ WebView data clearing failed:', error);
      if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'WEBVIEW_DATA_CLEARED',
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        }));
      }
    }
  })();
  true;
  `;
  type ProfileScreenProps = StackScreenProps<RootStackParamList, "Profile">;
  
  const ProfileIOS = ({ navigation }: ProfileScreenProps) => {
    const theme = useTheme();
    const { colors }: { colors: any } = theme;
    const webViewRef = useRef<WebView>(null);
    const [loading, setLoading] = useState(true); // Start with loading true
    const [initialLoadComplete, setInitialLoadComplete] = useState(false);
    const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const [showWebView, setShowWebView] = useState(true);
    const [showB2BPermissionError, setShowB2BPermissionError] = useState(false);
    const dispatch = useDispatch();
    const uri = "https://sunrise-trade.myshopify.com/account/orders"; // Start with orders page
    const [modalVisible, setModalVisible] = useState(false);
    // Handle initial load and subsequent navigation
    const handleLoadStart = () => {
      // Only show loading indicator for initial load or if explicitly needed
      if (!initialLoadComplete) {
        setLoading(true);
      }
    };
  
    const handleLoadEnd = () => {
      // Clear any existing timeout
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
  
      // Directly set loading to false to avoid getting stuck in redirect loops.
      // A slight flicker is better than an infinite loader.
      setLoading(false);
      if (!initialLoadComplete) {
        setInitialLoadComplete(true);
      }
    };
  
    // Clean up timeout on unmount
    useEffect(() => {
      return () => {
        if (loadingTimeoutRef.current) {
          clearTimeout(loadingTimeoutRef.current);
        }
      };
    }, []);
  
    // Refresh WebView and redirect to orders page when profile page is focused
    useEffect(() => {
      const unsubscribe = navigation.addListener("focus", () => {
        console.log(
          "🔄 Profile page focused - refreshing WebView and redirecting to orders"
        );
        if (webViewRef.current) {
          // Navigate directly to orders page
          webViewRef.current.injectJavaScript(`
            console.log('🛒 Redirecting to orders page...');
            window.location.href = 'https://sunrise-trade.myshopify.com/account/orders';
            true;
          `);
          setLoading(true);
          setInitialLoadComplete(false);
        }
      });
  
      return unsubscribe;
    }, [navigation]);
  
    const handleLogOut = async () => {
      try {
        // Clear notification state first
        await NotificationService.onLogout();
  
        // CRITICAL: Set logout flag FIRST to prevent auto-login
        console.log("🚪 Setting JUST_LOGGED_OUT flag to prevent auto-navigation");
        await AsyncStorage.setItem("JUST_LOGGED_OUT", "true");
        console.log("✅ JUST_LOGGED_OUT flag set successfully");
        const clearAllData = async () => {
            dispatch(clearCart());
          await CookieManager.clearAll();
          await AsyncStorage.clear();
          if (Platform.OS === "ios") {
            dispatch(clearCart());
            await CookieManager.clearAll(true);
          }
          // 2. Inject JS to clear WebView storage
          webViewRef.current?.injectJavaScript(`
                  localStorage.clear();
                  sessionStorage.clear();
                  document.cookie.split(';').forEach(c => {
                    document.cookie = c.trim().split('=')[0] + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
                  });
                  true;
                `);
          // 3. Reload WebView to reset session
          webViewRef.current?.reload();
  
        }
        clearAllData();
  
        // await CookieManager.clearAll(); // ✅ Clears all WebView cookies
  
        navigation.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: Platform.OS === "ios" ? "ShopifyLoginIOS" : "ShopifyLogin" }],
          })
        );
      } catch (error) {
        console.error("Logout error:", error);
      }
      // const data = await AsyncStorage.getItem("customerToken");
      // const customerdata = await AsyncStorage.getItem("customerdata");
    };
  
    const injectedDrawerClick = `
    (function openDrawerOnce() {
      if (window.__drawerOpened) return true; // Already opened once, skip
  
      const btn = document.querySelector('button[aria-label="Open main navigation"]');
      if (btn) {
        btn.click();
        window.__drawerOpened = true; // Mark that drawer is opened
  
        // Hide unwanted navigation items after drawer opens
        setTimeout(() => {
          hideUnwantedNavItems();
        }, 100);
      } else {
        setTimeout(openDrawerOnce, 500);
      }
    })();
  
    function hideUnwantedNavItems() {
      try {
        // Hide Shop link - try multiple selectors
        const shopSelectors = [
          'a[href*="sunrise-trade.myshopify.com/?country=MY"]',
          'a[href*="sunrise-trade.myshopify.com"]',
          'a[href="https://sunrise-trade.myshopify.com/?country=MY"]',
          'a:contains("Shop")',
          'a[href*="/?country=MY"]'
        ];
  
        shopSelectors.forEach(selector => {
          try {
            const shopLinks = document.querySelectorAll(selector);
            shopLinks.forEach(link => {
              // Check if link text contains "Shop" or href contains shop domain
              if (link.textContent?.trim() === 'Shop' ||
                  link.href?.includes('sunrise-trade.myshopify.com/?country=MY') ||
                  link.href?.includes('sunrise-trade.myshopify.com') && !link.href?.includes('/account') && !link.href?.includes('/pages')) {
                const listItem = link.closest('li');
                if (listItem) {
                  listItem.style.display = 'none';
                  console.log('✅ Hidden shop link:', link.href);
                }
              }
            });
          } catch (e) {
            // Continue with next selector
          }
        });
  
        // Also try to find by text content
        const allLinks = document.querySelectorAll('a');
        allLinks.forEach(link => {
          if (link.textContent?.trim() === 'Shop') {
            const listItem = link.closest('li');
            if (listItem) {
              listItem.style.display = 'none';
              console.log('✅ Hidden shop link by text:', link.textContent);
            }
          }
        });
  
        // Hide Policies link
        const policyLinks = document.querySelectorAll('a[href*="/policies/terms-of-service"], a[href*="/policies"]');
        policyLinks.forEach(link => {
          const listItem = link.closest('li');
          if (listItem) {
            listItem.style.display = 'none';
            console.log('✅ Hidden policy link:', link.href);
          }
        });
  
        // Hide Settings link
        const settingsLinks = document.querySelectorAll('a[href*="/account/settings"]');
        settingsLinks.forEach(link => {
          const listItem = link.closest('li');
          if (listItem) {
            listItem.style.display = 'none';
            console.log('✅ Hidden settings link:', link.href);
          }
        });
  
        console.log('✅ Unwanted navigation items hidden');
      } catch (error) {
        console.error('❌ Error hiding navigation items:', error);
      }
    }
  
    true;
  `;
  
    const handleOkay = () => {
      setModalVisible(false);
    };
    return (
      <>
        {showB2BPermissionError ? (
          <View style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "#fff"
          }}>
            <View style={{
              borderWidth: 1,
              borderColor: "#ddd",
              borderRadius: 6,
              padding: 32,
              maxWidth: 600,
              width: "90%",
              backgroundColor: "#fff"
            }}>
              <Text style={{
                textAlign: "center",
                fontSize: 18,
                marginBottom: 24,
                color: "#222"
              }}>
                You no longer have permission to place B2B orders for Sunrise trade. Log out to place a personal order.
              </Text>
              <TouchableOpacity
                style={{
                  backgroundColor: "#1879B9",
                  borderRadius: 6,
                  paddingVertical: 21,
                  paddingHorizontal: 26,
                  alignSelf: "center",
                }}
                onPress={handleLogOut}
              >
                <Text style={{ color: "#fff", fontSize: 14, fontWeight: "bold" }}>Log out</Text>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <>
            {showWebView && (
              <WebView
                ref={webViewRef}
                source={{ uri: uri }}
                onNavigationStateChange={(navState) => {
                  console.log("navState.url", navState.url);
  
                  // Show custom B2B permission error UI for specific URLs
                  if (B2B_PERMISSION_ERROR_URLS.some((url: string) => navState.url.startsWith(url))) {
                    setShowB2BPermissionError(true);
                    return;
                  }
                  setShowB2BPermissionError(false);
  
                  if (navState.url.includes("/pages/create-company")) {
                    console.log("✅ Detected Registration Form URL");
                    setShowWebView(false);
                    // setLoading(true);
                    // Hide the WebView
                    navigation.navigate("ApplicationStatus");
                    return;
                  }
  
                  if (navState.url.includes("/account/logout")) {
                    console.log("🔓 Shopify logout initiated");
  
                    // Hide WebView & show custom loader
                    setLoading(true);
                    webViewRef.current?.injectJavaScript(`
       const logoutModal = document.getElementById("Modal0");
        if (logoutModal) {
         logoutModal.remove();
      }
      document.body.style.overflow = 'hidden';
      true;
    `);
                  }
  
                  // Apply navigation filtering to all account pages (but be gentle with orders page)
                  if (
                    navState.url.includes("/account/") &&
                    !navState.url.includes("/account/login") &&
                    !navState.url.includes("/authentication")
                  ) {
                    // Special handling for orders page - minimal interference
                    if (navState.url.includes("/account/orders")) {
                      console.log(
                        "🛒 Orders page detected - using minimal JS injection"
                      );
                      webViewRef.current?.injectJavaScript(`
    (function() {
      console.log('🛒 Orders page JS injection started');
  
      function hideUnwantedNavItemsMinimal() {
        try {
          // Only hide navigation items, don't touch page content
          const navContainer = document.querySelector('#left-nav') || document.querySelector('nav') || document.querySelector('.drawer');
  
          console.log('🔍 Nav container found:', !!navContainer);
  
          if (navContainer) {
            // Hide Shop link only in navigation
            const shopLinks = navContainer.querySelectorAll('a');
            console.log('🔍 Found nav links:', shopLinks.length);
  
            shopLinks.forEach(link => {
              if (link.textContent?.trim() === 'Shop' &&
                  (link.href?.includes('sunrise-trade.myshopify.com/?country=MY') ||
                   link.href?.includes('sunrise-trade.myshopify.com') && !link.href?.includes('/account'))) {
                const listItem = link.closest('li');
                if (listItem) {
                  listItem.style.display = 'none';
                  console.log('✅ Hidden shop link in nav:', link.href);
                }
              }
            });
  
            // Hide Policies and Settings links only in navigation
            const unwantedLinks = navContainer.querySelectorAll('a[href*="/policies"], a[href*="/account/settings"]');
            unwantedLinks.forEach(link => {
              const listItem = link.closest('li');
              if (listItem) {
                listItem.style.display = 'none';
                console.log('✅ Hidden unwanted nav link:', link.href);
              }
            });
          }
  
          // Debug: Check if orders content is present
          const ordersContent = document.querySelector('.orders') || document.querySelector('[data-orders]') || document.querySelector('.order-history');
          console.log('🔍 Orders content found:', !!ordersContent);
  
          // Debug: Check for any elements being hidden unintentionally
          const hiddenElements = document.querySelectorAll('[style*="display: none"]');
          console.log('🔍 Total hidden elements:', hiddenElements.length);
  
          console.log('✅ Minimal navigation cleanup for orders page completed');
        } catch (error) {
          console.error('❌ Error in minimal nav cleanup:', error);
        }
      }
  
      // Run minimal cleanup only once, then after a short delay
      hideUnwantedNavItemsMinimal();
      setTimeout(hideUnwantedNavItemsMinimal, 1000);
  
      // Debug: Monitor for any content changes
      setTimeout(() => {
        const pageContent = document.body.innerHTML.length;
        console.log('🔍 Page content length after 2s:', pageContent);
      }, 2000);
    })();
    true;
  `);
                    } else {
                      // Full navigation hiding for other account pages
                      webViewRef.current?.injectJavaScript(`
    (function() {
      function hideUnwantedNavItems() {
        try {
          // Hide Shop link - try multiple selectors
          const shopSelectors = [
            'a[href*="sunrise-trade.myshopify.com/?country=MY"]',
            'a[href*="sunrise-trade.myshopify.com"]',
            'a[href="https://sunrise-trade.myshopify.com/?country=MY"]',
            'a:contains("Shop")',
            'a[href*="/?country=MY"]'
          ];
  
          shopSelectors.forEach(selector => {
            try {
              const shopLinks = document.querySelectorAll(selector);
              shopLinks.forEach(link => {
                // Check if link text contains "Shop" or href contains shop domain
                if (link.textContent?.trim() === 'Shop' ||
                    link.href?.includes('sunrise-trade.myshopify.com/?country=MY') ||
                    link.href?.includes('sunrise-trade.myshopify.com') && !link.href?.includes('/account') && !link.href?.includes('/pages')) {
                  const listItem = link.closest('li');
                  if (listItem) {
                    listItem.style.display = 'none';
                    console.log('✅ Hidden shop link:', link.href);
                  }
                }
              });
            } catch (e) {
              // Continue with next selector
            }
          });
  
          // Also try to find by text content
          const allLinks = document.querySelectorAll('a');
          allLinks.forEach(link => {
            if (link.textContent?.trim() === 'Shop') {
              const listItem = link.closest('li');
              if (listItem) {
                listItem.style.display = 'none';
                console.log('✅ Hidden shop link by text:', link.textContent);
              }
            }
          });
  
          // Hide Policies link
          const policyLinks = document.querySelectorAll('a[href*="/policies/terms-of-service"], a[href*="/policies"]');
          policyLinks.forEach(link => {
            const listItem = link.closest('li');
            if (listItem) {
              listItem.style.display = 'none';
              console.log('✅ Hidden policy link:', link.href);
            }
          });
  
          // Hide Settings link
          const settingsLinks = document.querySelectorAll('a[href*="/account/settings"]');
          settingsLinks.forEach(link => {
            const listItem = link.closest('li');
            if (listItem) {
              listItem.style.display = 'none';
              console.log('✅ Hidden settings link:', link.href);
            }
          });
  
          console.log('✅ Unwanted navigation items hidden');
        } catch (error) {
          console.error('❌ Error hiding navigation items:', error);
        }
      }
  
      // Hide items immediately
      hideUnwantedNavItems();
  
      // Run again after delays to catch dynamic content
      setTimeout(hideUnwantedNavItems, 500);
      setTimeout(hideUnwantedNavItems, 1000);
  
      // Set up mutation observer for dynamic content (only for navigation)
      const observer = new MutationObserver(() => {
        hideUnwantedNavItems();
      });
  
      const navElement = document.querySelector('#left-nav') || document.querySelector('nav');
      if (navElement) {
        observer.observe(navElement, { childList: true, subtree: true });
      }
    })();
    true;
  `);
                    }
                  }
  
                  if (navState.url.includes("/account/profile")) {
                    // Inject JS to simulate drawer open for other account pages
                    webViewRef.current?.injectJavaScript(injectedDrawerClick);
                    webViewRef.current?.injectJavaScript(`
    (function() {
      // Hide unwanted navigation items immediately when profile page loads
      function hideUnwantedNavItems() {
        try {
          // Only target navigation elements, not page content
          const navContainer = document.querySelector('#left-nav') || document.querySelector('nav') || document.querySelector('.drawer');
  
          if (navContainer) {
            // Hide Shop link only in navigation
            const shopLinks = navContainer.querySelectorAll('a');
            shopLinks.forEach(link => {
              if (link.textContent?.trim() === 'Shop' &&
                  (link.href?.includes('sunrise-trade.myshopify.com/?country=MY') ||
                   link.href?.includes('sunrise-trade.myshopify.com') && !link.href?.includes('/account'))) {
                const listItem = link.closest('li');
                if (listItem) {
                  listItem.style.display = 'none';
                  console.log('✅ Hidden shop link on profile page:', link.href);
                }
              }
            });
  
            // Hide Policies and Settings links only in navigation
            const unwantedLinks = navContainer.querySelectorAll('a[href*="/policies"], a[href*="/account/settings"]');
            unwantedLinks.forEach(link => {
              const listItem = link.closest('li');
              if (listItem) {
                listItem.style.display = 'none';
                console.log('✅ Hidden unwanted link on profile page:', link.href);
              }
            });
          }
  
          console.log('✅ Navigation cleanup completed for profile page');
        } catch (error) {
          console.error('❌ Error hiding navigation items:', error);
        }
      }
  
      // Hide items with reduced frequency
      hideUnwantedNavItems();
      setTimeout(hideUnwantedNavItems, 1000);
  
      // Set up mutation observer only for navigation changes
      const observer = new MutationObserver((mutations) => {
        // Only react to navigation changes, not all DOM changes
        const hasNavChanges = mutations.some(mutation => {
          const target = mutation.target;
          return target.closest && (target.closest('#left-nav') || target.closest('nav') || target.closest('.drawer'));
        });
  
        if (hasNavChanges) {
          hideUnwantedNavItems();
        }
      });
  
      const navElement = document.querySelector('#left-nav') || document.querySelector('nav');
      if (navElement) {
        observer.observe(navElement, { childList: true, subtree: true });
      }
  
      const logoutSpan = Array.from(document.querySelectorAll('span'))
        .find(el => el.textContent === "Log out");
  
      if (logoutSpan) {
        const logoutButton = logoutSpan.closest("button");
        if (logoutButton) {
          logoutButton.addEventListener("click", function () {
            console.log("✅ Logout button clicked");
            window.ReactNativeWebView.postMessage(JSON.stringify({
              event: "custom_logout_clicked"
            }));
  
            const interval = setInterval(() => {
              const modal = document.getElementById("Modal0");
              if (modal) {
                modal.remove();
                clearInterval(interval);
              }
            }, 50);
          }, { once: true });
        } else {
          console.log("❌ Logout button not found");
        }
      } else {
        console.log("❌ Logout span not found");
      }
    })();
    true;
  `);
                  }
  
                  if (
                    navState.url.includes("/authentication/") &&
                    navState.url.includes("/login")
                  ) {
                    console.log(
                      "✅ Shopify logout complete, now navigating to login page"
                    );
  
                    // CRITICAL: Set logout flag FIRST to prevent auto-login
                    console.log(
                      "🚪 Setting JUST_LOGGED_OUT flag to prevent auto-navigation"
                    );
                    const clearAllData = async () => {
                      await CookieManager.clearAll();
                      await AsyncStorage.clear();
                      dispatch(clearCart());
                      // 2. Inject JS to clear WebView storage
                      if (Platform.OS === "ios") {
                        await CookieManager.clearAll(true);
                        await AsyncStorage.clear();
                        webViewRef.current?.injectJavaScript(clearWebViewDataScript);
                      }
                      webViewRef.current?.injectJavaScript(`
                  localStorage.clear();
                  sessionStorage.clear();
                  document.cookie.split(';').forEach(c => {
                    document.cookie = c.trim().split('=')[0] + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
                  });
                  true;
                `);
                      // 3. Reload WebView to reset session
                      webViewRef.current?.reload();
  
                    }
                    clearAllData();
                    AsyncStorage.setItem("JUST_LOGGED_OUT", "true")
                      .then(() => {
                        console.log("✅ JUST_LOGGED_OUT flag set successfully");
                        return AsyncStorage.multiRemove([
                          "isLoggedIn",
                          "customerData",
                          "customerToken",
                          "customerEmail",
                          "accountNumber",
                          "isCompanyVerified",
                          "isCompanyFormSubmitted",
                        ]);
                      })
                      .then(() => {
                        console.log(
                          "✅ AsyncStorage cleared, navigating to ShopifyLogin"
                        );
                        // Navigate to your SignIn screen
                        navigation.dispatch(
                          CommonActions.reset({
                            index: 0,
                            routes: [{ name: "ShopifyLogin" }],
                          })
                        );
                      })
                      .catch((err) => {
                        console.error("Logout cleanup error:", err);
                      });
                  }
                }}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                sharedCookiesEnabled={true}
                thirdPartyCookiesEnabled={true}
                startInLoadingState={true}
                onLoadStart={handleLoadStart}
                onLoadEnd={handleLoadEnd}
                style={{ flex: 1 }}
                onMessage={({ nativeEvent }: any) => {
                  console.log("nativeEvent", nativeEvent);
                  try {
                    const data = JSON.parse(nativeEvent.data);
                    if (data.event === "custom_logout_clicked") {
                      console.log("🚪 Detected logout button click via injection");
                      setLoading(true); // Your custom loader
                    }
                    // New logic for sign-out-button
                    if (data.event === 'logout_clicked') {
                      console.log('🚪 Detected <a.sign-out-button> logout click');
                      setLoading(true);
                    }
                  } catch (err) {
                    console.error("❌ Failed to parse WebView message:", err);
                  }
                }}
                renderLoading={() => (
                  <View
                    style={{
                      flex: 1,
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <ActivityIndicator size="large" />
                  </View>
                )}
                injectedJavaScript={`
              // Existing injected JS (if any)
              (function() {
                var logoutBtn = document.querySelector('a.sign-out-button[href*="/customer_identity/logout"]');
                if (logoutBtn) {
                  logoutBtn.addEventListener('click', function() {
                    window.ReactNativeWebView.postMessage(JSON.stringify({ event: 'logout_clicked' }));
                  });
                }
              })();
              true;
            `}
              />
            )}
            {loading && (
              <View
                style={{
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  zIndex: 9999,
                  justifyContent: "center",
                  alignItems: "center",
                  backgroundColor: "#fff",
                }}
              >
                <ActivityIndicator size="large" color={COLORS.primary} />
              </View>
            )}
            {/* <TaskModal
          visible={modalVisible}
          onClose={() => setModalVisible(false)}
          onOkay={handleOkay}
          taskText="Your task description or confirmation message goes here."
          title="Your Modal Title" // Optional
        />
        <Button
          title="Check Verification Status"
          // onPress={() => setModalVisible(true)}
          onPress={() => navigation.navigate("ApplicationStatus")}
        /> */}
  
            {/* Floating Action Button for Crashlytics Demo */}
            {__DEV__ && (
              <TouchableOpacity
                style={styles.crashlyticsFab}
                onPress={() => navigation.navigate("CrashlyticsDemo")}
              >
                <Text style={styles.crashlyticsFabText}>🔥</Text>
              </TouchableOpacity>
            )}
          </>
        )}
      </>
    );
  };
  
  const styles = StyleSheet.create({
    crashlyticsFab: {
      position: "absolute",
      bottom: 20,
      right: 20,
      width: 56,
      height: 56,
      borderRadius: 28,
      backgroundColor: "#FF6B35",
      justifyContent: "center",
      alignItems: "center",
      elevation: 8,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      zIndex: 1000,
    },
    crashlyticsFabText: {
      fontSize: 24,
      color: "#FFF",
    },
  });
  
  export default ProfileIOS;
  