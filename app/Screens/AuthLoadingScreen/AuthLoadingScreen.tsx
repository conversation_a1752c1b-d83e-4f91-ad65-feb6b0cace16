// AuthLoadingScreen.tsx
import React, { useEffect } from "react";
import { ActivityIndicator, View } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { COLORS } from "@/app/constants/theme";

const AuthLoadingScreen = ({ navigation }: any) => {
  // const SHOPIFY_ADMIN_TOKEN = "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";
  // const SHOPIFY_ADMIN_URL =
  //   "https://sunrise-trade.myshopify.com/admin/api/2025-01/graphql.json";
  console.log('AuthLoadingScreen.tsx')
  useEffect(() => {
    const checkLogin = async () => {
      const isLoggedIn = await AsyncStorage.getItem("isLoggedIn");
      const customerToken = await AsyncStorage.getItem("customerToken");
      const checkVerificationStatus = async () => {
        try {
          const storedCustomerData = await AsyncStorage.getItem("customerData");
          const customerData = JSON.parse(storedCustomerData || "{}");
          const customerId = customerData?.data?.customer?.id;

          if (!customerId) {
            console.warn("Customer ID not found in AsyncStorage");
            return;
          }

          const query = `
                query {
                  customer(id: "${customerId}") {
                    metafield(namespace: "custom", key: "verified") {
                      value
                    }
                  }
                }
              `;

          const response = await fetch(
            `${process.env.EXPO_PUBLIC_ADMIN_API_URL}`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "X-Shopify-Access-Token": `${process.env.EXPO_PUBLIC_ADMIN_ACCESS_TOKEN}`,
              },
              body: JSON.stringify({ query }),
            }
          );

          const result = await response.json();
          const verifiedStatus = result?.data?.customer?.metafield?.value;

          console.log("Verification status:", verifiedStatus);
          const isVerified = verifiedStatus === "true";
          console.log("verifiedStatus::", verifiedStatus);

          if (
            verifiedStatus === undefined ||
            verifiedStatus === null ||
            verifiedStatus === ""
          ) {
            await AsyncStorage.setItem("isCompanyVerified", "false");
          }
          else {
            await AsyncStorage.setItem("isCompanyVerified", "true");
          }
          // Store verification status in AsyncStorage

          // Return the verification status instead of navigating directly
          return verifiedStatus;
        } catch (error) {
          console.error("Verification check error:", error);
        }
      };
      const isCompanyVerified = await checkVerificationStatus();

      console.log("isCompanyVerified", isCompanyVerified);
      console.log("isLoggedIn>>>>>>type of :", isLoggedIn === "true");
      if (
        isLoggedIn === "true" &&
        customerToken &&
        isCompanyVerified === "true" || isCompanyVerified === "false"
      ) {
        navigation.reset({
          index: 0,
          routes: [
            {
              name: "DrawerNavigation",
              state: {
                index: 0,
                routes: [{ name: "Home" }],
              },
            },
          ],
        });
      } else if (
        isLoggedIn === "true" &&
        customerToken
      ) {
        navigation.reset({
          index: 0,
          routes: [{ name: "CompanyRegistration" }],
        });
      } else {
        navigation.reset({
          index: 0,
          routes: [{ name: "ShopifyLogin" }],
        });
      }

      // navigation.reset({
      //   index: 0,
      //   routes: [
      //     isLoggedIn === "true" && customerToken
      //       ? isCompanyVerified === true
      //         ? {
      //             name: "DrawerNavigation",
      //             state: {
      //               index: 0,
      //               routes: [{ name: "Home" }],
      //             },
      //           }
      //         : { name: "CompanyRegistration" }
      //       : { name: "ShopifyLogin" },
      //   ],
      // });
    };
    checkLogin();
  }, []);

  return (
    <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
      <ActivityIndicator size="large" color={COLORS.primary} />
    </View>
  );
};

export default AuthLoadingScreen;
