import crashlytics, {
  setCrashlyticsCollectionEnabled,
  log,
  setUserId,
  setAttributes,
  setAttribute,
  recordError
} from '@react-native-firebase/crashlytics';
import AsyncStorage from '@react-native-async-storage/async-storage';
/**
 * Firebase Crashlytics Service
 *
 * This service provides comprehensive crash reporting and error tracking
 * using Firebase Crashlytics SDK with modern v9+ modular API.
 */
class CrashlyticsService {
  private isInitialized = false;
  private initializationAttempted = false;

  /**
   * Get crashlytics instance - using the current approach until v22 migration is complete
   */
  private getCrashlyticsInstance() {
    return crashlytics();
  }

  /**
   * Initialize Crashlytics service
   * Returns true if successful, false if failed (but doesn't throw)
   */
  async initialize(): Promise<boolean> {
    // Prevent multiple initialization attempts
    if (this.initializationAttempted) {
      return this.isInitialized;
    }
    
    this.initializationAttempted = true;

    try {
      console.log('🔥 Initializing Firebase Crashlytics...');

      // Check if Crashlytics is available
      if (!crashlytics) {
        console.warn('⚠️ Crashlytics module not available');
        return false;
      }

      // Enable Crashlytics collection using v22 modular API
      const crashlyticsInstance = this.getCrashlyticsInstance();
      await setCrashlyticsCollectionEnabled(crashlyticsInstance, true);

      // Test basic functionality
      log(crashlyticsInstance, 'Crashlytics service initialized successfully');
      
      // Load and set user data if available
      await this.loadUserData();
      
      this.isInitialized = true;
      console.log('✅ Crashlytics initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Crashlytics:', error);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * Load user data from AsyncStorage and set in Crashlytics
   */
  private async loadUserData(): Promise<void> {
    try {
      const userId = await AsyncStorage.getItem('userId');
      const userEmail = await AsyncStorage.getItem('userEmail');
      
      if (userId) {
        await this.setUserId(userId);
      }
      
      if (userEmail) {
        await this.setUserEmail(userEmail);
      }
    } catch (error) {
      console.warn('⚠️ Failed to load user data for Crashlytics:', error);
    }
  }

  /**
   * Log a custom message to Crashlytics
   * Safe to call even if Crashlytics is not initialized
   */
  log(message: string, additionalData?: Record<string, any>): void {
    try {
      if (this.isInitialized) {
        let logMessage = message;
        if (additionalData) {
          logMessage += ` | Data: ${JSON.stringify(additionalData)}`;
        }
        const crashlyticsInstance = this.getCrashlyticsInstance();
        log(crashlyticsInstance, logMessage);
        console.log('📝 Logged to Crashlytics:', message);
      } else {
        console.log('📝 Crashlytics not available, logging to console:', message);
      }
    } catch (error) {
      console.error('❌ Failed to log to Crashlytics:', error);
    }
  }

  /**
   * Set user identifier for crash reports
   */
  async setUserId(userId: string): Promise<void> {
    try {
      if (this.isInitialized) {
        const crashlyticsInstance = this.getCrashlyticsInstance();
        await setUserId(crashlyticsInstance, userId);
        console.log('👤 User ID set in Crashlytics:', userId);
      } else {
        console.log('👤 Crashlytics not available, user ID not set:', userId);
      }
    } catch (error) {
      console.error('❌ Failed to set user ID:', error);
    }
  }

  /**
   * Set user email for crash reports
   */
  async setUserEmail(email: string): Promise<void> {
    try {
      if (this.isInitialized) {
        const crashlyticsInstance = this.getCrashlyticsInstance();
        await setAttributes(crashlyticsInstance, {
          userEmail: email,
        });
        console.log('📧 User email set in Crashlytics:', email);
      } else {
        console.log('📧 Crashlytics not available, user email not set:', email);
      }
    } catch (error) {
      console.error('❌ Failed to set user email:', error);
    }
  }

  /**
   * Set custom attributes for crash reports
   */
  async setUserAttributes(attributes: Record<string, string>): Promise<void> {
    try {
      if (this.isInitialized) {
        const crashlyticsInstance = this.getCrashlyticsInstance();
        await setAttributes(crashlyticsInstance, attributes);
        console.log('🏷️ User attributes set in Crashlytics:', attributes);
      } else {
        console.log('🏷️ Crashlytics not available, attributes not set:', attributes);
      }
    } catch (error) {
      console.error('❌ Failed to set user attributes:', error);
    }
  }

  /**
   * Set custom key-value pairs for crash reports
   */
  async setCustomKey(key: string, value: string | number | boolean): Promise<void> {
    try {
      if (this.isInitialized) {
        const crashlyticsInstance = this.getCrashlyticsInstance();
        await setAttribute(crashlyticsInstance, key, String(value));
        console.log(`🔑 Custom key set in Crashlytics: ${key} = ${value}`);
      } else {
        console.log(`🔑 Crashlytics not available, key not set: ${key} = ${value}`);
      }
    } catch (error) {
      console.error('❌ Failed to set custom key:', error);
    }
  }

  /**
   * Record a non-fatal error
   */
  recordError(error: Error, context?: string): void {
    try {
      if (this.isInitialized) {
        const crashlyticsInstance = this.getCrashlyticsInstance();
        if (context) {
          log(crashlyticsInstance, `Error context: ${context}`);
        }
        recordError(crashlyticsInstance, error);
        console.log('🚨 Non-fatal error recorded:', error.message);
      } else {
        console.error('🚨 Crashlytics not available, error logged to console:', error.message);
        if (context) {
          console.error('Context:', context);
        }
      }
    } catch (recordingError) {
      console.error('❌ Failed to record error:', recordingError);
    }
  }

  /**
   * Record a custom error with message and additional data
   */
  recordCustomError(errorMessage: string, errorCode?: string, additionalData?: Record<string, any>): void {
    try {
      const error = new Error(errorMessage);
      if (errorCode) {
        (error as any).code = errorCode;
      }
      
      if (additionalData) {
        this.log(`Custom error data: ${JSON.stringify(additionalData)}`);
      }
      
      this.recordError(error, `Custom error${errorCode ? ` (${errorCode})` : ''}`);
    } catch (error) {
      console.error('❌ Failed to record custom error:', error);
    }
  }

  /**
   * Clear user data (useful for logout)
   */
  async clearUserData(): Promise<void> {
    try {
      if (this.isInitialized) {
        const crashlyticsInstance = this.getCrashlyticsInstance();
        await setUserId(crashlyticsInstance, '');
        await setAttributes(crashlyticsInstance, {});
        console.log('🧹 User data cleared from Crashlytics');
      }
    } catch (error) {
      console.error('❌ Failed to clear user data:', error);
    }
  }

  /**
   * Trigger a test crash (USE ONLY FOR TESTING!)
   * This will crash the app immediately
   */
  testCrash(): void {
    try {
      console.log('💥 Triggering test crash...');

      // Force crash by throwing an unhandled error that will be caught by Crashlytics
      // This is the most reliable way to test crash reporting
      setTimeout(() => {
        throw new Error('CRASHLYTICS TEST CRASH - This is intentional for testing Firebase Crashlytics');
      }, 100);
    } catch (error) {
      console.error('❌ Failed to trigger test crash:', error);
      // Force crash by throwing an unhandled error
      setTimeout(() => {
        throw new Error('CRASHLYTICS TEST CRASH - This is intentional for testing Firebase Crashlytics');
      }, 100);
    }
  }

  /**
   * Check if Crashlytics is properly initialized
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Get initialization status
   */
  getStatus(): { initialized: boolean; attempted: boolean } {
    return {
      initialized: this.isInitialized,
      attempted: this.initializationAttempted,
    };
  }
}

// Export a singleton instance
export default new CrashlyticsService();
