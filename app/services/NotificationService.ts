import { Platform, Alert, PermissionsAndroid } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { EventEmitter } from "events";
import { Notifications } from "react-native-notifications";
import {
  initializeFirebase,
  getMessagingInstance,
} from "../config/firebaseConfig";
import { AuthorizationStatus } from "@react-native-firebase/messaging";
import CrashlyticsService from "./CrashlyticsService";

class NotificationService {
  // Event emitter for notifications
  private eventEmitter: EventEmitter;
  private pushNotificationConfigured: boolean = false;
  private foregroundHandlerRegistered: boolean = false;
  private foregroundUnsubscribe: (() => void) | null = null;
  private tokenRefreshUnsubscribe: (() => void) | null = null;

  // Backend configuration for real system notifications
  private readonly BACKEND_URL = this.getBackendUrl();

  private getBackendUrl(): string {
    const SERVEO_URL = "https://6136eccfcaa5.ngrok-free.app";
    const NGROK_URL = "https://6136eccfcaa5.ngrok-free.app";
    const DEV_IP = "***********";
    const DEV_PORT = "3000";
    const ANDROID_EMULATOR_IP = "********";
    const PROD_URL = "https://your-backend-url.com";
    if (__DEV__) {
      if (SERVEO_URL) return SERVEO_URL;
      if (NGROK_URL) return NGROK_URL;
      // return `http://${ANDROID_EMULATOR_IP}:${DEV_PORT}`;
      // return `http://${DEV_IP}:${DEV_PORT}`;
    } else {
      return NGROK_URL;
    }
    // Fallback (should never hit)
    return PROD_URL;
  }

  constructor() {
    this.eventEmitter = new EventEmitter();
    this.configurePushNotification();
  }

  // Configure notifications for real system notifications
  private configurePushNotification() {
    if (this.pushNotificationConfigured) return;

    try {
      // Initialize react-native-notifications
      Notifications.registerRemoteNotifications();

      // Create notification channel for Android
      if (Platform.OS === "android") {
        this.createNotificationChannel();
      }

      // Set up notification event listeners
      Notifications.events().registerNotificationReceivedForeground(
        (notification, completion) => {
          console.log("📱 Notification received in foreground:", notification);
          completion({ alert: true, sound: true, badge: false });
        }
      );

      Notifications.events().registerNotificationOpened(
        (notification, completion) => {
          console.log("📱 Notification opened:", notification);
          completion();
        }
      );

      // Request permissions for both platforms
      this.requestNotificationPermissions();

      this.pushNotificationConfigured = true;
      console.log("✅ Notifications configured successfully");
    } catch (error) {
      console.error("❌ Error configuring notifications:", error);
      CrashlyticsService.recordError(
        error as Error,
        "NotificationService.configurePushNotification"
      );
    }
  }

  // Create notification channel for Android
  private createNotificationChannel() {
    if (Platform.OS === "android") {
      try {
        console.log("📱 Creating Android notification channel...");

        // Note: react-native-notifications handles channels automatically
        // We'll specify the channel when posting notifications
        console.log(
          "✅ Android notification channels will be created automatically"
        );
      } catch (error) {
        console.error("❌ Error creating notification channels:", error);
      }
    }
  }

  // Request notification permissions
  private async requestNotificationPermissions() {
    try {
      if (Platform.OS === "android") {
        console.log("📱 Requesting Android notification permissions...");

        // For Android 13+ (API level 33+), we need to request POST_NOTIFICATIONS permission
        if (Platform.Version >= 33) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
            {
              title: "Notification Permission",
              message: "This app needs access to show notifications",
              buttonNeutral: "Ask Me Later",
              buttonNegative: "Cancel",
              buttonPositive: "OK",
            }
          );

          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            console.log("✅ Android notification permission granted");
          } else {
            console.log("❌ Android notification permission denied");
          }
        } else {
          console.log(
            "✅ Android notification permissions not required for this version"
          );
        }
      } else {
        console.log(
          "📱 iOS permissions will be requested by Firebase messaging"
        );
      }
    } catch (error) {
      console.error("❌ Error requesting notification permissions:", error);
    }
  }

  // Subscribe to notification events
  onNotification(callback: (notification: any) => void) {
    this.eventEmitter.on("notification", callback);
    return () => this.eventEmitter.off("notification", callback);
  }

  // Cleanup method to properly unsubscribe handlers
  cleanup() {
    console.log("🧹 Cleaning up notification handlers...");

    if (this.foregroundUnsubscribe) {
      this.foregroundUnsubscribe();
      this.foregroundUnsubscribe = null;
      this.foregroundHandlerRegistered = false;
      console.log("✅ Foreground handler cleaned up");
    }

    if (this.tokenRefreshUnsubscribe) {
      this.tokenRefreshUnsubscribe();
      this.tokenRefreshUnsubscribe = null;
      console.log("✅ Token refresh listener cleaned up");
    }

    // Clear event emitter listeners
    this.eventEmitter.removeAllListeners();
    console.log("✅ Event emitter cleaned up");
  }

  // Initialize messaging service
  async initialize() {
    try {
      console.log("🔔 Initializing notification service...");

      // Initialize Firebase first
      const firebaseInitialized = await initializeFirebase();
      if (!firebaseInitialized) {
        console.error("❌ Firebase initialization failed");
        return false;
      }

      // Get messaging instance safely
      const messaging = await getMessagingInstance();

      // Request permission for iOS (Android doesn't need this explicit request)
      if (Platform.OS === "ios") {
        console.log(
          "📱 iOS device detected, requesting notification permissions..."
        );

        try {
          const authStatus = await messaging.requestPermission();
          console.log("📱 iOS permission status:", authStatus);

          // Check for valid permission statuses
          const enabled =
            authStatus === AuthorizationStatus.AUTHORIZED ||
            authStatus === AuthorizationStatus.PROVISIONAL;

          if (!enabled) {
            // Check if we're in simulator and handle gracefully
            if (__DEV__) {
              console.log(
                "⚠️ iOS permission not granted, but continuing in development mode"
              );
              console.log(
                "💡 In simulator, notifications have limitations. Use the test button to test local notifications."
              );

              // Try to request permissions using react-native-notifications as fallback
              try {
                console.log(
                  "🔄 Requesting permissions via react-native-notifications..."
                );
                Notifications.registerRemoteNotifications();
                console.log("✅ Fallback permission request completed");
              } catch (fallbackError) {
                console.log(
                  "⚠️ Fallback permission request failed:",
                  fallbackError
                );
              }
            } else {
              console.log("❌ Notification permission not granted on iOS");
              return false;
            }
          } else {
            console.log("✅ iOS notification permissions granted");
          }
        } catch (permissionError) {
          console.error(
            "❌ Error requesting iOS permissions:",
            permissionError
          );
          if (__DEV__) {
            console.log(
              "⚠️ Permission error in development mode, continuing anyway"
            );
          } else {
            return false;
          }
        }
      } else {
        console.log(
          "📱 Android device detected, checking notification permissions..."
        );
        // On Android, we can check the current permission status
        const enabled = await messaging.hasPermission();
        console.log("📱 Android permission status:", enabled);

        if (
          enabled === AuthorizationStatus.NOT_DETERMINED ||
          enabled === AuthorizationStatus.DENIED
        ) {
          // Request permissions on Android (this is optional but recommended)
          const authStatus = await messaging.requestPermission();
          console.log("📱 Android permission request result:", authStatus);

          if (authStatus === AuthorizationStatus.DENIED) {
            console.log("❌ Notification permission denied on Android");
            return false;
          }
        }
        console.log(
          "✅ Android notification permissions granted or already available"
        );
      }

      // Get FCM token (may fail in simulator)
      try {
        const fcmToken = await this.getFcmToken();
        if (fcmToken) {
          console.log("📝 FCM Token obtained:", fcmToken);
        } else {
          console.log("⚠️ FCM Token not available (normal in iOS simulator)");
        }
      } catch (tokenError) {
        console.error("❌ Error getting FCM token:", tokenError);
        if (__DEV__) {
          console.log(
            "⚠️ FCM token error in development mode, continuing anyway"
          );
        }
      }

      // Set up foreground message handler
      await this.setupForegroundHandler();
      console.log("✅ Foreground message handler set up");

      // Background message handler is set up in firebaseConfig.ts
      console.log(
        "✅ Background message handler will be set up by Firebase config"
      );

      console.log("🎉 Notification service initialized successfully");
      CrashlyticsService.log("Notification service initialized successfully");
      return true;
    } catch (error) {
      console.error("❌ Error initializing notifications:", error);
      CrashlyticsService.recordError(
        error as Error,
        "NotificationService.initialize"
      );
      return false;
    }
  }

  // Get FCM token and store it
  async getFcmToken() {
    try {
      console.log("🔑 Getting FCM token...");

      // Get messaging instance safely
      const messaging = await getMessagingInstance();

      // Check if we already have a token
      let fcmToken = await AsyncStorage.getItem("fcmToken");

      if (fcmToken) {
        console.log("🔑 Found existing FCM token in storage");

        // Verify if the token is still valid by getting a fresh one and comparing
        try {
          const freshToken = await messaging.getToken();
          if (freshToken && freshToken !== fcmToken) {
            console.log("🔄 Token has changed, updating stored token");
            fcmToken = freshToken;
            await AsyncStorage.setItem("fcmToken", fcmToken);
            // NOTE: We will send the token to the server upon login or next app start,
            // rather than here, to ensure it's associated with the correct user.
          }
        } catch (tokenError) {
          console.error(
            "⚠️ Error verifying token, will use stored token:",
            tokenError
          );
          if (__DEV__) {
            console.log(
              "⚠️ Token verification failed in development mode (normal in simulator)"
            );
          }
        }
      } else {
        console.log("🔑 No token found in storage, generating new token");
        try {
          // Get new token
          fcmToken = await messaging.getToken();

          if (fcmToken) {
            // Save the token
            await AsyncStorage.setItem("fcmToken", fcmToken);
            console.log("✅ New FCM Token generated and saved");

            // Mark that this is a new installation
            const isFirstInstall = await AsyncStorage.getItem("isFirstInstall");
            if (!isFirstInstall) {
              await AsyncStorage.setItem("isFirstInstall", "false");
              // We won't send the welcome notification from here anymore.
              // It will be triggered by onAppInstall.
            }
          } else {
            console.error("❌ Failed to generate FCM token");
            if (__DEV__) {
              console.log(
                "⚠️ FCM token generation failed (normal in iOS simulator)"
              );
            }
          }
        } catch (tokenGenerationError) {
          console.error("❌ Error generating FCM token:", tokenGenerationError);
          if (__DEV__) {
            console.log(
              "⚠️ FCM token generation error in development mode (normal in simulator)"
            );
          }
        }
      }

      // Log the token for debugging
      if (fcmToken) {
        console.log("📝 FCM Token:", fcmToken);
      }

      return fcmToken;
    } catch (error) {
      console.error("❌ Error getting FCM token:", error);
      return null;
    }
  }

  // Send FCM token to your backend server
  async sendTokenToServer(token: string, shopifyCustomerID: string) {
    try {
      console.log("📤 Sending FCM token to server...",shopifyCustomerID);

      // Get user data if available
      const customerData = await AsyncStorage.getItem("customerData");

      let userId = null;
      if (customerData) {
        try {
          const parsedData = JSON.parse(customerData);
          userId = parsedData?.data?.customer?.id;
        } catch (e) {
          console.warn("Could not parse customer data");
        }
      }

      // Prepare the payload
      const payload = {
        fcmToken: token,
        platform: Platform.OS,
        userId: userId,
        timestamp: new Date().toISOString(),
        appVersion: "1.0.0", // You can get this from your app config
        shopifyCustomerID,
      };

      console.log("📤 Token payload:", payload);

      // TODO: Replace with your actual backend endpoint
      const response = await fetch(
        `${this.BACKEND_URL}/api/register-fcm-token`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            shopifyCustomerID: payload.shopifyCustomerID,
            token: payload.fcmToken,
          }),
        }
      );

      if (response.ok) {
        console.log("✅ FCM token sent to server successfully");
        await AsyncStorage.setItem("tokenSentToServer", "true");
      } else {
        console.error(
          "❌ Failed to send FCM token to server:",
          response.status
        );
      }

      // For now, just log that we would send it
      console.log(
        "✅ FCM token ready to be sent to server (implement your backend endpoint)"
      );
      await AsyncStorage.setItem("tokenSentToServer", "true");
    } catch (error) {
      console.error("❌ Error sending FCM token to server:", error);
    }
  }

  // Send welcome notification for new installations
  async sendWelcomeNotification() {
    try {
      console.log("🎉 Sending welcome notification for new installation...");

      // Show a system welcome notification
      await this.showSystemNotification(
        "Welcome to Sunrise B2B! 🌅",
        "Thank you for installing our app. Get ready to explore amazing B2B opportunities!",
        {
          type: "welcome",
          action: "open_app",
        }
      );

      console.log("✅ Welcome notification sent");
    } catch (error) {
      console.error("❌ Error sending welcome notification:", error);
    }
  }

  // Set up foreground notification handler
  async setupForegroundHandler() {
    // Prevent multiple handler registrations
    if (this.foregroundHandlerRegistered) {
      console.log("🔔 Foreground handler already registered, skipping setup");
      return this.foregroundUnsubscribe;
    }

    console.log("🔔 Setting up foreground notification handler");
    const messaging = await getMessagingInstance();

    const unsubscribe = messaging.onMessage(async (remoteMessage: any) => {
      console.log("📬 Foreground Message received:", remoteMessage);

      // Check for duplicate messages using messageId
      const messageId = remoteMessage.messageId;
      if (messageId) {
        const lastProcessedMessage = await AsyncStorage.getItem(
          "lastProcessedMessageId"
        );
        if (lastProcessedMessage === messageId) {
          console.log("🚫 Duplicate message detected, skipping:", messageId);
          return;
        }
        // Store this message ID to prevent future duplicates
        await AsyncStorage.setItem("lastProcessedMessageId", messageId);
      }

      // Display a system notification even when the app is in the foreground.
      // The `showSystemNotification` function also handles storing the notification
      // for in-app display, so we don't need to call a separate handler.
      console.log("📱 Foreground message received, showing system notification.");

      const { title, body } = remoteMessage.notification || {};
      const data = remoteMessage.data || {};

      if (title && body) {
        // Pass the original messageId to ensure consistency
        await this.showSystemNotification(
          title,
          body,
          data,
          remoteMessage.messageId
        );
      } else {
        console.warn(
          "Foreground message received without title or body, storing data only."
        );
        // Fallback to storing data only if the notification part is missing
        await this.handleMessageDataOnly(remoteMessage);
      }
    });

    // Mark handler as registered and store unsubscribe function
    this.foregroundHandlerRegistered = true;
    this.foregroundUnsubscribe = unsubscribe;

    console.log("✅ Foreground handler registered successfully");
    return unsubscribe;
  }

  // Handle background messages
  async handleBackgroundMessage(remoteMessage: any) {
    console.log("📬 Background Message received:", remoteMessage);
    // Handle the message here
    return this.handleMessage(remoteMessage);
  }

  // Common message handler for both foreground and background
  async handleMessage(remoteMessage: any) {
    // Store the notification in AsyncStorage to display in the notifications screen
    try {
      console.log(
        "📩 Handling notification message:",
        JSON.stringify(remoteMessage, null, 2)
      );

      // Get existing notifications
      const existingNotifications = await AsyncStorage.getItem("notifications");
      console.log("📋 Retrieved existing notifications from storage");

      let notifications = [];
      try {
        notifications = existingNotifications
          ? JSON.parse(existingNotifications)
          : [];
        console.log(`📋 Found ${notifications.length} existing notifications`);
      } catch (parseError) {
        console.error(
          "❌ Error parsing existing notifications, resetting:",
          parseError
        );
        notifications = [];
      }

      // Add the new notification
      const newNotification = {
        id: remoteMessage.messageId || `notification-${Date.now()}`,
        title: remoteMessage.notification?.title || "New Notification",
        body: remoteMessage.notification?.body || "",
        data: remoteMessage.data || {},
        date: new Date().toISOString(),
        read: false,
      };

      console.log("✨ Created new notification object:", newNotification);

      // Add to the beginning of the array
      notifications = [newNotification, ...notifications];

      // Save the updated notifications
      await AsyncStorage.setItem(
        "notifications",
        JSON.stringify(notifications)
      );
      console.log("💾 Saved updated notifications to storage");

      // You can also emit an event here to update the UI if the app is in the foreground
      console.log("✅ Notification processing complete");

      return Promise.resolve();
    } catch (error) {
      console.error("❌ Error handling notification:", error);
      return Promise.reject(error);
    }
  }

  // Handle message data storage only (without showing notification)
  async handleMessageDataOnly(remoteMessage: any) {
    // Store the notification in AsyncStorage to display in the notifications screen
    try {
      console.log(
        "📩 Handling notification message:",
        JSON.stringify(remoteMessage, null, 2)
      );

      // Get existing notifications
      const existingNotifications = await AsyncStorage.getItem("notifications");
      console.log("📋 Retrieved existing notifications from storage");

      let notifications = [];
      try {
        notifications = existingNotifications
          ? JSON.parse(existingNotifications)
          : [];
        console.log(`📋 Found ${notifications.length} existing notifications`);
      } catch (parseError) {
        console.error(
          "❌ Error parsing existing notifications, resetting:",
          parseError
        );
        notifications = [];
      }

      // Add the new notification
      const newNotification = {
        id: remoteMessage.messageId || `notification-${Date.now()}`,
        title: remoteMessage.notification?.title || "New Notification",
        body: remoteMessage.notification?.body || "",
        data: remoteMessage.data || {},
        date: new Date().toISOString(),
        read: false,
      };

      console.log("✨ Created new notification object:", newNotification);

      // Add to the beginning of the array
      notifications = [newNotification, ...notifications];

      // Save the updated notifications
      await AsyncStorage.setItem(
        "notifications",
        JSON.stringify(notifications)
      );
      console.log("💾 Saved updated notifications to storage");

      console.log("✅ Notification processing complete");

      return Promise.resolve();
    } catch (error) {
      console.error("❌ Error handling notification:", error);
      return Promise.reject(error);
    }
  }

  // Store notification directly without processing (to avoid duplicates)
  async storeNotificationDirectly(notification: any) {
    try {
      // Get existing notifications
      const existingNotifications = await AsyncStorage.getItem("notifications");
      let notifications = [];

      try {
        notifications = existingNotifications
          ? JSON.parse(existingNotifications)
          : [];
      } catch (parseError) {
        console.error(
          "❌ Error parsing existing notifications, resetting:",
          parseError
        );
        notifications = [];
      }

      // Add to the beginning of the array
      notifications = [notification, ...notifications];

      // Keep only the last 100 notifications to avoid storage bloat
      if (notifications.length > 100) {
        notifications = notifications.slice(0, 100);
      }

      // Save the updated notifications
      await AsyncStorage.setItem(
        "notifications",
        JSON.stringify(notifications)
      );
      console.log("💾 Stored notification directly");

      return Promise.resolve();
    } catch (error) {
      console.error("❌ Error storing notification directly:", error);
      return Promise.reject(error);
    }
  }

  // Get all stored notifications
  async getNotifications() {
    try {
      const notifications = await AsyncStorage.getItem("notifications");
      return notifications ? JSON.parse(notifications) : [];
    } catch (error) {
      console.error("Error getting notifications:", error);
      return [];
    }
  }

  // Mark a notification as read
  async markAsRead(notificationId: string) {
    try {
      const existingNotifications = await AsyncStorage.getItem("notifications");
      if (!existingNotifications) return;

      let notifications = JSON.parse(existingNotifications);

      // Find and update the notification
      notifications = notifications.map((notification: any) => {
        if (notification.id === notificationId) {
          return { ...notification, read: true };
        }
        return notification;
      });

      // Save the updated notifications
      await AsyncStorage.setItem(
        "notifications",
        JSON.stringify(notifications)
      );
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  }

  // Delete a notification
  async deleteNotification(notificationId: string) {
    try {
      const existingNotifications = await AsyncStorage.getItem("notifications");
      if (!existingNotifications) return;

      let notifications = JSON.parse(existingNotifications);

      // Filter out the notification to delete
      notifications = notifications.filter(
        (notification: any) => notification.id !== notificationId
      );

      // Save the updated notifications
      await AsyncStorage.setItem(
        "notifications",
        JSON.stringify(notifications)
      );
    } catch (error) {
      console.error("Error deleting notification:", error);
    }
  }

  // Show a local notification (in-app only)
  async showLocalNotification(title: string, body: string, data: any = {}) {
    try {
      console.log("📱 Showing local notification:", title, body);

      // Create a notification object
      const notification = {
        id: `local-${Date.now()}`,
        title,
        body,
        data,
        date: new Date().toISOString(),
        read: false,
      };

      // Store the notification
      await this.handleMessage({
        messageId: notification.id,
        notification: {
          title,
          body,
        },
        data,
      });

      // Emit the notification event
      this.eventEmitter.emit("notification", notification);

      return notification;
    } catch (error) {
      console.error("❌ Error showing local notification:", error);
      return null;
    }
  }

  // Show a system notification (appears in notification tray)
  async showSystemNotification(
    title: string,
    body: string,
    data: any = {},
    messageId?: string
  ) {
    try {
      console.log("🔔 Showing system notification:", title, body);

      const notificationId = Date.now();

      if (Platform.OS === "android") {
        // Use native Android notification for guaranteed system tray display
        const { NativeModules } = require("react-native");

        try {
          // Try to use our custom native Android notification module
          if (NativeModules.NotificationModule) {
            const notificationData = {
              id: notificationId,
              title: title,
              body: body,
              channelId: "high-priority",
            };

            console.log(
              "📱 Using native Android notification module:",
              notificationData
            );
            await NativeModules.NotificationModule.showNotification(
              notificationData
            );
            console.log("✅ Native Android notification sent successfully");
          } else {
            throw new Error("Native notification module not available");
          }
        } catch (nativeError) {
          console.log(
            "⚠️ Native notification failed, using react-native-notifications:",
            nativeError
          );

          // Fallback to react-native-notifications
          const notificationPayload: any = {
            identifier: messageId || notificationId.toString(),
            title: title,
            body: body,
            sound: "default",
            badge: 1,
            payload: data,
            category: "sunrise_notifications",
            android: {
              channelId: "high-priority",
              priority: "high",
              autoCancel: true,
              color: "#FF4081",
              vibrate: true,
              lights: true,
              ongoing: false,
              showWhen: true,
              when: Date.now(),
            },
          };

          console.log("📱 Posting fallback notification:", notificationPayload);
          Notifications.postLocalNotification(notificationPayload);
        }
      } else {
        // iOS notification
        const notificationPayload: any = {
          identifier: messageId || notificationId.toString(),
          title: title,
          body: body,
          sound: "default",
          badge: 1,
          payload: data,
          category: "sunrise_notifications",
        };

        console.log("📱 Posting iOS notification:", notificationPayload);
        Notifications.postLocalNotification(notificationPayload);
      }

      // Also store it for in-app display
      const notification = {
        id: messageId || `system-${notificationId}`,
        title,
        body,
        data,
        date: new Date().toISOString(),
        read: false,
      };

      // Store the notification directly without calling handleMessage to avoid duplicates
      await this.storeNotificationDirectly(notification);

      // Emit the notification event
      this.eventEmitter.emit("notification", notification);

      console.log("✅ System notification sent successfully");
      return notification;
    } catch (error) {
      console.error("❌ Error showing system notification:", error);
      return null;
    }
  }

  // Send notification via backend server (for real system notifications)
  async sendNotificationViaBackend(notificationData: {
    token: string;
    title: string;
    body: string;
    data?: any;
  }) {
    try {
      console.log(
        "🔥 Sending notification via backend:",
        notificationData.title
      );

      const response = await fetch(
        `${this.BACKEND_URL}/api/send-notification`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            // Add your authentication headers here
            // 'Authorization': 'Bearer YOUR_API_KEY',
          },
          body: JSON.stringify({
            token: notificationData.token,
            notification: {
              title: notificationData.title,
              body: notificationData.body,
            },
            data: {
              ...notificationData.data,
              timestamp: new Date().toISOString(),
            },
            android: {
              priority: "high",
              notification: {
                title: notificationData.title,
                body: notificationData.body,
                sound: "default",
                color: "#FF4081",
              },
            },
            apns: {
              payload: {
                aps: {
                  alert: {
                    title: notificationData.title,
                    body: notificationData.body,
                  },
                  sound: "default",
                  badge: 1,
                },
              },
            },
          }),
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log("✅ Backend notification sent successfully:", result);
        return result;
      } else {
        throw new Error(`Backend responded with status: ${response.status}`);
      }
    } catch (error) {
      console.error("❌ Error sending notification via backend:", error);
      throw error;
    }
  }

  // Send a real system notification via Firebase (requires backend implementation)
  async sendFirebaseNotification(
    title: string,
    body: string,
    data: any = {},
    targetToken?: string
  ) {
    try {
      console.log("🔥 Sending Firebase notification:", title, body);

      // Get FCM token if not provided
      const fcmToken = targetToken || (await this.getFcmToken());

      if (!fcmToken) {
        console.error("❌ No FCM token available for sending notification");
        return null;
      }

      // Try to send via backend first for real system notifications
      try {
        await this.sendNotificationViaBackend({
          token: fcmToken,
          title: title,
          body: body,
          data: data,
        });

        console.log("✅ Real system notification sent via backend");
        return { success: true, method: "backend" };
      } catch (backendError) {
        console.warn(
          "⚠️ Backend notification failed, falling back to local notification:",
          backendError
        );

        // Fallback to local notification
        await this.showSystemNotification(title, body, data);
        return { success: true, method: "local_fallback" };
      }
    } catch (error) {
      console.error("❌ Error sending Firebase notification:", error);
      return null;
    }
  }

  // Show both local and system notification
  async showNotification(
    title: string,
    body: string,
    data: any = {},
    systemNotification: boolean = true
  ) {
    try {
      if (systemNotification) {
        return await this.showSystemNotification(title, body, data);
      } else {
        return await this.showLocalNotification(title, body, data);
      }
    } catch (error) {
      console.error("❌ Error showing notification:", error);
      return null;
    }
  }

  // Handle user login success - re-register notifications with user context
  async onLoginSuccess(userData: any) {
    try {
      console.log("🔐 User logged in, processing notifications...");
      console.log("🔑 User data:", userData);

      // Extract Shopify Customer ID from userData
      const shopifyCustomerId = userData?.customerData?.data?.customer?.id;
      
      if (!shopifyCustomerId) {
        console.error(
          "❌ onLoginSuccess: Shopify Customer ID not found in userData."
        );
        return;
      }

      console.log("🔑 Shopify Customer ID:", shopifyCustomerId);

      // 1. Get FCM token (it will be fetched from storage or generated)
      const fcmToken = await this.getFcmToken();

      if (!fcmToken) {
        console.error("❌ onLoginSuccess: Could not get FCM token.");
        return;
      }
      console.log("📱 FCM Token:", fcmToken);

      // 2. Register token with backend and set up auto-refresh listener.
      // This is the single, reliable point of registration after login.
      await this.registerFCMToken(shopifyCustomerId, fcmToken);
      await this.autoRegisterFCMToken(shopifyCustomerId);

      // 3. Send login success notification, but only once per session.
      const lastLoginNotification = await AsyncStorage.getItem(
        "lastLoginNotification"
      );
      if (lastLoginNotification) {
        console.log(
          "Login notification already sent for this session. Skipping."
        );
      } else {
        console.log("🚀 Sending login success notification...");
        await this.sendNotificationViaBackend({
          token: fcmToken,
          title: "Login Successful! 🎉",
          body: "Welcome back! You're now logged in to Sunrise B2B.",
          data: {
            type: "login_success",
            userId: shopifyCustomerId,
            action: "open_dashboard",
          },
        });
        // Set a flag to prevent sending the notification again until the user logs out.
        await AsyncStorage.setItem(
          "lastLoginNotification",
          new Date().toISOString()
        );
      }

      console.log("✅ Login notification flow complete.");
    } catch (error) {
      console.error("❌ Error in onLoginSuccess:", error);
      CrashlyticsService.recordError(
        error as Error,
        "NotificationService.onLoginSuccess"
      );
    }
  }

  // Handle user logout - clear notification state
  async onLogout() {
    try {
      console.log("🚪 User logged out, clearing notification state...");

      if (this.tokenRefreshUnsubscribe) {
        this.tokenRefreshUnsubscribe();
        this.tokenRefreshUnsubscribe = null;
        console.log("✅ Token refresh listener removed on logout.");
      }

      // Clear the login notification timestamp to allow new notifications on next login
      await AsyncStorage.removeItem("lastLoginNotification");

      console.log("✅ Logout notification cleanup complete");
    } catch (error) {
      console.error("❌ Error handling logout notification cleanup:", error);
    }
  }

  // Handle app installation/first launch
  async onAppInstall() {
    try {
      console.log("📱 Handling app installation notification setup...");

      const isFirstLaunch = await AsyncStorage.getItem("isFirstLaunch");

      if (!isFirstLaunch) {
        console.log("🎉 First app launch detected!");

        // Mark as not first launch anymore
        await AsyncStorage.setItem("isFirstLaunch", "false");

        // Initialize notifications
        const initialized = await this.initialize();

        if (initialized) {
          // Send welcome notification via Firebase for real system notification
          setTimeout(async () => {
            const fcmToken = await this.getFcmToken();
            if (fcmToken) {
              await this.sendNotificationViaBackend({
                token: fcmToken,
                title: "Welcome to Sunrise B2B! 🌅",
                body: "Discover amazing B2B opportunities. Tap to get started!",
                data: {
                  type: "welcome",
                  action: "open_onboarding",
                },
              });
            }
          }, 5000); // 5 second delay to ensure FCM token is ready
        }

        console.log("✅ App installation notification setup complete");
      } else {
        console.log(
          "📱 App already launched before, skipping welcome notification"
        );
        // Still initialize notifications for returning users
        await this.initialize();
      }
    } catch (error) {
      console.error("❌ Error handling app installation:", error);
    }
  }

  // Handle company verification success
  async onCompanyVerified() {
    try {
      console.log("🏢 Company verified, sending notification...");

      await this.showSystemNotification(
        "Company Verified! ✅",
        "Your company has been successfully verified. You now have full access to all features.",
        {
          type: "company_verified",
          action: "open_dashboard",
        }
      );

      console.log("✅ Company verification notification sent");
    } catch (error) {
      console.error(
        "❌ Error sending company verification notification:",
        error
      );
    }
  }

  // Get notification statistics
  async getNotificationStats() {
    try {
      const notifications = await this.getNotifications();
      const unreadCount = notifications.filter((n: any) => !n.read).length;

      return {
        total: notifications.length,
        unread: unreadCount,
        read: notifications.length - unreadCount,
      };
    } catch (error) {
      console.error("❌ Error getting notification stats:", error);
      return { total: 0, unread: 0, read: 0 };
    }
  }

  /**
   * Register FCM token with backend for a Shopify customer
   * @param shopifyCustomerId Shopify GID string
   * @param fcmToken Optional FCM token to register. If not provided, it will be fetched.
   */
  async registerFCMToken(shopifyCustomerID: string, fcmToken?: string) {
    console.log(
      "Registering FCM Token...",
      shopifyCustomerID,
      fcmToken ? "with provided token" : "fetching new token"
    );

    try {
      let tokenToRegister = fcmToken;

      if (!tokenToRegister) {
        const messaging = await getMessagingInstance();
        tokenToRegister = await messaging.getToken();
      }

      if (!tokenToRegister) {
        console.error("❌ No FCM token available for registration");
        return { success: false, error: "No FCM token" };
      }

      await AsyncStorage.setItem("fcmToken", tokenToRegister);

      const payload = {
        shopifyCustomerID,
        token: tokenToRegister,
      };
      const url = `${this.BACKEND_URL}/api/register-fcm-token`;
      let attempt = 0;
      const maxAttempts = 3;
      let lastError = null;

      while (attempt < maxAttempts) {
        try {
          const res = await fetch(url, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          });

          if (res.ok) {
            const result = await res.json();
            console.log("✅ FCM token registered:", result);
            return result;
          } else {
            lastError = await res.text();
            console.warn(`Attempt ${attempt + 1} failed:`, lastError);
          }
        } catch (err) {
          lastError = err;
          console.warn(`Attempt ${attempt + 1} failed with error:`, err);
        }
        attempt++;
        if (attempt < maxAttempts) {
          await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
        }
      }

      console.error(
        "❌ Failed to register FCM token after retries:",
        lastError
      );
      return { success: false, error: lastError };
    } catch (error) {
      console.error("❌ Error in registerFCMToken:", error);
      return { success: false, error };
    }
  }

  // Auto-register FCM token on token refresh
  async autoRegisterFCMToken(shopifyCustomerId: string) {
    console.log("Setting up FCM token refresh listener...");
    const messaging = await getMessagingInstance();

    if (this.tokenRefreshUnsubscribe) {
      this.tokenRefreshUnsubscribe();
      console.log("Removed existing token refresh listener.");
    }

    this.tokenRefreshUnsubscribe = messaging.onTokenRefresh(
      async (token: string) => {
        console.log("🔄 FCM token refreshed, re-registering with backend...");
        await this.registerFCMToken(shopifyCustomerId, token);
      }
    );

    console.log("✅ FCM token refresh listener set up.");
  }
}

export default new NotificationService();
