import { View, Text, Image, TouchableOpacity } from "react-native";
import React from "react";
import Badge from "../Badge/Badge";
import { COLORS, FONTS, FONTWEIGHT } from "../../constants/theme";
import { useNavigation } from "@react-navigation/native";

export default function SingleBannerImage({
  imgURL,
  title = "",
  badge,
  handle,
  imgStyles,
}: any) {
  const navigation = useNavigation();
  return (
    <TouchableOpacity
      style={{
        backgroundColor: COLORS.background,
        // marginRight: imgStyles ? 3 : 0,
      }}
      disabled={!handle}
      onPress={() => {
        if (!handle) {
          return;
        }
        navigation.navigate("ProductListingPage", { handle });
      }}
    >
      {badge?.badgeTitle && (
        <View
          style={{
            position: "absolute",
            zIndex: 10000,
            width: "100%",
            marginHorizontal: 5,
          }}
        >
          <Badge
            title={badge?.badgeTitle}
            color={COLORS.white}
            size="md"
            style={{
              position: "absolute",
              zIndex: 10000,
              borderRadius: 8,
              marginTop: 5,
              // top: 10,
              // left: 10,
              marginHorizontal: 5,
              ...FONTS.fontMedium,
              ...FONTWEIGHT.Bold,
            }}
          />
        </View>
      )}
      <View style={{ position: "absolute", zIndex: 10000, width: "100%" }}>
        <Text
          style={{
            color: "white",
            fontSize: 25,
            top: 135,
            fontWeight: "600",
            marginHorizontal: 10,
          }}
        >
          {title}
        </Text>
      </View>
      <Image
        source={{
          uri: imgURL,
        }}
        style={
          !imgStyles
            ? {
              width: "100%",
              height: undefined,
              aspectRatio: 2 / 1,
              borderRadius: 10,
              objectFit: "fill",
            }
            : { ...imgStyles }
        }
      />
    </TouchableOpacity>
  );
}
export function SingleSmallBannerImage({
  imgURL,
  title = "",
  badge,
  handle,
  fullImage,
}: any) {
  const navigation = useNavigation();
  return (
    <TouchableOpacity
      style={{ backgroundColor: COLORS.background, paddingHorizontal: 15 }}
      disabled={!handle}
      onPress={() => {
        if (!handle) {
          return;
        }
        navigation.navigate("ProductListingPage", { handle });
      }}
    >
      {badge?.badgeTitle && (
        <View
          style={{
            position: "absolute",
            zIndex: 10000,
            width: "100%",
            marginHorizontal: 5,
          }}
        >
          <Badge
            title={badge?.badgeTitle}
            color={COLORS.white}
            size="md"
            style={{
              position: "absolute",
              zIndex: 10000,
              borderRadius: 8,
              marginTop: 5,
              top: 10,
              left: 10,
              marginHorizontal: 5,
              ...FONTS.fontMedium,
              ...FONTWEIGHT.Bold,
            }}
          />
        </View>
      )}
      <View style={{ position: "absolute", zIndex: 10000, width: "100%" }}>
        <Text
          style={{
            color: "white",
            fontSize: 25,
            top: 135,
            fontWeight: "600",
            marginHorizontal: 10,
          }}
        >
          {title}
        </Text>
      </View>
      <View
        style={{
          width: "100%", // take full available width
          height: 141,
          // borderWidth:1,
          marginRight: 5,
          borderRadius: 10,
          overflow: "hidden",
          // marginHorizontal:10
          // borderWidth:1
        }}
      >
        <Image
          source={{ uri: imgURL }}
          style={{
            width: "100%",
            height: "100%",
            objectFit: "fill",
          }}
        />
      </View>
    </TouchableOpacity>
  );
}
