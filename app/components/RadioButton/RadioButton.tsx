import { View, TouchableOpacity, Text, Platform } from "react-native";
import React from "react";
import { RadioButton as PaperRadioButton, useTheme } from "react-native-paper";
import { COLORS, FONTS } from "../../constants/theme";

interface RadioButtonProps {
  label: string;
  value: string;
  status: 'checked' | 'unchecked';
  onPress: () => void;
  color?: string;
  labelStyle?: any;
  style?: any;
}

export default function RadioButton({ 
  label, 
  value, 
  status, 
  onPress, 
  color = COLORS.primary,
  labelStyle,
  style 
}: RadioButtonProps) {
  const theme = useTheme();
  const { colors }: { colors: any } = theme;

  // iOS-specific custom radio button
  if (Platform.OS === 'ios') {
    return (
      <TouchableOpacity
        style={[
          {
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 8,
            paddingHorizontal: 16,
            justifyContent: 'space-between',
            width: '100%',
          },
          style
        ]}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <Text
          style={[
            {
              ...FONTS.font,
              color: colors.text,
              flex: 1,
              textAlign: 'left',
            },
            labelStyle
          ]}
        >
          {label}
        </Text>
        <View
          style={{
            width: 22,
            height: 22,
            borderRadius: 11,
            borderWidth: 2,
            borderColor: status === 'checked' ? color : '#666666',
            backgroundColor: 'transparent',
            marginLeft: 14,
            marginRight: 0,
            alignItems: 'center',
            justifyContent: 'center',
            alignSelf: 'flex-end',
            // iOS-specific shadow for better visibility
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 1,
            },
            shadowOpacity: 0.1,
            shadowRadius: 1,
            elevation: 1,
          }}
        >
          {status === 'checked' && (
            <View
              style={{
                width: 12,
                height: 12,
                borderRadius: 6,
                backgroundColor: color,
              }}
            />
          )}
        </View>
      </TouchableOpacity>
    );
  }

  // Android - use react-native-paper
  return (
    <PaperRadioButton.Item
      label={label}
      value={value}
      status={status}
      onPress={onPress}
      color={color}
      position="trailing"
      labelStyle={[
        {
          ...FONTS.font,
          color: colors.text,
          textAlign: 'left',
        },
        labelStyle
      ]}
      style={[
        {
          paddingHorizontal: 16,
        },
        style
      ]}
    />
  );
}

// RadioButton Group component for iOS compatibility
interface RadioButtonGroupProps {
  onValueChange: (value: string) => void;
  value: string;
  children: React.ReactNode;
}

export function RadioButtonGroup({ onValueChange, value, children }: RadioButtonGroupProps) {
  if (Platform.OS === 'ios') {
    return (
      <View>
        {React.Children.map(children, (child) => {
          if (React.isValidElement(child)) {
            return React.cloneElement(child, {
              status: child.props.value === value ? 'checked' : 'unchecked',
              onPress: () => onValueChange(child.props.value),
            });
          }
          return child;
        })}
      </View>
    );
  }

  // Android - use react-native-paper
  return (
    <PaperRadioButton.Group onValueChange={onValueChange} value={value}>
      {children}
    </PaperRadioButton.Group>
  );
}
