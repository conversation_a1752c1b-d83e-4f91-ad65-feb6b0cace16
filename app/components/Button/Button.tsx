import React, { useState, useRef, useEffect } from "react";
import { Text, TouchableOpacity, View, TextInput, Alert, Platform } from "react-native";
import { COLORS, FONTS, SIZES } from "../../constants/theme";
import { useTheme } from "@react-navigation/native";
import { ActivityIndicator } from "react-native-paper";

type Props = {
  title: string;
  onPress?: any;
  onQuantityDecrease?: () => void;
  onQuantityIncrease?: () => void;
  color?: any;
  style?: object;
  size?: any;
  text?: string;
  btnRounded?: any;
  badge?: any;
  icon?: any;
  fullWidth?: any;
  outline?: any;
  height?: any;
  leftIcon?: any;
  rightIcon?: any;
  rightDisabled?: boolean;
  loading?: boolean;
  useTextInput?: boolean;
  onTextChange?: (text: string) => void;
  textInputValue?: string;
  maxLength?: number;
  disabled?: boolean
};

const Button = ({
  title,
  color,
  onPress,
  style,
  size,
  badge,
  btnRounded,
  text,
  icon,
  fullWidth,
  outline,
  height,
  leftIcon = false,
  rightIcon = false,
  onQuantityDecrease = () => { },
  onQuantityIncrease = () => { },
  rightDisabled = false,
  loading = false,
  useTextInput = false,
  onTextChange,
  textInputValue,
  maxLength = 7,
  disabled = false
}: Props) => {
  const theme = useTheme();
  const { colors }: { colors: any } = theme;

  // TextInput state management (similar to QuantityInputBox)
  const [localValue, setLocalValue] = useState(
    String(textInputValue || title || "")
  );
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);
  const lastExternalValue = useRef(textInputValue || title);

  // Sync with external value changes
  useEffect(() => {
    const newValueStr = String(textInputValue || title || "");
    if (
      (textInputValue || title) !== lastExternalValue.current &&
      newValueStr !== localValue
    ) {
      lastExternalValue.current = textInputValue || title;
      setLocalValue(newValueStr);
    }
  }, [textInputValue, title]);

  const handleTextChange = (text: string) => {
    console.log("TextInput changed:", text);
    const numeric: any = text.replace(/[^0-9]/g, "");

    if (numeric === "0" || numeric === "" || numeric === undefined) {
      setLocalValue("1")
      return Alert.alert("Add atleast one product");
    }
    setLocalValue(numeric);

    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    if (numeric !== "" && onTextChange) {
      debounceTimer.current = setTimeout(() => {
        lastExternalValue.current = numeric;
        onTextChange(numeric);
      }, 500);
    }
  };

  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);

  return (
    <TouchableOpacity
      disabled={disabled || rightDisabled || loading || (leftIcon && rightIcon && useTextInput)}
      activeOpacity={0.8}
      onPress={() => onPress && onPress()}
      delayPressIn={0}
      style={[
        {
          height: outline ? 48 : 55,
          paddingHorizontal: 25,
          paddingVertical: 13,
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          borderRadius: btnRounded ? 30 : SIZES.radius,
          
          backgroundColor: color
            ? color
            : outline
              ? colors.card
              : disabled || rightDisabled || loading || (leftIcon && rightIcon && useTextInput)?COLORS.lightgray:COLORS.primary,
          borderWidth: outline ? 1 : null,
          borderColor: theme.dark
            ? COLORS.white
            : outline
              ? colors.borderColor
              : null,
        },
        size === "sm" && {
          paddingHorizontal: 25,
          paddingVertical: 10,
          height: height ? 46 : 40,
        },
        size === "lg" && {
          paddingHorizontal: 35,
          paddingVertical: 16,
          height: 58,
        },
        icon && {
          paddingLeft: 65,
          paddingRight: fullWidth ? 65 : 25,
        },
        style && { ...style },
      ]}
    >
      {icon && (
        <View
          style={{
            height: outline ? 48 : 55,
            width: outline ? 48 : 55,
            borderRadius: 55,
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: outline
              ? colors.title
              : theme.dark
                ? COLORS.title
                : COLORS.white,
            borderWidth: outline ? 0 : 2,
            borderColor: color ? color : outline ? "" : COLORS.primary,
            position: "absolute",
            left: 0,
          }}
        >
          {icon}
        </View>
      )}
      {loading ? (
        <View
          style={{
            position: "absolute",
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <ActivityIndicator size="small" color={COLORS.white} />
        </View>
      ) : (
        <Text
          style={[
            {
              ...FONTS.fontLg,
              textAlign: outline ? null : "center",
              flexDirectoin: "row",
              color: text
                ? text
                : outline
                  ? colors.title
                  : theme.dark
                    ? COLORS.title
                    : COLORS.white,
            },
            size === "sm" && {
              fontSize: 14,
            },
            size === "lg" && {
              fontSize: 18,
            },
            outline && {
              ...FONTS.fontMedium,
            },
          ]}
        >
          {leftIcon || rightIcon ? (
            <View
              style={{
                justifyContent: "center",
                alignItems: "center",
                flexDirection: "row",
                width: "100%",
                gap: 10,
              }}
              pointerEvents={Platform.OS === 'ios' ? 'box-none' : 'auto'}
            >
              {leftIcon && (
                <TouchableOpacity
                  style={{
                    justifyContent: "center",
                    zIndex: 1000,
                    elevation: Platform.OS === 'android' ? 1000 : 0
                  }}
                  onPress={(e) => {
                    console.log("Left button (decrease) pressed");
                    e.stopPropagation();
                    onQuantityDecrease();
                  }}
                  activeOpacity={0.7}
                  hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
                  delayPressIn={0}
                >
                  <Text
                    style={{
                      fontWeight: "500",
                      fontSize: 25,
                      width: 30,
                      textAlign: "center",
                    }}
                  >
                    {leftIcon}
                  </Text>
                </TouchableOpacity>
              )}
              {useTextInput ? (
                <View style={{
                  zIndex: 1000,
                  elevation: Platform.OS === 'android' ? 1000 : 0
                }}>
                  <TextInput
                    style={{
                      fontSize: 16,
                      width: 40,
                      // height: 31,
                      textAlign: "center",
                      // borderWidth: 1,
                      borderColor: "#ccc",
                      paddingVertical: 2,
                      paddingHorizontal: 4,
                      fontWeight: "500",
                      color: text
                        ? text
                        : outline
                          ? colors.title
                          : theme.dark
                            ? COLORS.title
                            : COLORS.white,
                    }}
                    keyboardType="numeric"
                    value={localValue}
                    onChangeText={handleTextChange}
                    maxLength={maxLength}
                    selectTextOnFocus={true}
                    returnKeyType="done"
                    onFocus={(e) => e.stopPropagation()}
                    onBlur={(e) => e.stopPropagation()}
                  />
                </View>
              ) : (
                <Text style={{ fontWeight: "500", fontSize: 14 }}>{title}</Text>
              )}
              {rightIcon && (
                <TouchableOpacity
                  style={{
                    zIndex: 1000,
                    elevation: Platform.OS === 'android' ? 1000 : 0
                  }}
                  onPress={(e) => {
                    console.log("Right button (increase) pressed");
                    e.stopPropagation();
                    onQuantityIncrease();
                  }}
                  activeOpacity={0.7}
                  hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
                  delayPressIn={0}
                >
                  <Text
                    style={{
                      fontWeight: "500",
                      fontSize: 20,
                      width: 30,
                      justifyContent: "center",
                      textAlign: "center",
                    }}
                  >
                    {rightIcon}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <Text style={{ borderWidth: 1 }}>{title}</Text>
          )}
          {badge && (
            <View style={{ marginVertical: -4, marginLeft: 8 }}>{badge()}</View>
          )}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export default Button;
