import React, { FlatList } from "react-native";
import VariantCard from "../VariantCard/VariantCard";
import { useEffect, useState } from "react";

export type Variant = {
  id: string;
  title: string;
  image: {
    src: string;
  };
  price: {
    amount: string;
    currencyCode: string;
  };
  availableForSale: boolean;
  selectedOptions?: { name: string; value: string }[];
  quantityAvailable?: number;
};

type VariantCardsContainerProp = {
  selectedVariantId?: string;
  variants: Variant[];
  handleVariantChange: (id: string) => void;
  handleColorSelect?: (color: string, variantId: string) => void;
  selectedColor?: string | null;
  setSelectedSize?: (size: string | null) => void;
  setActiveSizeIndex?: (idx: number | null) => void;
};

const VariantCardsContainer = ({
  selectedVariantId,
  variants,
  handleVariantChange,
  handleColorSelect,
  selectedColor,
  setSelectedSize,
  setActiveSizeIndex,
}: VariantCardsContainerProp) => {
  // Filter unique colors
  const uniqueColorTracker: Record<string, boolean> = {};
  const uniqueColorVariants = variants.filter((variant) => {
    const color = variant.selectedOptions?.find((o: any) => o.name.toLowerCase() === "color")?.value;
    if (!color || uniqueColorTracker[color]) return false;
    uniqueColorTracker[color] = true;
    return true;
  });

  useEffect(() => {
    if (!selectedVariantId && uniqueColorVariants.length > 0) {
      handleVariantChange(uniqueColorVariants[0].id);
      if (handleColorSelect) {
        const color: string = uniqueColorVariants[0].selectedOptions?.find((o: any) => o.name.toLowerCase() === "color")?.value || '';
        handleColorSelect(color, uniqueColorVariants[0].id);
      }
    }
  }, []);

  const renderItem = ({ item }: { item: Variant }) => {
    const color = item.selectedOptions?.find((o: any) => o.name.toLowerCase() === "color")?.value || '';
    return (
      <VariantCard
        selectedVariantId={selectedVariantId}
        variant={item}
        handleVariantChange={() => {
          handleVariantChange(item.id);
          if (handleColorSelect) {
            handleColorSelect(color, item.id);
            if (setSelectedSize) setSelectedSize(null);
            if (setActiveSizeIndex) setActiveSizeIndex(null);
          }
        }}
        selectedColor={selectedColor}
      />
    );
  };

  return (
    <FlatList
      data={uniqueColorVariants}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      horizontal
      contentContainerStyle={{gap:0}}
    />
  );
};

export default VariantCardsContainer;
