import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet, Alert, Clipboard } from 'react-native';
import { Notifications } from 'react-native-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface FirebaseTestButtonProps {
  visible?: boolean;
}

const FirebaseTestButton: React.FC<FirebaseTestButtonProps> = ({ visible = __DEV__ }) => {
  if (!visible) return null;

  const testLocalNotification = () => {
    try {
      const notificationPayload: any = {
        identifier: Date.now().toString(),
        title: '🔥 Firebase Test',
        body: 'This is a test notification from Firebase!',
        sound: 'default',
        badge: 1,
        payload: { 
          test: true, 
          timestamp: new Date().toISOString(),
          source: 'manual_test'
        },
        category: 'firebase_test',
        type: 'local',
        thread: 'firebase_test'
      };

      console.log('📱 Manual test notification:', notificationPayload);
      Notifications.postLocalNotification(notificationPayload);
      
      Alert.alert(
        'Test Notification Sent!',
        'Check your notification center or lock screen to see the notification.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('❌ Error posting test notification:', error);
      Alert.alert('Error', 'Failed to send test notification');
    }
  };

  const copyFCMToken = async () => {
    try {
      const fcmToken = await AsyncStorage.getItem('fcmToken');
      if (fcmToken) {
        await Clipboard.setString(fcmToken);
        Alert.alert(
          '📋 FCM Token Copied!',
          `Token copied to clipboard:\n\n${fcmToken.substring(0, 50)}...`,
          [
            { text: 'OK' },
            {
              text: 'Show Full Token',
              onPress: () => Alert.alert('Full FCM Token', fcmToken, [
                { text: 'Copy Again', onPress: () => Clipboard.setString(fcmToken) },
                { text: 'Close' }
              ])
            }
          ]
        );
        console.log('📋 FCM Token copied to clipboard:', fcmToken);
      } else {
        Alert.alert('❌ No FCM Token', 'No FCM token found. Make sure notifications are initialized.');
      }
    } catch (error) {
      console.error('❌ Error copying FCM token:', error);
      Alert.alert('Error', 'Failed to copy FCM token');
    }
  };

  const testFirebaseBackgroundHandler = () => {
    try {
      // Simulate a Firebase message for testing the background handler
      const mockFirebaseMessage = {
        messageId: Date.now().toString(),
        notification: {
          title: '🔥 Firebase Background Test',
          body: 'Testing Firebase background message handler'
        },
        data: {
          test: 'true',
          timestamp: new Date().toISOString(),
          source: 'background_test'
        }
      };

      console.log('📬 Simulating Firebase background message:', mockFirebaseMessage);
      
      // This would normally be handled by the background message handler
      const notificationPayload: any = {
        identifier: Date.now().toString(),
        title: mockFirebaseMessage.notification.title,
        body: mockFirebaseMessage.notification.body,
        sound: 'default',
        badge: 1,
        payload: mockFirebaseMessage.data,
        category: 'firebase_background',
        type: 'local',
        thread: 'firebase_background'
      };

      Notifications.postLocalNotification(notificationPayload);
      
      Alert.alert(
        'Background Handler Test!',
        'Simulated Firebase background message processing.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('❌ Error testing background handler:', error);
      Alert.alert('Error', 'Failed to test background handler');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔥 Firebase Testing (Dev Only)</Text>
      
      <TouchableOpacity style={styles.button} onPress={testLocalNotification}>
        <Text style={styles.buttonText}>Test Local Notification</Text>
      </TouchableOpacity>

      <TouchableOpacity style={[styles.button, styles.copyButton]} onPress={copyFCMToken}>
        <Text style={styles.buttonText}>📋 Copy FCM Token</Text>
      </TouchableOpacity>

      <TouchableOpacity style={[styles.button, styles.secondaryButton]} onPress={testFirebaseBackgroundHandler}>
        <Text style={styles.buttonText}>Test Background Handler</Text>
      </TouchableOpacity>
      
      <Text style={styles.note}>
        Note: Real Firebase push notifications require a physical device and proper APNs setup.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 100,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.8)',
    padding: 15,
    borderRadius: 10,
    minWidth: 200,
    zIndex: 1000,
  },
  title: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#FF6B35',
    padding: 10,
    borderRadius: 5,
    marginBottom: 8,
  },
  secondaryButton: {
    backgroundColor: '#4A90E2',
  },
  copyButton: {
    backgroundColor: '#28A745',
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 12,
    fontWeight: '600',
  },
  note: {
    color: '#ccc',
    fontSize: 10,
    textAlign: 'center',
    marginTop: 5,
    fontStyle: 'italic',
  },
});

export default FirebaseTestButton;
