import { View, TouchableOpacity, Text, Platform } from "react-native";
import React from "react";
import { Checkbox, useTheme } from "react-native-paper";
import { COLORS, FONTS, SIZES } from "../../constants/theme";
import Icon from "react-native-vector-icons/MaterialIcons";

export default function CheckBox({ label, styles, checkedState }: any) {
  const theme = useTheme();
  const { colors }: { colors: any } = theme;

  // iOS-specific custom checkbox
  if (Platform.OS === 'ios') {
    return (
      <TouchableOpacity
        style={[
          {
            flexDirection: 'row',
            alignItems: 'center',
            paddingVertical: 5,
          },
          styles
        ]}
        onPress={checkedState?.checkedUnChecked}
        activeOpacity={0.7}
      >
        <View
          style={{
            width: 22,
            height: 22,
            borderRadius: 4,
            borderWidth: 2,
            borderColor: checkedState?.show ? COLORS.primary : '#666666',
            backgroundColor: checkedState?.show ? COLORS.primary : 'transparent',
            marginRight: 12,
            alignItems: 'center',
            justifyContent: 'center',
            // iOS-specific shadow for better visibility
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 1,
            },
            shadowOpacity: 0.1,
            shadowRadius: 1,
            elevation: 1,
          }}
        >
          {checkedState?.show && (
            <Icon
              name="check"
              size={14}
              color={COLORS.white}
            />
          )}
        </View>
        <Text
          style={{
            ...FONTS.fontRegular,
            color: colors.text,
            flex: 1,
          }}
        >
          {label}
        </Text>
      </TouchableOpacity>
    );
  }

  // Android - use react-native-paper
  return (
    <View style={{ ...styles }}>
      <View>
        <Checkbox.Item
          position="leading"
          label={label}
          labelStyle={{
            textAlign: "left",
            width: "60%",
            ...FONTS.fontRegular,
            fontSize: SIZES.fontLg,
          }}
          style={{ paddingVertical: 5, padding: 0 }}
          uncheckedColor={colors.text}
          color={COLORS.primary}
          status={checkedState?.show ? "checked" : "unchecked"}
          onPress={checkedState?.checkedUnChecked}
        />
      </View>
    </View>
  );
}
