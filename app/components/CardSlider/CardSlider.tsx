import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  Platform,
  Dimensions,
} from "react-native";
import React from "react";
import { COLORS, FONTS, FONTWEIGHT, SIZES } from "../../constants/theme";
import { GlobalStyleSheet } from "../../constants/StyleSheet";
import RedirectIcon from "../../assets/icons/redirectionindicationicon.png";
import { useNavigation } from "@react-navigation/native";
export default function CardSlider({
  products,
  smallCard,
  addBackground,
  searchSuggectionCard = false,
  categoryCards = false,
}: any) {
  const navigation = useNavigation();
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentInset={{ borderWidth: 1 }}
      keyboardShouldPersistTaps="handled"
      scrollEventThrottle={18}
      decelerationRate="fast"
    >
      <View
        style={[
          GlobalStyleSheet.row,
          {
            flexWrap: "nowrap",
            marginHorizontal: !categoryCards && smallCard ? 16 : 0,
            marginRight: smallCard ? 25 : 25,
            // marginHorizontal: addBackground ? 0 : 5,
            gap: categoryCards ? 5 : 10,
          },
        ]}
      >
        {products.map((data: any, index: any) => {
          const handle = data?.url?.split("/")?.pop();
          return (
            <View
              style={[
                {
                  // marginBottom: addBackground ? 0 : 20,
                  width: smallCard ? 100 : 150,
                  height: smallCard ? 125 : 183,
                  backgroundColor: smallCard ? COLORS.card : COLORS.background,
                  borderRadius: 20,
                },
              ]}
              key={data.image + data.title}
            >
              <TouchableOpacity
                activeOpacity={0.7}
                style={{
                  borderWidth: 1,
                  borderColor: "white",
                  borderRadius: 20,
                  backgroundColor: addBackground
                    ? COLORS.background
                    : COLORS.card,
                }}
                onPress={() => {
                  navigation.navigate("ProductListingPage", {
                    handle: data?.handle ? data.handle : handle ? handle : null,
                    headerBarTitle: data?.title,
                  });
                }}
              >
                <Image
                  style={{
                    width: smallCard ? 100 : 150,
                    height: addBackground ? 70 : smallCard ? 100 : 138,
                    objectFit: "cover",
                    // aspectRatio: smallCard ? null : 2 / 2,
                    borderTopLeftRadius: 15,
                    borderTopRightRadius: 15,
                  }}
                  source={{
                    uri:
                      data?.imageUrl ??
                      "https://media.istockphoto.com/id/1761333789/photo/badminton-shuttlecocks-and-racket-placed-in-the-corner-of-a-synthetic-field.jpg?s=612x612&w=0&k=20&c=3rr4BZqe1rDWsCe6LF_YPCXZe6Um5jizc6d6n96U1Q4=",
                  }}
                />
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent: "space-evenly",
                    alignItems: "center",
                    marginVertical: smallCard ? 2 : 10,
                    paddingHorizontal: smallCard ? 8 : 16,
                    width: "100%",
                    // borderWidth:1
                  }}
                >
                  <Text
                    style={{
                      ...FONTS.fontSemiBold,
                      color: COLORS.black,
                      fontSize: SIZES.font,
                      width: "75%",
                      flexWrap: "wrap",
                    }}
                    numberOfLines={1}
                    ellipsizeMode="tail"
                  >
                    {data?.title}
                  </Text>
                  <Image
                    source={RedirectIcon}
                    style={{
                      width: smallCard ? 20 : 25,
                      height: smallCard ? 20 : 25,
                      // marginRight: 10,
                    }}
                  />
                </View>
              </TouchableOpacity>
            </View>
          );
        })}
      </View>
    </ScrollView>
  );
}
export function CardSliderCatetgory({
  products,
  smallCard,
  addBackground,
  searchSuggectionCard = false,
  categoryCards = false,
}: any) {
  const navigation = useNavigation();
  const { width: SCREEN_WIDTH } = Dimensions.get("window");

  const CARD_WIDTH = SCREEN_WIDTH * 0.22;
  const CARD_HEIGHT = CARD_WIDTH * 1.2;
  const IMAGE_WIDTH = CARD_WIDTH * 0.85;
  const IMAGE_HEIGHT = CARD_HEIGHT * 0.7;

  return (
    <ScrollView
      keyboardShouldPersistTaps="handled"
      scrollEventThrottle={18}
      decelerationRate="fast"
      contentContainerStyle={{
        flexDirection: "row",
        flexWrap: "wrap",
        // gap: 12,
        marginLeft: 8,
      }}
    >
      {products.map((data: any, index: any) => {
        const handle = data?.url?.split("/")?.pop();

        return (
          <View
            key={data.image + data.title}
            style={{
              marginBottom: 9,
              width: CARD_WIDTH,
              height: CARD_HEIGHT,
              backgroundColor: smallCard ? COLORS.card : COLORS.background,
              borderRadius: 20,

            }}
          >
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => {
                console.log("data?.handle>>", data?.handle)
                console.log("handle<<<", handle)
                navigation.navigate("ProductListingPage", {
                  handle: handle ? handle : null,
                  headerBarTitle: data?.title,
                });
              }}
              style={{
                borderWidth: 1,
                borderColor: "white",
                borderRadius: 20,
                backgroundColor: addBackground
                  ? COLORS.background
                  : COLORS.card,
              }}
            >
              <Image
                source={{
                  uri:
                    data?.imageUrl ??
                    "https://st2.depositphotos.com/1036970/6013/i/450/depositphotos_60134001-stock-photo-set-of-badminton-paddle-and.jpg",
                }}
                style={{
                  width: IMAGE_WIDTH,
                  height: IMAGE_HEIGHT,
                  borderTopLeftRadius: 15,
                  borderTopRightRadius: 15,
                  alignSelf: "center",
                  resizeMode: "cover",
                }}
              />
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginTop: smallCard ? 4 : 8,
                  paddingHorizontal: 10,
                  width: "100%",
                }}
              >
                <Text
                  style={{
                    ...FONTS.fontSemiBold,
                    color: COLORS.black,
                    fontSize: SIZES.fontXxs,
                    flex: 1,
                    marginRight: 6,
                  }}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {data?.title}
                </Text>
                <Image
                  source={RedirectIcon}
                  style={{
                    width: smallCard ? 13 : 24,
                    height: smallCard ? 13 : 24,
                  }}
                />
              </View>
            </TouchableOpacity>
          </View>
        );
      })}
    </ScrollView>
  );
}
