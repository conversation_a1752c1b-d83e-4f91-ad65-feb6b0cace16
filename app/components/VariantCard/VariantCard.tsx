import { Dimensions, Image, Text, TouchableOpacity, View } from "react-native";
import { Variant } from "../VariantCardsContainer/VariantCardsContainer";
import { COLORS, FONTS, FONTWEIGHT, SIZES } from "../../constants/theme";

type SingleVariantCardProp = {
  selectedVariantId: string;
  variant: Variant;
  handleVariantChange: (id: string) => void;
};
const VariantCard = ({
  selectedVariantId,
  variant,
  handleVariantChange,
}: SingleVariantCardProp) => {

  const SCREEN_WIDTH = Dimensions.get("window").width;
  console.log("variant>>>>>>", variant)
  return (
    <TouchableOpacity
      onPress={() => handleVariantChange(variant.id)}
      style={{
        alignItems: "center",
        marginHorizontal: 5,
        // borderWidth: 0.1,
        width: SCREEN_WIDTH * 0.2,
        borderColor: COLORS.gray,
        // padding: 10,
        borderRadius: 10,
        height: "auto",
      }}
    >
      <Image
        source={{ uri: variant?.image }}
        style={{
          width: 80,
          height: 80,
          //   borderWidth: 1,
          borderWidth: selectedVariantId === variant?.id ? 1 : 0,
          aspectRatio: 1 / 1,
          objectFit: "contain",
          borderRadius: 15,
        }}
      />
      <View style={{ width: "100%", marginTop: 5 }}>
        <Text

          style={{
            ...FONTS.fontSm,
            ...FONTWEIGHT.SemiBold,
            flexWrap: "wrap",
            textAlign: "center"
          }}
        >
          {variant.color}
        </Text>

      </View>
      <View style={{ width: "100%" }}>
        <Text style={{
          ...FONTS.fontXs,
          fontSize: SIZES.fontXxs,
          color: COLORS.red,
          marginVertical: 5,
        }}> {selectedVariantId === variant.id && variant?.quantityAvailable <= 0 ?
          "Quantity available for selected varient is 0" : null}</Text>
      </View>
    </TouchableOpacity>
  );
};

export default VariantCard;
