import React from "react";
import MultiSlider from "@ptomasroos/react-native-multi-slider";
import { useEffect, useState } from "react";
import { View, Text } from "react-native";
import { COLORS } from "../../constants/theme";

const PriceRangeSlider = ({ min, max, onChange, value }: any) => {
  const [values, setValues] = useState([min, max]);

  // ✅ sync parent state when it changes externally
  useEffect(() => {
    setValues([
      value?.min !== undefined ? value.min : min,
      value?.max !== undefined ? value.max : max,
    ]);
  }, [value, min, max]);

  const handleChange = (newValues: number[]) => {
    setValues(newValues);
  };

  const handleFinish = (newValues: number[]) => {
    onChange({ min: newValues[0], max: newValues[1] });
  };

  return (
    <MultiSlider
      values={values}
      min={min}
      max={max}
      step={1}
      onValuesChange={handleChange}
      onValuesChangeFinish={handleFinish}
      selectedStyle={{ backgroundColor: COLORS.primary, height: 4 }}
      unselectedStyle={{ backgroundColor: COLORS.lightgray, height: 4 }}
      markerStyle={{
        backgroundColor: COLORS.black,
        height: 20,
        width: 20,
        borderRadius: 10,
        borderWidth: 2,
        borderColor: "#fff",
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 2,
        elevation: 3,
      }}
      containerStyle={{ paddingHorizontal: 10 }}
      trackStyle={{ height: 6, borderRadius: 3 }}
      sliderLength={150}
    />
  );
};

export default PriceRangeSlider;
