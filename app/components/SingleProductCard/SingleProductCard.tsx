import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  StyleSheet,
  Platform,
  Keyboard,
} from "react-native";
import React, { useCallback, useRef, useState, useEffect } from "react";
import { COLORS, FONTS, FONTWEIGHT, SIZES } from "../../constants/theme";
import Badge from "../Badge/Badge";
import BottomNavModal from "../BottomSheetNew/BottomSheetNew";
import { IMAGES } from "../../constants/Images";
import CartIcon from "../../assets/icons/addtocart.png";
import { useMutation } from "@apollo/client";
import { ADD_LINES_TO_CART, CREATE_CART } from "../../api/cartQuery";
import { useDispatch, useSelector } from "react-redux";
import { setCartId } from "../../redux/reducer/cartReducer";
import CustomAlert from "../CustomAlert";
import { useCustomAlert } from "../../hooks/useCustomAlert";
import { LanguageState } from "@/app/redux/reducer/languageReducer";
import { staticTranslations } from "@/app/constants/staticTranslations";
import LineImage from "../../assets/line.png"; // adjust the path accordingly

type Props = {
  navigation?: any;
  data: any;
  badge?: any;
  handle?: any;
};
const { width: SCREEN_WIDTH } = Dimensions.get("window");
const CARD_WIDTH = SCREEN_WIDTH * 0.44;
const CARD_MARGIN = SCREEN_WIDTH * 0.03;
export default function SingleProductCard({
  navigation,
  data,
  badge,
  handle,
}: Props) {
  const [modalVisible, setModalVisible] = useState(false);

  const [activeIndex, setActiveIndex] = useState(0);
  const scrollRef = useRef(null);
  const [createCart, { loading, error }] = useMutation(CREATE_CART);
  const [addCart, { loading: addLoading }] = useMutation(ADD_LINES_TO_CART);
  const dispatch = useDispatch();
  const { cartId } = useSelector((state: any) => state.cart);
  const [selectedVariantId, setSelectedVariantId] = useState(
    data.variants[0].id
  );
  const [selectedSize, setSelectedSize] = useState(data?.sizes?.[0] || null);
  const [quantity, setQuantity] = useState(0);
  const { locale }: LanguageState = useSelector((state: any) => state.language);
  const staticLabels = locale === "en" ? staticTranslations.english : staticTranslations.malay;
  const [currentProductDetails, setCurrentProductDetails] = useState(null);
  const [isAddedToBagLoading, setIsAddedToBagLoading] = useState(false);
  const [selectedColor, setSelectedColor] = useState<string | null>(null);
  console.log(">>>>>>>", data.color)
  // Custom Alert Hook
  const {
    alertConfig,
    visible: alertVisible,
    showSuccess,
    showError,
    hideAlert,
  } = useCustomAlert();
  const openBottomSheet = useCallback(
    (product: any) => {
      // Always use the first variant for PLP
      console.log("product.variants >>", product.variants)
      const selectedVariant = product.variants && product.variants.length > 0
        ? product.variants[0]
        : null;
      setModalVisible(true);
      setCurrentProductDetails({
        ...product,
        selectedVariant,
      });
      // Ensure size is properly selected when opening
      if (data?.sizes?.length > 0 && !selectedSize) {
        setSelectedSize(data.sizes[0]);
        setActiveIndex(0);
      }
    },
    [modalVisible, currentProductDetails, data?.sizes, selectedSize]
  );
  const varientsColor = data?.variants?.map((item: any, index: number) => {
    return item.title.toLowerCase();
  });

  // Ensure selectedSize is properly initialized
  useEffect(() => {
    if (data?.sizes?.length > 0 && !selectedSize) {
      setSelectedSize(data.sizes[0]);
      setActiveIndex(0);
    }
  }, [data?.sizes, selectedSize]);
  const [keyboardOpen, setKeyboardOpen] = useState(false);

  useEffect(() => {
    const showSub = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillShow" : "keyboardDidShow",
      () => setKeyboardOpen(true)
    );
    const hideSub = Keyboard.addListener(
      Platform.OS === "ios" ? "keyboardWillHide" : "keyboardDidHide",
      () => setKeyboardOpen(false)
    );

    return () => {
      showSub.remove();
      hideSub.remove();
    };
  }, []);
  const closeBottomSheet = () => {
    setModalVisible(false);
    // Reset to default values when closing
    if (data?.sizes?.length > 0) {
      setSelectedSize(data.sizes[0]);
      setActiveIndex(0);
    } else {
      setSelectedSize(null);
      setActiveIndex(0);
    }
  };

  const handleAddToBag = () => {
    setIsAddedToBagLoading(true)
    // Remove all logic and error messages related to inventory limit
    // Do not check for maxAvailable or showError for exceeding inventory
    const selectedVariant = data.variants.find((v: any) => v.id === selectedVariantId);
    const safeQuantity = quantity; // No clamping
    if (!data?.sizes?.length) {
      // If no sizes are available, proceed without size selection
      const lines = [
        {
          quantity: safeQuantity,
          merchandiseId: selectedVariantId,
          attributes: [],
        },
      ];

      if (cartId) {
        addCart({
          variables: {
            cartId,
            lines: lines,
          },
        })
          .then((response: any) => {
            if (response.data.cartLinesAdd.userErrors.length > 0) {
              setIsAddedToBagLoading(false)
              closeBottomSheet(); // Close bottom sheet first

              setTimeout(() => {
                showError("Error", response.data.cartLinesAdd.userErrors[0].message);
              }, 500); // Wait for bottom sheet to close
            } else {
              closeBottomSheet(); // Close bottom sheet first
              setTimeout(() => {
                setIsAddedToBagLoading(false)
                showSuccess("Success", staticLabels?.Added_tocart_successfully, [
                  {
                    text: "OK",
                    onPress: () => {
                      const cartId = response.data.cartLinesAdd.cart.id;
                      dispatch(setCartId(cartId));
                      navigation.push("MyCart", {
                        cartId,
                        lines,
                      });
                    },
                  },
                ]);
              }, 500); // Wait for bottom sheet to close
            }
          })
          .catch((err) => {
            setIsAddedToBagLoading(false)
            closeBottomSheet(); // Close bottom sheet first
            setTimeout(() => {
              showError("Error", "Something went wrong. Please try again.");
            }, 300); // Wait for bottom sheet to close
            console.error("Cart addition error:", err);
          });
      } else {
        createCart({
          variables: {
            lines,
          },
        })
          .then((response: any) => {
            if (response.data.cartCreate.userErrors.length > 0) {
              setIsAddedToBagLoading(false)
              alert("Error: " + response.data.cartCreate.userErrors[0].message);
            } else {
              setIsAddedToBagLoading(false)
              closeBottomSheet(); // Close bottom sheet first
              setTimeout(() => {
                showSuccess("Success", staticLabels?.Added_tocart_successfully, [
                  {
                    text: "OK",
                    onPress: () => {
                      const cartId = response.data.cartCreate.cart.id;
                      dispatch(setCartId(cartId));
                      navigation.push("MyCart", {
                        cartId,
                        lines,
                      });
                    },
                  },
                ]);
              }, 500); // Wait for bottom sheet to close
            }
          })
          .catch((err) => {
            setIsAddedToBagLoading(false)
            alert("Something went wrong. Please try again.");
            console.error("Cart creation error:", err);
          });
      }
      return;
    }

    if (!selectedSize) {
      closeBottomSheet(); // Close bottom sheet first
      setTimeout(() => {
        showError(staticLabels?.error, staticLabels?.select_size_errMsg);
      }, 500); // Wait for bottom sheet to close
      return;
    }

    const lines = [
      {
        quantity: safeQuantity,
        merchandiseId: selectedVariantId,
        attributes: [
          {
            key: "Size",
            value: selectedSize,
          },
        ],
      },
    ];

    if (cartId) {
      addCart({
        variables: {
          cartId,
          lines: lines,
        },
      })
        .then((response: any) => {
          if (response.data.cartLinesAdd.userErrors.length > 0) {
            closeBottomSheet(); // Close bottom sheet first
            setTimeout(() => {
              setIsAddedToBagLoading(false)
              showError("Error", response.data.cartLinesAdd.userErrors[0].message);
            }, 500); // Wait for bottom sheet to close
          } else {
            closeBottomSheet(); // Close bottom sheet first
            setTimeout(() => {
              setIsAddedToBagLoading(false)
              showSuccess("Success", staticLabels?.Added_tocart_successfully, [
                {
                  text: "OK",
                  onPress: () => {
                    const cartId = response.data.cartLinesAdd.cart.id;
                    // console.log("Cart Id: ", cartId);

                    dispatch(setCartId(cartId));

                    navigation.push("MyCart", {
                      cartId,
                      lines,
                    });
                  },
                },
              ]);
            }, 500); // Wait for bottom sheet to close
          }
        })
        .catch((err) => {
          setIsAddedToBagLoading(false)
          closeBottomSheet(); // Close bottom sheet first
          setTimeout(() => {
            showError("Error", "Something went wrong. Please try again.");
          }, 300); // Wait for bottom sheet to close
          console.error("Cart addition error:", err);
        });
    } else {
      createCart({
        variables: {
          lines,
        },
      })
        .then((response: any) => {
          if (response.data.cartCreate.userErrors.length > 0) {
            setIsAddedToBagLoading(false)
            closeBottomSheet(); // Close bottom sheet first
            setTimeout(() => {
              showError("Error", response.data.cartCreate.userErrors[0].message);
            }, 500); // Wait for bottom sheet to close
          } else {
            setIsAddedToBagLoading(false)
            closeBottomSheet(); // Close bottom sheet first
            setTimeout(() => {
              showSuccess("Success", staticLabels?.Added_tocart_successfully, [
                {
                  text: "OK",
                  onPress: () => {
                    const cartId = response.data.cartCreate.cart.id;
                    // console.log("Cart Id: ", cartId);
                    dispatch(setCartId(cartId));
                    navigation.push("MyCart", {
                      cartId,
                      lines,
                    });
                  },
                },
              ]);
            }, 500); // Wait for bottom sheet to close
            // closeBottomSheet()
          }
        })
        .catch((err) => {
          setIsAddedToBagLoading(false)
          closeBottomSheet(); // Close bottom sheet first
          setTimeout(() => {
            showError("Error", "Something went wrong. Please try again.");
          }, 300); // Wait for bottom sheet to close
          console.error("Cart creation error:", err);
        });
    }
  };

  const handleIncrease = () => {
    setIsAddedToBagLoading(true)
    setTimeout(() => {
      setIsAddedToBagLoading(false)
    }, 300)
    setQuantity((pre: number) => pre + 1);
  };

  const handleDecrease = () => {
    setIsAddedToBagLoading(true)
    setTimeout(() => {
      setIsAddedToBagLoading(false)
    }, 400)
    setQuantity((pre: number) => {
      if (pre <= 1) {
        return 0;
      }
      return pre - 1;
    });
  };

  const handleVariantChange = (variantId: string) => {
    setIsAddedToBagLoading(true)
    setTimeout(() => {
      setIsAddedToBagLoading(false)
    }, 400)
    if (variantId !== selectedVariantId) {
      setSelectedVariantId(variantId);
    }

  };

  const handleSizeChange = (size: string, index: number) => {
    setIsAddedToBagLoading(true)
    setTimeout(() => {
      setIsAddedToBagLoading(false)
    }, 400)
    setSelectedSize(size);
    setActiveIndex(index);
  };

  useEffect(() => {
    if (modalVisible && data.variants && selectedVariantId) {
      const selectedVariant = data.variants.find((v: any) => v.id === selectedVariantId);
      setCurrentProductDetails((prev: any) => {
        if (prev && typeof prev === 'object') {
          return { ...prev, selectedVariant };
        } else {
          return { ...data, selectedVariant };
        }
      });
    }
  }, [selectedVariantId, modalVisible]);


  const innerCardHeight = Dimensions.get("window").height
  const isPreorder = Array.isArray(data?.tags) && data.tags.includes('preorder');
  console.log("isPreorder>>",isPreorder)
  return (
    <>
      <View style={[{}]}>
        {isPreorder &&
          <Badge
            title={isPreorder && "Pre order"}
            color={COLORS.badgeBackgroundColor}
            size="md"
            style={{
              position: "absolute",
              zIndex: 10000,
              borderRadius: 8,
              marginTop: 8,
              marginHorizontal: 10,
            }}
          />}
        <TouchableOpacity
          activeOpacity={0.7}
          style={[styles.cardContainer, { marginHorizontal: CARD_MARGIN / 2 }]}
          onPress={() => {
            navigation.navigate("ProductDetailsPage", {
              handle: data?.handle,
            });
          }}
        >
          <TouchableOpacity
            onPress={() => openBottomSheet(data)}
            style={{
              position: "absolute",
              bottom: 110,
              // top: 148,
              right: 0,
              backgroundColor: COLORS.addToCartBg,
              borderTopLeftRadius: 8,
              zIndex: 100000,
            }}
          >
            <Image
              source={CartIcon}
              style={{
                width: 40,
                height: 40,
              }}
            />
          </TouchableOpacity>
          {data?.image && (
            <Image
              style={{
                width: "100%",
                height: 188,
                objectFit:"fill"
                // aspectRatio: 2 / 2,
                // borderTopLeftRadius: 15,
                // borderTopRightRadius: 15,
              }}
              source={{ uri: data?.image }}
            />
          )}
          {!data.image && (
            <Image
              style={{
                width: "100%",
                height: 188,
                // aspectRatio: 2 / 2,
                // borderTopLeftRadius: 15,
                // borderTopRightRadius: 15,
              }}
              source={{ uri: "https://t3.ftcdn.net/jpg/02/01/90/26/360_F_201902639_gZhcd695qjLUnVtJPXqlWpxNj5TvVHw2.jpg" }}
            />
          )}

          <View
            style={{
              flexDirection: "column",
              justifyContent: "space-between",
              marginVertical: 5,
              marginHorizontal: 8,
              marginTop: 3,
              width: 130,
              height: 100,
            }}
          >
            <Text
              style={{
                ...FONTS.fontRegular,
                fontSize: SIZES.fontXs,
                color: COLORS.textBrandName,
                ...FONTS.fontRegular,
              }}
            >
              {data?.vendor}
            </Text>

            <Text
              style={{
                ...FONTS.fontRegular,
                fontSize: SIZES.font,
                fontFamily: "DMSansSemiBold",
              }}
              numberOfLines={2}
              ellipsizeMode="tail"
            >
              {data?.title}
            </Text>

            <View style={{ flexDirection: "row", gap: 5 }}>
              <Text
                style={{
                  marginVertical: 5,
                  fontSize: SIZES.font,
                  fontFamily: "DMSansSemiBold",
                }}
              >
                ${data?.price}
              </Text>
              {/* <Text
                      style={{
                        marginVertical: 5,
                        color: COLORS.darkgray,
                        fontSize: SIZES.font,
                        ...FONTWEIGHT.Light,
                        textDecorationLine: "line-through",
                        marginHorizontal: 2,
                        fontFamily: "DMSansMedium",
                      }}
                    >
                      $126.00
                    </Text> */}
            </View>
            <View
              style={{
                flexDirection: "row",
                gap: 10,
              }}
            >
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  gap: 4,
                  alignItems: "center",
                }}
              >
                {data.uniqueColors?.slice(0, 3)?.map((varient: any) => {
                  return (
                    <>
                      <View
                        key={varient}
                        style={{
                          width: 15,
                          height: 15,
                          backgroundColor: `${varient}`,
                          borderRadius: 25,
                        }}
                      ></View>
                    </>
                  );
                })}
              </View>
              {data.uniqueColors?.length > 3 && (
                <Text style={{ fontFamily: "DMSansLight" }}>
                  +{data.uniqueColors?.slice(3)?.length}
                </Text>
              )}
            </View>
          </View>
        </TouchableOpacity>
      </View>
      <BottomNavModal
        modalVisible={modalVisible}
        setModalVisible={setModalVisible}
        currentProductDetails={currentProductDetails}
        buttonWidth={190}
        clearAllBtn={true}
        leftIconWidth={140}
        height={Platform.OS === "ios" && keyboardOpen && innerCardHeight * 0.95}
        buttonTitle={quantity}
        onQuantityDecrease={handleDecrease}
        onQuantityIncrease={handleIncrease}
        onPressRightBtn={handleAddToBag}
        rightIcon="+"
        leftIcon="-"
        headerEnabled={false}
        modelTitleTextComponent={
          <Text
            style={{
              marginTop: 5,
              marginBottom: 10,
              marginHorizontal: 10,
              fontSize: SIZES.fontLg,
              color: COLORS.title,
              fontFamily: "DMSansSemiBold",
            }}
          >
            {currentProductDetails && typeof currentProductDetails === 'object' && (currentProductDetails as any).title ? (currentProductDetails as any).title : ''}
          </Text>
        }
        useTextInput={true}
        onTextChange={(text) => {
          const numeric = text.replace(/[^0-9]/g, "");
          let parsedValue = parseInt(numeric, 10);
          if (isNaN(parsedValue) || parsedValue < 1) parsedValue = 0;
          setQuantity(parsedValue);
        }}
        useQuantitySelector={true}
        isButtonBoading={isAddedToBagLoading ? true : false}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{}}
        >
          {data?.uniqueColors?.length > 0 &&
            <View>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginBottom: 16,
                }}
              >
                <Text
                  style={{
                    fontFamily: "DMSansBold",
                    fontSize: SIZES.font,
                    marginHorizontal: 10,
                  }}
                >
                  {staticLabels?.choose_Color}
                </Text>
                <Text
                  style={{ fontWeight: "400" }}
                >{`(${data?.uniqueColors?.length} Colors)`}</Text>
              </View>

              <View
                style={{
                  marginHorizontal: 5,
                }}
              >
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  scrollEventThrottle={16}
                  contentContainerStyle={{gap:10}}
                >
                  {data?.uniqueColors?.map((colorEntry: any, index: number) => (
                    <TouchableOpacity
                      onPress={() => {
                        // Find the variant for this color
                        const selectedVariant = data.variants.find((v: any) => v.id === colorEntry.variantId);
                        const color = selectedVariant?.selectedOptions.find((o: any) => o.name === "Color")?.value;
                        setSelectedColor(color);
                        setSelectedVariantId(colorEntry.variantId);
                        setSelectedSize(null); // Optionally reset size on color change
                        setActiveIndex(0);
                      }}
                      key={colorEntry.variantId}
                      style={{
                        // alignItems: "center",
                        borderRadius: 10,
                        // padding: 6,
                        gap:10
                      }}
                    >
                      <Image
                        source={{ uri: colorEntry.image ?? "https://t3.ftcdn.net/jpg/02/01/90/26/360_F_201902639_gZhcd695qjLUnVtJPXqlWpxNj5TvVHw2.jpg" }}
                        style={{
                          width: 150,
                          borderEndWidth:1.5,
                          borderColor:"#1E60AE",
                          borderWidth: selectedColor === colorEntry.color ? 1 : 0,
                          height: undefined,
                          aspectRatio: 1 / 1,
                          objectFit: "fill",
                          borderRadius: 10,
                        }}
                      />
                      <Text style={{...FONTS.font}}>{colorEntry.color}</Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>
          }
          {data?.sizes?.length > 0 && (
            <View style={{ marginTop: 20, marginHorizontal: 10 }}>
              <View>
                <Text
                  style={{ fontFamily: "DMSansBold", fontSize: SIZES.font }}
                >
                  {staticLabels?.choose_size}
                </Text>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  gap: 8,
                  flexWrap: "wrap",
                  marginTop: 16,
                }}
              >
                {data?.sizes?.map((size: any, index: any) => {
                  // const availableSizes = selectedColor ? (data.colorSizeMap[selectedColor] || []) : [];
                  // const sizeEntry = availableSizes.find((s: any) => s.size === size);
                  // const isDisabled = !sizeEntry || sizeEntry.quantity === 0;
                  return (
                    <TouchableOpacity
                      onPress={() => {
                        // if (isDisabled) return;
                        setSelectedSize(size);
                        setActiveIndex(index);
                        const variant = data.variants.find((v: any) =>
                          v.selectedOptions.some((o: any) => o.name === "Color" && o.value === selectedColor) &&
                          v.selectedOptions.some((o: any) => o.name === "Size" && o.value === size)
                        );
                        if (variant) setSelectedVariantId(variant.id);
                      }}
                      style={{
                        marginTop: 8,
                        borderWidth: 1,
                        borderRadius: 10,
                        paddingVertical: 10,
                        width: 76,
                        height: 40, // Fixed height to help layout
                        overflow: 'hidden', // ✅ Clip child overflow (strike-through)
                        position: 'relative', // Needed for absolute children
                        // opacity: isDisabled ? 0.2 : 1,
                        backgroundColor: activeIndex === index ? COLORS.black : COLORS.white,
                      }}
                    // disabled={isDisabled}
                    >
                      <Text
                        style={{
                          textAlign: "center",
                          color: activeIndex === index ? COLORS.white : COLORS.black,
                          // textDecorationLine: isDisabled ? "line-through" : "none",
                        }}
                      >
                        {size}
                      </Text>

                      {/* {isDisabled && (
                        <View
                          style={{
                            position: "absolute",
                            width: "230%",
                            height: 2,
                            backgroundColor: "rgba(0,0,0,0.5)",
                            top: "0%",
                            left: "-5%",
                            transform: [{ rotate: "-25deg" }],
                          }}
                        />
                      )} */}
                    </TouchableOpacity>

                  );
                })}
              </View>
            </View>
          )}
        </ScrollView>
      </BottomNavModal>

      {/* Custom Alert */}
      {alertConfig && (
        <CustomAlert
          visible={alertVisible}
          title={alertConfig.title}
          message={alertConfig.message}
          type={alertConfig.type}
          buttons={alertConfig.buttons}
          onClose={hideAlert}
        />
      )}
    </>
  );
}

const styles = StyleSheet.create({
  cardContainer: {
    width: CARD_WIDTH,
    backgroundColor: COLORS.white,
    borderRadius: 10,
    // marginBottom: 20,
    overflow: "hidden",
    zIndex: 1,
  },
  productImage: {
    width: "100%",
    aspectRatio: 1,
    resizeMode: "cover",
  },
  badge: {
    position: "absolute",
    top: 8,
    left: 8,
    backgroundColor: COLORS.primary,
    color: COLORS.white,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    fontSize: 12,
    ...FONTWEIGHT.Bold,
  },
  title: {
    fontSize: 14,
    color: COLORS.primary,
    paddingHorizontal: 10,
    paddingTop: 8,
    ...FONTS.fontMedium,
  },
  price: {
    fontSize: 16,
    color: COLORS.black,
    paddingHorizontal: 10,
    paddingVertical: 8,
    ...FONTWEIGHT.Bold,
  },
});