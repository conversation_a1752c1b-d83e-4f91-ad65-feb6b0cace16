import React, { memo, useCallback, useState, useEffect, useRef } from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableWithoutFeedback,
  Image,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Dimensions,
  Platform,
  StyleProp,
  TextStyle,
  KeyboardAvoidingView,
  Keyboard,
  FlatList,
} from "react-native";
import { SafeAreaProvider, SafeAreaView } from "react-native-safe-area-context";
import {
  ButtonLabel,
  COLORS,
  FONTS,
  FONTWEIGHT,
  SIZES,
} from "../../constants/theme";
import Button from "../Button/Button";
import QuantitySelector from "../QuantitySelector/QuantitySelector";
import Header from "../../layout/Header";
import CustomAlert from "../CustomAlert";
import { useCustomAlert } from "../../hooks/useCustomAlert";
import { useMutation } from "@apollo/client";
import { ADD_LINES_TO_CART, CREATE_CART } from "../../api/cartQuery";
import { useNavigation } from "@react-navigation/native";
import { ActivityIndicator } from "react-native-paper";
import AddIcon from "../../assets/icons/addicon.png";
import RemoveIcon from "../../assets/icons/removeIcon.png";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  runOnJS,
  useAnimatedReaction,
} from "react-native-reanimated";
import { useDispatch, useSelector } from "react-redux";
import { setCartId } from "../../redux/reducer/cartReducer";
import { LanguageState } from "@/app/redux/reducer/languageReducer";
import { staticTranslations } from "@/app/constants/staticTranslations";
import { widthPercentageToDP } from "react-native-responsive-screen";

interface ButtonProps {
  modalVisible?: any;
  setModalVisible?: any;
  currentProductDetails?: any;
  buttonWidth?: any;
  children?: any;
  height?: any;
  buttonTitle?: any;
  leftbuttonTitle?: any;
  rightButtonTitle?: any;
  leftIcon?: any;
  rightIcon?: any;
  clearAllBtn?: any;
  headerEnabled?: any;
  removeExtraSpaceFromHeader?: any;
  noButtton?: any;
  planBottomSheet?: any;
  navbarTitle?: any;
  isCloseButtonRequired?: any;
  isBackBtnRequired?: any;
  modelTitle?: any;
  onPressRightBtn?: any;
  onPressLeftBtn?: any;
  leftIconWidth?: any;
  modelTitleTextComponent?: any;
  onQuantityDecrease?: () => void;
  onQuantityIncrease?: () => void;
  useTextInput?: boolean;
  onTextChange?: (text: string) => void;
  useQuantitySelector?: boolean;
  isButtonBoading?: boolean
}
const BottomNavModal: React.FC<ButtonProps> = ({
  modalVisible,
  setModalVisible,
  currentProductDetails,
  buttonWidth,
  children,
  height,
  leftbuttonTitle = false,
  rightButtonTitle = false,
  leftIcon = false,
  rightIcon = false,
  clearAllBtn = false,
  headerEnabled = false,
  removeExtraSpaceFromHeader = false,
  noButtton = false,
  buttonTitle,
  planBottomSheet = false,
  navbarTitle,
  isCloseButtonRequired = true,
  isBackBtnRequired = true,
  modelTitle = "",
  onPressRightBtn = () => { },
  onPressLeftBtn = () => { },
  leftIconWidth = false,
  modelTitleTextComponent = null,
  onQuantityDecrease = () => { },
  onQuantityIncrease = () => { },
  useTextInput = false,
  onTextChange = () => { },
  useQuantitySelector = false,
  isButtonBoading = false
}: any) => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { cartId } = useSelector((state: any) => state.cart);
  const { locale }: LanguageState = useSelector((state: any) => state.language);
  const staticLabels = locale === "en" ? staticTranslations.english : staticTranslations.malay;
  const closeBottomSheet = useCallback(() => {
    setModalVisible(false);
  }, [modalVisible]);
  const [quantities, setQuantities] = useState<{ [variantId: string]: number }>(
    {}
  );
  const [createCart, { loading, error }] = useMutation(CREATE_CART);
  const [addCart, { loading: addLoading }] = useMutation(ADD_LINES_TO_CART);

  // Custom Alert Hook
  const {
    alertConfig,
    visible: alertVisible,
    showSuccess,
    showError,
    hideAlert,
  } = useCustomAlert();
  const modalAnimY = useSharedValue(1000); // Initialize the shared value for Y translation
  const isFullyOpen = useSharedValue(false);

  const [isRendered, setIsRendered] = useState(modalVisible);
  const screenHeight = Dimensions.get("screen").height;
  const [pendingSuccessModal, setPendingSuccessModal] = useState<null | { message: string; onPress: () => void }>(null);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [canRenderChildren, setCanRenderChildren] = useState(false);
  const { width: screenWidth } = Dimensions.get("window");
  const responsiveWidth = screenWidth * 0.226;
  useEffect(() => {
    const onKeyboardShow = (e: { endCoordinates: { height: number } }) => setKeyboardHeight(e.endCoordinates.height);
    const onKeyboardHide = () => setKeyboardHeight(0);

    const showEvent = Platform.OS === "ios" ? "keyboardWillShow" : "keyboardDidShow";
    const hideEvent = Platform.OS === "ios" ? "keyboardWillHide" : "keyboardDidHide";

    const showListener = Keyboard.addListener(showEvent, onKeyboardShow);
    const hideListener = Keyboard.addListener(hideEvent, onKeyboardHide);

    return () => {
      showListener.remove();
      hideListener.remove();
    };
  }, []);
  const isSmallDevice = screenWidth < 360; // or tweak threshold to your design
  useEffect(() => {
    if (modalVisible) {
      setIsRendered(true);
      isFullyOpen.value = false;
      modalAnimY.value = withTiming(0, {
        duration: 500,
        easing: Easing.out(Easing.quad),
      }, (finished) => {
        if (finished) {
          isFullyOpen.value = true;
        }
      });
    } else {
      isFullyOpen.value = false;
      modalAnimY.value = withTiming(1000, {
        duration: 200,
        easing: Easing.in(Easing.ease),
      }, (finished) => {
        if (finished) {
          runOnJS(setIsRendered)(false);
        }
      });
    }
  }, [modalVisible]);
  useAnimatedReaction(
    () => isFullyOpen.value,
    (val) => {
      if (val === true) {
        runOnJS(setCanRenderChildren)(true);
      } else {
        runOnJS(setCanRenderChildren)(false);
      }
    },
    [isFullyOpen]
  );

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: modalAnimY.value }],
    };
  });

  useEffect(() => {
    if (modalVisible) {
      setQuantities({});
    }
  }, [modalVisible]);

  const handleIncrease = (variantId: string) => {
    setQuantities((prev) => {
      const current = prev[variantId] || 1;
      // Remove inventory check and error
      return { ...prev, [variantId]: current + 1 };
    });
  };

  const handleDecrease = (variantId: string) => {
    setQuantities((prev) => ({
      ...prev,
      [variantId]: Math.max(1, (prev[variantId] || 1) - 1),
    }));
  };
  console.log("currentproductdetails>>>", currentProductDetails)
  const handleAddToBag = () => {
    if (loading || addLoading) return;
    if (!currentProductDetails || currentProductDetails.length === 0) {
      closeBottomSheet();
      setPendingSuccessModal({
        message: "Error: No product selected.",
        onPress: () => { },
      });
      return;
    }
    console.log("currentProductDetails>>??", currentProductDetails);
    const cartLines = currentProductDetails
      .filter((variant: any) => quantities[variant.id] && quantities[variant.id] > 0)
      .map((variant: any) => {
        // Extract color and size from selectedOptions or fallback
        let color = variant.color;
        let size = variant.size;
        if (Array.isArray(variant.selectedOptions)) {
          for (const opt of variant.selectedOptions) {
            if (typeof opt.name === 'string' && opt.name.toLowerCase() === 'color') color = opt.value;
            if (typeof opt.name === 'string' && opt.name.toLowerCase() === 'size') size = opt.value;
          }
        }
        // Build attributes array for Shopify cart line
        const attributes = [];
        if (size) attributes.push({ key: 'Size', value: size });
        // If you want to pass color as well, uncomment the next line:
        // if (color) attributes.push({ key: 'Color', value: color });
        return {
          merchandiseId: variant.id,
          quantity: quantities[variant.id],
          attributes,
        };
      });

    // Build analyticsLines for toast: extract color/size from selectedOptions or fallback
    const analyticsLines = currentProductDetails
      .filter((variant: any) => quantities[variant.id] && quantities[variant.id] > 0)
      .map((variant: any) => {
        let color = variant.color;
        let size = variant.size;
        if (Array.isArray(variant.selectedOptions)) {
          for (const opt of variant.selectedOptions) {
            if (typeof opt.name === 'string' && opt.name.toLowerCase() === 'color') color = opt.value;
            if (typeof opt.name === 'string' && opt.name.toLowerCase() === 'size') size = opt.value;
          }
        }
        return { color, size };
      });

    if (cartLines.length === 0) {
      closeBottomSheet();
      setPendingSuccessModal({
        message: "Error: No product selected.",
        onPress: () => { },
      });
      return;
    }
    // console.log("CartLines: ", cartLines);

    if (cartId) {
      addCart({
        variables: {
          cartId,
          lines: cartLines,
        },
      })
        .then((response: any) => {
          if (response.data.cartLinesAdd.userErrors.length > 0) {
            closeBottomSheet();
            setPendingSuccessModal({
              message: response.data.cartLinesAdd.userErrors[0].message,
              onPress: () => { },
            });
          } else {
            closeBottomSheet();
            setPendingSuccessModal({
              message: `${staticLabels?.Added_tocart_successfully}`,
              onPress: () => {
                const cartId = response.data.cartLinesAdd.cart.id;
                dispatch(setCartId(cartId));
                (navigation as any).navigate({
                  name: "MyCart",
                  params: { cartId, cartLines },
                });
              },
            });
          }
        })
        .catch((err) => {
          closeBottomSheet();
          setPendingSuccessModal({
            message: "Something went wrong. Please try again.",
            onPress: () => { },
          });
        });
    } else {
      createCart({
        variables: {
          lines: cartLines,
        },
      })
        .then((response: any) => {
          if (response.data.cartCreate.userErrors.length > 0) {
            closeBottomSheet();
            setPendingSuccessModal({
              message: response.data.cartCreate.userErrors[0].message,
              onPress: () => { },
            });
          } else {
            closeBottomSheet();
            setPendingSuccessModal({
              message: `${staticLabels?.Added_tocart_successfully}`,
              onPress: () => {
                const cartId = response.data.cartCreate.cart.id;
                dispatch(setCartId(cartId));
                (navigation as any).navigate({
                  name: "MyCart",
                  params: { cartId, cartLines },
                });
              },
            });
          }
        })
        .catch((err) => {
          closeBottomSheet();
          setPendingSuccessModal({
            message: "Something went wrong. Please try again.",
            onPress: () => { },
          });
        });
    }
  };

  // Show modal only after bottom sheet is fully closed
  useEffect(() => {
    if (!modalVisible && !isRendered && pendingSuccessModal) {
      const errorPatterns = [/^Error:/i, /something went wrong/i, /failed/i, /cannot /i, /unable to/i, /not selected/i, /already been taken/i, /missing/i, /invalid/i, /not found/i];
      const isError = errorPatterns.some((pattern) => pattern.test(pendingSuccessModal.message));
      const alertFn = isError ? showError : showSuccess;
      alertFn(isError ? "Error" : "Success", pendingSuccessModal.message, [
        {
          text: "OK",
          onPress: pendingSuccessModal.onPress,
        },
      ]);
      setPendingSuccessModal(null);
    }
  }, [modalVisible, isRendered, pendingSuccessModal]);
  return (
    <KeyboardAvoidingView>
      <SafeAreaProvider>
        {isRendered && (
          <Modal
            animationType="none"
            transparent={true}
            visible={true}
            onRequestClose={() => setModalVisible(false)}
          >
            <TouchableWithoutFeedback onPress={() => closeBottomSheet()}>
              <View style={styles.modalOverlay}>
                <TouchableWithoutFeedback onPress={() => { }}>
                  <Animated.View
                    style={[
                      animatedStyle,
                      styles.bottomNavContainer,
                      {
                        maxHeight: height ? height : "80%",
                        height: height,
                        borderTopLeftRadius: noButtton ? 20 : height ? 0 : 20,
                        borderTopRightRadius: noButtton ? 20 : height ? 0 : 20,
                        backgroundColor: COLORS.background,
                        // paddingRight: 5,
                        padding: planBottomSheet ? 0 : 20,
                        // gap: planBottomSheet?10:0,
                        // 👇 Add this line to push content above the keyboard
                        paddingBottom: Platform.OS === "ios" ? keyboardHeight + 10 : 10,
                      },
                    ]}
                  >
                    {headerEnabled === false
                      ? planBottomSheet && (
                        <View
                          style={{
                            marginTop: removeExtraSpaceFromHeader
                              ? Platform.OS === "ios"
                                ? 50
                                : 0
                              : screenHeight / 15,
                            marginHorizontal: 12,
                          }}
                        >
                          <Header
                            title={navbarTitle}
                            leftIcon={isBackBtnRequired ? "back" : null}
                            rightIcon={isCloseButtonRequired && "close"}
                            rightIconhandler={closeBottomSheet}
                            backAction={closeBottomSheet}
                          />
                        </View>
                      )
                      : null}
                    {headerEnabled && !planBottomSheet && (
                      <Header
                        title={navbarTitle || ""}
                        leftIcon="back"
                        rightIcon={isCloseButtonRequired && "close"}
                        rightIconhandler={closeBottomSheet}
                      />
                    )}
                    {modelTitleTextComponent
                      ? modelTitleTextComponent
                      : modelTitle && (
                        <Text
                          style={[{
                            marginTop: noButtton ? 0 : 5,
                            marginBottom: noButtton || navbarTitle ? 0 : 20,
                            marginHorizontal: 10,
                            fontSize: SIZES.fontLg,
                            color: COLORS.title,
                            fontFamily: "DMSansSemiBold",
                          } as StyleProp<TextStyle>]}
                        >
                          {modelTitle}
                        </Text>
                      )}

                    {/* <ScrollView
                      contentContainerStyle={styles.scrollContainer}
                      showsVerticalScrollIndicator={false}
                      keyboardShouldPersistTaps="handled"
                    > */}
                    {canRenderChildren && children ? (
                      children
                    ) : (
                      <>
                        <FlatList
                          data={currentProductDetails}
                          keyExtractor={(item) => item.id}
                          renderItem={({ item: variant }) => (
                            <View key={variant.id} style={styles.navItems}>
                              <View
                                style={{
                                  flexDirection: "row",
                                  justifyContent: "space-between",
                                }}
                              >
                                <View style={{ flexDirection: "row", gap: 10, width: "40%" }}>
                                  <Image
                                    style={styles.productImage}
                                    source={{ uri: variant.image?.url ?? "https://t3.ftcdn.net/jpg/02/01/90/26/360_F_201902639_gZhcd695qjLUnVtJPXqlWpxNj5TvVHw2.jpg" }}
                                  />
                                  <View
                                    style={{ gap: 8, justifyContent: "center", marginHorizontal: 10, width: responsiveWidth }}
                                  >
                                    <Text
                                      style={[{
                                        // ...FONTWEIGHT.Bold,
                                        fontSize: SIZES.font,
                                        ...FONTS.fontSemiBold,
                                        // textAlign: "justify"
                                        // display:"flex",
                                        // flexWrap:"wrap"
                                      } as StyleProp<TextStyle>]}
                                    >
                                      {variant?.title}
                                    </Text>
                                    <Text
                                      style={[{
                                        color: COLORS.textBrandName,
                                        fontSize: SIZES.fontXs,
                                        ...FONTS.fontRegular,
                                      } as StyleProp<TextStyle>]}
                                    >
                                      SKU: {variant.sku || "N/A"}
                                    </Text>
                                    <Text
                                      style={[{
                                        ...FONTWEIGHT.Normal,
                                        fontSize: SIZES.fontXs,
                                        ...FONTS.fontRegular,
                                      } as StyleProp<TextStyle>]}
                                    >
                                      ${variant.price?.amount}
                                    </Text>
                                  </View>
                                </View>
                                {/* <View
                          style={{
                            flexDirection: "row",
                            alignItems: "center",
                            justifyContent: "center",
                            columnGap: 10, // or use marginHorizontal on buttons if not supported
                          }}
                        >
                          <TouchableOpacity
                            onPress={() => handleDecrease(variant.id)}
                            style={styles.quantityButton}
                          >
                            <Image
                              style={{ width: 15, height: 15 }}
                              source={RemoveIcon}
                            />
                          </TouchableOpacity>

                          <TextInput
                            style={{
                              fontSize: SIZES.font,
                              width: 60, // enough to fit 4-digit number
                              // height: 36,
                              textAlign: "center",
                              borderWidth: 1,
                              borderColor: "#ccc",
                              borderRadius: 5,
                              // paddingVertical: 2,
                              // paddingHorizontal: 6,
                            }}
                            value={String(quantities[variant.id] || 1)}
                            keyboardType="numeric"
                            onChangeText={(text) =>
                              handleManualInput(variant.id, text)
                            }
                            maxLength={4}
                          />

                          <TouchableOpacity
                            onPress={() => handleIncrease(variant.id)}
                            style={styles.quantityButton}
                          >
                            <Image
                              style={{ width: 15, height: 15 }}
                              source={AddIcon}
                            />
                          </TouchableOpacity>
                        </View> */}
                                <QuantityInput
                                  variant={variant}
                                  quantities={quantities}
                                  setQuantities={setQuantities}
                                  RemoveIcon={RemoveIcon}
                                  AddIcon={AddIcon}
                                />
                              </View>
                            </View>
                          )}
                          showsVerticalScrollIndicator={false}
                          keyboardShouldPersistTaps="handled"
                          removeClippedSubviews={true}
                          initialNumToRender={8}
                          maxToRenderPerBatch={10}
                          windowSize={10}
                          updateCellsBatchingPeriod={50}
                        />
                        {/* {currentProductDetails?.map((variant: any) => (
                         
                          ))} */}
                      </>
                    )}
                    {/* </ScrollView> */}
                    {children
                      ? isCloseButtonRequired && (
                        <View
                          style={{
                            // marginTop: 10,
                            flexDirection: "row",
                            justifyContent: "space-between",
                            alignItems: "center",
                            gap: 10,
                            paddingVertical: 20,
                            paddingHorizontal: clearAllBtn ? 0 : 5,
                            backgroundColor: COLORS.background,
                          }}
                        >
                          {clearAllBtn && (
                            <View
                              style={{
                                gap: 12,
                                flexDirection: "row",
                                justifyContent: "center",
                                alignItems: "center",
                                width: "100%",
                                // borderWidth:1
                              }}
                            >

                              {useQuantitySelector ? (
                                <View>
                                  <View
                                    style={{
                                      height: 60,
                                      paddingHorizontal: 25,
                                      paddingVertical: 13,
                                      flexDirection: "row",
                                      alignItems: "center",
                                      justifyContent: "center",
                                      borderRadius: 28,
                                      backgroundColor: COLORS.white,
                                      borderWidth: 1,
                                      borderColor: COLORS.darkgray,
                                      width: clearAllBtn
                                        ? leftIconWidth
                                          ? leftIconWidth
                                          : 160
                                        : 150,
                                    }}
                                  >
                                    <View
                                      style={{
                                        justifyContent: "center",
                                        alignItems: "center",
                                        flexDirection: "row",
                                        width: "100%",
                                        gap: 10,
                                      }}
                                    >
                                      <TouchableOpacity
                                        style={{
                                          justifyContent: "center",
                                          alignItems: "center",
                                          minWidth: 30,
                                          minHeight: 30,
                                        }}
                                        onPress={onQuantityDecrease}
                                        activeOpacity={0.7}
                                        hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
                                      >
                                        <Text
                                          style={[{ fontWeight: "500", fontSize: 25, width: 30, textAlign: "center", color: COLORS.title } as StyleProp<TextStyle>]}
                                        >
                                          {leftIcon}
                                        </Text>
                                      </TouchableOpacity>
                                      <TextInput
                                        style={[{ fontSize: 16, width: 40, textAlign: "center", paddingVertical: 2, paddingHorizontal: 4, fontWeight: "500", color: COLORS.black, fontFamily: FONTS.font.fontFamily, backgroundColor: "transparent" } as StyleProp<TextStyle>]}
                                        keyboardType="numeric"
                                        value={String(buttonTitle)}
                                        onChangeText={(text) => {
                                          const numeric = text.replace(/[^0-9]/g, "");
                                          let parsedValue = parseInt(numeric, 10);
                                          console.log("parsed value", parsedValue)
                                          const getMatchedVariant = (productDetails: any) => {
                                            const selectedId = productDetails?.selectedVariant?.id;

                                            const matchedVariant = productDetails?.variants?.find(
                                              (variant: any) => variant.id === selectedId
                                            );

                                            return matchedVariant || null;
                                          };

                                          const matchedVariant = getMatchedVariant(currentProductDetails);
                                          console.log("MATCHED VARIANT FROM variants[]:", matchedVariant);
                                          console.log("currentProductDetails", currentProductDetails)
                                          if (isNaN(parsedValue) || parsedValue < 1) parsedValue = 0;
                                          // Remove inventory restriction: do not clamp to max
                                          onTextChange(String(parsedValue));
                                        }}
                                        onBlur={() => {
                                          if (!buttonTitle) onTextChange("0");
                                        }}
                                        maxLength={6}
                                        returnKeyType="done"
                                      />
                                      <TouchableOpacity
                                        style={{
                                          justifyContent: "center",
                                          alignItems: "center",
                                          minWidth: 30,
                                          minHeight: 30,
                                        }}
                                        onPress={onQuantityIncrease}
                                        activeOpacity={0.7}
                                        hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
                                      >
                                        <Text
                                          style={[{ fontWeight: "500", fontSize: 20, width: 30, textAlign: "center", color: COLORS.title } as StyleProp<TextStyle>]}
                                        >
                                          {rightIcon}
                                        </Text>
                                      </TouchableOpacity>
                                    </View>
                                  </View>
                                </View>
                              ) : (
                                <Button
                                  title={buttonTitle}
                                  onPress={onPressLeftBtn}
                                  btnRounded={true}
                                  color={COLORS.white}
                                  outline={true}
                                  style={{
                                    width: clearAllBtn
                                      ? leftIconWidth
                                        ? leftIconWidth
                                        : isSmallDevice ? 160 : 140
                                      : 150,
                                    height: 60,
                                  }}
                                />
                              )}
                              <Button
                                title={
                                  rightButtonTitle
                                    ? rightButtonTitle
                                    : (Array.isArray(currentProductDetails?.tags) && currentProductDetails.tags.includes('preorder')
                                      ? staticLabels.pre_order
                                      : staticLabels.add_to_bag)
                                }
                                onPress={onPressRightBtn}
                                btnRounded={true}
                                style={{ width: isSmallDevice ? 140 : buttonWidth }}
                                loading={isButtonBoading}
                                disabled={Number(buttonTitle) === 0}
                              />
                            </View>
                          )}
                        </View>
                      )
                      : !planBottomSheet && (
                        <View style={{ marginTop: 10, marginHorizontal: 10 }}>
                          <Button
                            title={staticLabels.add_to_bag}
                            onPress={async () => {
                              await handleAddToBag();
                            }}
                            btnRounded={true}
                            style={{ width: buttonWidth }}
                            loading={loading || addLoading}
                          />
                        </View>
                      )}
                  </Animated.View>
                </TouchableWithoutFeedback>
              </View>
            </TouchableWithoutFeedback>
          </Modal>
        )}

        {/* Custom Alert */}
        {alertConfig && (
          <CustomAlert
            visible={alertVisible}
            title={alertConfig.title}
            message={alertConfig.message}
            type={alertConfig.type}
            buttons={alertConfig.buttons}
            onClose={hideAlert}
          />
        )}
      </SafeAreaProvider>
    </KeyboardAvoidingView>
  );
};

const QuantityInput = ({
  variant,
  quantities,
  setQuantities,
  RemoveIcon,
  AddIcon,
}: any) => {
  const value = String(quantities[variant.id] || 0);
  const [error, setError] = useState('');

  const handleManualInput = (variantId: any, text: any) => {
    const numeric = text.replace(/[^0-9]/g, "");
    let parsedValue = numeric === "" ? 0 : parseInt(numeric, 10);
    if (isNaN(parsedValue) || parsedValue < 1) parsedValue = 0;
    setError('');
    setQuantities((prev: any) => ({
      ...prev,
      [variantId]: parsedValue,
    }));
  };

  return (
    <View>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          backgroundColor: "#f8f8f8",
          borderRadius: 10,
          borderWidth: 1,
          borderColor: error ? COLORS.error || '#ff0000' : COLORS.darkgray,
          overflow: "hidden",
          height: 35,
          width: 140,
        }}
      >
        <TouchableOpacity
          onPress={() => {
            // Decrement, but do not allow below 1
            const newVal = Math.max(0, (quantities[variant.id] || 0) - 1);
            setError('');
            setQuantities((prev: any) => ({ ...prev, [variant.id]: newVal }));
          }}
          style={{
            flex: 1,
            height: "100%",
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "transparent",
          }}
        >
          <Image style={{ width: 18, height: 18 }} source={RemoveIcon} />
        </TouchableOpacity>

        <View
          style={{
            flex: 1,
            height: "100%",
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "transparent",
          }}
        >
          <TextInput
            style={{
              fontSize: 16,
              fontWeight: "600",
              textAlign: "center",
              color: "#333",
              width: "100%",
              height: "100%",
              backgroundColor: COLORS.background,
              borderWidth: 0,
              paddingVertical: 0,
              paddingHorizontal: 0,
            }}
            keyboardType="numeric"
            value={value}
            onChangeText={(text) => handleManualInput(variant.id, text)}
            maxLength={6}
            selectTextOnFocus={false}
            editable={variant.availableForSale}
          />
        </View>

        <TouchableOpacity
          onPress={() => {
            // Increment, always start from at least 1
            const currentVal = quantities[variant.id] || 0;
            const newVal = currentVal + 1;
            setError('');
            setQuantities((prev: any) => ({ ...prev, [variant.id]: newVal }));
          }}
          style={{
            flex: 1,
            height: "100%",
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "transparent",
          }}
        >
          <Image style={{ width: 18, height: 18 }} source={AddIcon} />
        </TouchableOpacity>
      </View>
      {error ? (
        <Text
          style={[{
            color: COLORS.error || '#ff0000',
            fontSize: 12,
            marginTop: 4,
            textAlign: 'center',
          } as StyleProp<TextStyle>]}
        >
          {error}
        </Text>
      ) : null}
    </View>
  );
};
const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  bottomNavContainer: {
    backgroundColor: "white",
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "80%",
  },
  scrollContainer: {
    width: "100%",
  },
  modalText: {
    fontSize: SIZES.fontLg,
    ...FONTWEIGHT.Medium,
    marginBottom: 10,
  },
  navItems: {
    gap: 10,
    marginBottom: 15,
  },
  productImage: {
    width: 70,
    height: 70,
    borderRadius: 10,
  },
  quantityContainer: {
    width: 120,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-evenly",
    alignSelf: "center",
    borderWidth: 0.8,
    borderColor: COLORS.darkgray,
    height: 30,
    borderRadius: 8,
    gap: 10,
    marginHorizontal: 20,
  },
  quantityButton: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 6,
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
    paddingTop: 10,
    alignItems: "center",
    // borderWidth:1
  },
  quantityButton1: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 6,
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
    paddingTop: 10,
    alignItems: "center",
    // borderWidth:1
  },
});

export default memo(BottomNavModal);
