import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Platform,
} from "react-native";
import { COLORS, FONTS, SIZES } from "../../constants/theme";
import CustomAlert from "../CustomAlert";
import { useCustomAlert } from "../../hooks/useCustomAlert";

type Props = {
  quantity: number;
  onQuantityDecrease: () => void;
  onQuantityIncrease: () => void;
  onTextChange?: (text: string) => void;
  useTextInput?: boolean;
  leftIcon?: string;
  rightIcon?: string;
  style?: object;
  outline?: boolean;
  color?: string;
  maxQuantity?: number;
};

const QuantitySelector = ({
  quantity,
  onQuantityDecrease,
  onQuantityIncrease,
  onTextChange,
  useTextInput = false,
  leftIcon = "-",
  rightIcon = "+",
  style,
  outline = true,
  color = COLORS.white,
  maxQuantity,
}: Props) => {
  const [localValue, setLocalValue] = useState(String(quantity));
  const [error, setError] = useState("");
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);
  const {
    alertConfig,
    visible: alertVisible,
    showError,
    hideAlert,
  } = useCustomAlert();

  useEffect(() => {
    setLocalValue(String(quantity));
  }, [quantity]);

  const handleTextChange = (text: string) => {
    const numeric = text.replace(/[^0-9]/g, "");
    console.log("maxQuantity",maxQuantity)
    if (numeric === "") {
      setLocalValue("");
      setError("");
      if (onTextChange) {
        if (debounceTimer.current) clearTimeout(debounceTimer.current);
        debounceTimer.current = setTimeout(() => onTextChange("1"), 500);
      }
      return;
    }

    let parsedValue = parseInt(numeric, 10);

    if (maxQuantity !== undefined && parsedValue > maxQuantity) {
      const errorMessage = `Maximum quantity available is ${maxQuantity}`;
      setError(errorMessage);
      showError("Limit Exceeded", errorMessage);
      parsedValue = maxQuantity;
      
    } else {
      setError("");
    }

    const newValue = String(parsedValue);
    setLocalValue(newValue);

    if (onTextChange) {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
      debounceTimer.current = setTimeout(() => {
        onTextChange(newValue);
      }, 500);
    }
  };

  const handleDecrease = () => {
    setError("");
    onQuantityDecrease();
  };

  const handleIncrease = () => {
    if (maxQuantity !== undefined && quantity >= maxQuantity) {
      const errorMessage = `Maximum quantity available is ${maxQuantity}`;
      setError(errorMessage);
      showError("Limit Exceeded", errorMessage);
      return;
    }
    setError("");
    onQuantityIncrease();
  };

  useEffect(() => {
    return () => {
      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }
    };
  }, []);

  return (
    <View>
      <View
        style={[
          {
            height: 48,
            paddingHorizontal: 25,
            paddingVertical: 13,
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            borderRadius: 28,
            backgroundColor: color,
            borderWidth: outline ? 1 : 0,
            borderColor: error ? COLORS.darkgray : COLORS.darkgray,
          },
          style,
        ]}
      >
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            flexDirection: "row",
            width: "100%",
            gap: 10,
          }}
        >
          <TouchableOpacity
            style={{
              justifyContent: "center",
              alignItems: "center",
              minWidth: 30,
              minHeight: 30,
            }}
            onPress={handleDecrease}
            activeOpacity={0.7}
            hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
          >
            <Text
              style={{
                fontWeight: "500",
                fontSize: 25,
                width: 30,
                textAlign: "center",
                color: outline ? COLORS.title : COLORS.white,
              }}
            >
              {leftIcon}
            </Text>
          </TouchableOpacity>

          {useTextInput ? (
            <TextInput
              style={{
                fontSize: 16,
                width: 40,
                textAlign: "center",
                paddingVertical: 2,
                paddingHorizontal: 4,
                fontWeight: "500",
                color: COLORS.black,
                fontFamily: FONTS.font.fontFamily,
                backgroundColor: "transparent",
              }}
              keyboardType="numeric"
              value={localValue}
              onChangeText={handleTextChange}
              onBlur={() => {
                if (localValue === "") {
                  setLocalValue(String(quantity));
                }
              }}
              maxLength={6}
              // selectTextOnFocus={true}
              returnKeyType="done"
            />
          ) : (
            <Text
              style={{
                fontWeight: "500",
                fontSize: 16,
                color: outline ? COLORS.title : COLORS.white,
                minWidth: 40,
                textAlign: "center",
              }}
            >
              {quantity}
            </Text>
          )}

          <TouchableOpacity
            style={{
              justifyContent: "center",
              alignItems: "center",
              minWidth: 30,
              minHeight: 30,
            }}
            onPress={handleIncrease}
            activeOpacity={0.7}
            hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
          >
            <Text
              style={{
                fontWeight: "500",
                fontSize: 20,
                width: 30,
                textAlign: "center",
                color: outline ? COLORS.title : COLORS.white,
              }}
            >
              {rightIcon}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
      {alertConfig && (
        <CustomAlert
          visible={alertVisible}
          title={alertConfig.title}
          message={alertConfig.message}
          type={alertConfig.type}
          buttons={alertConfig.buttons}
          onClose={hideAlert}
        />
      )}
    </View>
  );
};

export default QuantitySelector;
