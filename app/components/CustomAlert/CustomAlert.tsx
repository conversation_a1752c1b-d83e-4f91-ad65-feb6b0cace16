import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Animated,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { Feather } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

interface AlertButton {
  text: string;
  onPress?: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

interface CustomAlertProps {
  visible: boolean;
  title: string;
  message: string;
  type?: 'success' | 'error' | 'info' | 'warning';
  buttons?: AlertButton[];
  onClose?: () => void;
}

const CustomAlert = ({
  visible,
  title,
  message,
  type = 'info',
  buttons = [{ text: 'OK' }],
  onClose,
}: CustomAlertProps) => {
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const getIconConfig = () => {
    switch (type) {
      case 'success':
        return { name: 'check-circle', color: COLORS.success };
      case 'error':
        return { name: 'x-circle', color: COLORS.danger };
      case 'warning':
        return { name: 'alert-triangle', color: COLORS.warning };
      default:
        return { name: 'info', color: COLORS.info };
    }
  };

  const getButtonStyle = (buttonStyle: string) => {
    switch (buttonStyle) {
      case 'cancel':
        return {
          backgroundColor: 'rgba(255, 255, 255, 0.2)',
          borderColor: 'rgba(255, 255, 255, 0.3)',
          borderWidth: 1,
        };
      case 'destructive':
        return {
          backgroundColor: COLORS.danger,
        };
      default:
        return {
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
        };
    }
  };

  const getButtonTextColor = (buttonStyle: string) => {
    switch (buttonStyle) {
      case 'cancel':
        return COLORS.white;
      case 'destructive':
        return COLORS.white;
      default:
        return COLORS.primary;
    }
  };

  const handleButtonPress = (button: any) => {
    if (button.onPress) {
      button.onPress();
    }
    if (onClose) {
      onClose();
    }
  };

  const iconConfig = getIconConfig();

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={() => {}} // Disable hardware back button closing
      statusBarTranslucent={true}
      presentationStyle="overFullScreen"
    >
      <TouchableWithoutFeedback onPress={() => {}}>
        <Animated.View style={[styles.overlay, { opacity: opacityAnim }]}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <Animated.View
              style={[
                styles.alertContainer,
                {
                  transform: [{ scale: scaleAnim }],
                },
              ]}
            >
              {/* Background with blur effect */}
              <View style={styles.blurBackground}>
                <View style={styles.gradientBackground} />
              </View>

              {/* Content */}
              <View style={styles.content}>
                {/* Icon */}
                <View style={styles.iconContainer}>
                  <Feather
                    name={iconConfig.name as any}
                    size={48}
                    color={iconConfig.color}
                  />
                </View>

                {/* Title */}
                <Text style={styles.title}>{title}</Text>

                {/* Message */}
                <Text style={styles.message}>{message}</Text>

                {/* Buttons */}
                <View style={styles.buttonContainer}>
                  {buttons.map((button, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.button,
                        getButtonStyle(button.style || 'default'),
                        buttons.length === 1 && styles.singleButton,
                        buttons.length > 1 && index === 0 && styles.leftButton,
                        buttons.length > 1 && index === buttons.length - 1 && styles.rightButton,
                      ]}
                      onPress={() => handleButtonPress(button)}
                      activeOpacity={0.8}
                    >
                      <Text
                        style={[
                          styles.buttonText,
                          { color: getButtonTextColor(button.style || 'default') },
                        ]}
                      >
                        {button.text}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999999, // Very high z-index to appear above bottom sheets
    elevation: 999999, // For Android
  },
  alertContainer: {
    width: width * 0.85,
    maxWidth: 340,
    borderRadius: SIZES.radius_lg,
    overflow: 'hidden',
    elevation: 1000000, // Very high elevation for Android
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 5,
    },
    shadowOpacity: 0.34,
    shadowRadius: 6.27,
    zIndex: 1000000, // Very high z-index
  },
  blurBackground: {
    ...StyleSheet.absoluteFillObject,
  },
  gradientBackground: {
    flex: 1,
    backgroundColor: 'rgba(41, 40, 40, 0.95)', // Blue background with transparency
  },
  content: {
    padding: 24,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 16,
  },
  title: {
    ...FONTS.h5,
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: 'JostSemiBold',
  },
  message: {
    ...FONTS.font,
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
    opacity: 0.9,
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: SIZES.radius,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 44,
  },
  singleButton: {
    marginHorizontal: 0,
  },
  leftButton: {
    marginRight: 8,
  },
  rightButton: {
    marginLeft: 8,
  },
  buttonText: {
    ...FONTS.fontLg,
    fontFamily: 'JostSemiBold',
  },
});

export default CustomAlert;
