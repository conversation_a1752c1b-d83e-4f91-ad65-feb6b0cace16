import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  Image,
  StyleSheet,
  Platform,
} from 'react-native';
import { useDispatch } from 'react-redux';
import { setLanguage } from '@/app/redux/reducer/languageReducer';
import { FONTS } from '@/app/constants/theme';

const LanguagePicker = ({ locale, languageWithCode }: any) => {
  const dispatch = useDispatch();
  const [modalVisible, setModalVisible] = useState(false);

  const handleSelectLanguage = (value: any) => {
    dispatch(setLanguage(value));
    setModalVisible(false);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={() => setModalVisible(true)}
        style={styles.globeWrapper}
        activeOpacity={0.7}
      >
        <Image
          source={{ uri: 'https://static.thenounproject.com/png/1449000-200.png' }}
          style={styles.globeIcon}
        />
        <Text style={styles.languageText}>{languageWithCode[locale]}</Text>
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalBackdrop}
          activeOpacity={1}
          onPressOut={() => setModalVisible(false)}
        >
          <View style={styles.dropdown}>
            <Text style={styles.dropdownTitle}>Select Language</Text>
            {Object.keys(languageWithCode).map((key) => (
              <TouchableOpacity
                key={key}
                style={[
                  styles.option,
                  locale === key && styles.selectedOption,
                ]}
                onPress={() => handleSelectLanguage(key)}
              >
                <Text
                  style={[
                    styles.optionText,
                    locale === key && styles.selectedText,
                  ]}
                >
                  {languageWithCode[key]}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '22%',
    borderRadius: 10,
  },
  globeWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  globeIcon: {
    width: 30,
    height: 30,
    marginRight: 2,
  },
  languageText: {
    fontWeight: '500',
    ...FONTS.fontMedium,
    fontSize: 14,
  },
  modalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dropdown: {
    width: 280,
    backgroundColor: '#FFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 10,
  },
  dropdownTitle: {
    fontWeight: '600',
    marginBottom: 12,
    ...FONTS.fontMedium,
    fontSize: 15,
  },
  option: {
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginBottom: 6,
  },
  selectedOption: {
    backgroundColor: '#E0E0E0',
  },
  optionText: {
    ...FONTS.fontMedium,
    fontSize: 14,
    color: 'black',
  },
  selectedText: {
    ...FONTS.fontMedium,
    color: 'black',
    fontWeight: '600',
  },
});

export default LanguagePicker;