import React, { useRef, useState, useEffect } from "react";
import { WebView, WebViewNavigation } from "react-native-webview";
import { View, Text } from "react-native";
import {
  useNavigation,
  NavigationProp,
  CommonActions,
} from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useShopifyAuth } from "../api/shopifyAuth";
import { Buffer } from "buffer";
import { ActivityIndicator } from "react-native-paper";
import { COLORS, FONTS } from "../constants/theme";

interface ShopifyWebViewProps {
  onLoginSuccess: (userData: any) => void;
}

const SHOPIFY_STORE_URL = "https://sunrise-trade.myshopify.com";
const SHOPIFY_SHOP_ID = "***********";
const CUSTOMER_ACCOUNT_API_URL = `https://shopify.com/${SHOPIFY_SHOP_ID}/account/customer/api/2024-01/graphql`;
const CLIENT_ID = "shp_14fe4f79-cc74-42bb-8f31-e072f2ff10d4";
const CLIENT_SECRET = "shpss_14fe4f79-cc74-42bb-8f31-e072f2ff10d4";
const REDIRECT_URI = `shop.${SHOPIFY_SHOP_ID}.app://auth/callback`;

// JWT token validation and checking functions
const decodeJWT = (token: string) => {
  try {
    const base64Url = token.split(".")[1];
    const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
    const jsonPayload = decodeURIComponent(
      Buffer.from(base64, "base64")
        .toString()
        .split("")
        .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
        .join("")
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error("❌ Error decoding JWT:", error);
    return null;
  }
};

const isTokenExpired = (token: string): boolean => {
  const decoded = decodeJWT(token);
  if (!decoded) return true;

  const currentTime = Math.floor(Date.now() / 1000);
  return decoded.exp < currentTime;
};

const hasRequiredPermissions = (token: string): boolean => {
  const decoded = decodeJWT(token);
  if (!decoded) return false;

  // The token has 'openid email customer-account-api:full' scope
  const requiredScopes = [
    "openid",
    "email",
    "customer-account-api:full", // This is the actual scope from the token
  ];

  const tokenScopes = decoded.scope.split(" ");
  console.log("🔑 Token scopes:", tokenScopes);
  console.log("🔑 Required scopes:", requiredScopes);

  return requiredScopes.every((scope) => tokenScopes.includes(scope));
};

// Add these types for better error handling
type ApiError = {
  message: string;
  code?: string;
  status?: number;
};

type TokenValidationResult = {
  isValid: boolean;
  reason?: string;
  isExpired?: boolean;
  missingScopes?: string[];
};

// Token formatting function
const formatToken = (token: string): string => {
  // Remove any existing prefix
  const cleanToken = token.replace(/^shcat_/, "");
  // Add the prefix
  return `shcat_${cleanToken}`;
};

// Function to create Authorization header
const createAuthHeader = () => {
  const credentials = Buffer.from(`${CLIENT_ID}:${CLIENT_SECRET}`).toString(
    "base64"
  );
  return `Basic ${credentials}`;
};

// PKCE functions
const generateCodeVerifier = async () => {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  const verifier = String.fromCharCode.apply(null, Array.from(array));
  return btoa(verifier)
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=/g, "");
};

const generateCodeChallenge = async (verifier: string) => {
  const encoder = new TextEncoder();
  const data = encoder.encode(verifier);
  const digest = await crypto.subtle.digest("SHA-256", data);
  const hash = String.fromCharCode(...new Uint8Array(digest));
  return btoa(hash).replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
};

// OAuth functions
const initiateLogin = async () => {
  try {
    // Generate PKCE values
    const codeVerifier = await generateCodeVerifier();
    const codeChallenge = await generateCodeChallenge(codeVerifier);

    // Store code verifier for later use
    await AsyncStorage.setItem("code_verifier", codeVerifier);

    // Generate state for CSRF protection
    const state = await generateCodeVerifier();
    await AsyncStorage.setItem("oauth_state", state);

    // Generate nonce for replay protection
    const nonce = await generateCodeVerifier();
    await AsyncStorage.setItem("oauth_nonce", nonce);

    // Construct authorization URL
    const authUrl = new URL(
      `https://shopify.com/authentication/${SHOPIFY_SHOP_ID}/oauth/authorize`
    );
    authUrl.searchParams.append(
      "scope",
      "openid email customer-account-api:full"
    );
    authUrl.searchParams.append("client_id", CLIENT_ID);
    authUrl.searchParams.append("response_type", "code");
    authUrl.searchParams.append("redirect_uri", REDIRECT_URI);
    authUrl.searchParams.append("state", state);
    authUrl.searchParams.append("nonce", nonce);
    authUrl.searchParams.append("code_challenge", codeChallenge);
    authUrl.searchParams.append("code_challenge_method", "S256");

    console.log("🔑 Auth URL:", authUrl.toString());
    return authUrl.toString();
  } catch (error) {
    console.error("❌ Login initiation error:", error);
    throw error;
  }
};

const exchangeCodeForToken = async (code: string) => {
  try {
    const codeVerifier = await AsyncStorage.getItem("code_verifier");
    if (!codeVerifier) {
      throw new Error("No code verifier found");
    }

    const body = new URLSearchParams();
    body.append("grant_type", "authorization_code");
    body.append("client_id", CLIENT_ID);
    body.append("redirect_uri", REDIRECT_URI);
    body.append("code", code);
    body.append("code_verifier", codeVerifier);

    const response = await fetch(
      `https://shopify.com/authentication/${SHOPIFY_SHOP_ID}/oauth/token`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body,
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Token exchange failed: ${errorText}`);
    }

    const data = await response.json();
    return {
      access_token: data.access_token,
      refresh_token: data.refresh_token,
      id_token: data.id_token,
      expires_in: data.expires_in,
    };
  } catch (error) {
    console.error("❌ Token exchange error:", error);
    throw error;
  }
};

const refreshAccessToken = async (refreshToken: string) => {
  try {
    const body = new URLSearchParams();
    body.append("grant_type", "refresh_token");
    body.append("client_id", CLIENT_ID);
    body.append("refresh_token", refreshToken);

    const response = await fetch(
      `https://shopify.com/authentication/${SHOPIFY_SHOP_ID}/oauth/token`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body,
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Token refresh failed: ${errorText}`);
    }

    const data = await response.json();
    return {
      access_token: data.access_token,
      refresh_token: data.refresh_token,
      expires_in: data.expires_in,
    };
  } catch (error) {
    console.error("❌ Token refresh error:", error);
    throw error;
  }
};

// Enhanced fetchCustomerData function
const fetchCustomerData = async (accessToken: string) => {
  try {
    console.log("🔑 Raw access token:", accessToken);

    // Format token with shcat_ prefix
    const formattedToken = formatToken(accessToken);
    console.log("🔑 Formatted token:", formattedToken);

    // Create request body
    const requestBody = {
      query: `
        {
          customer {
            emailAddress {
              emailAddress
            }
            id
            firstName
          }
        }
      `,
      variables: {},
    };

    // Create headers with formatted token
    const headers = {
      "Content-Type": "application/json",
      Authorization: formattedToken,
      Origin: SHOPIFY_STORE_URL,
      "User-Agent":
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
    };

    // Log request details
    console.log("🔗 API URL:", CUSTOMER_ACCOUNT_API_URL);
    console.log("📤 Request Headers:", headers);

    const response = await fetch(CUSTOMER_ACCOUNT_API_URL, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(requestBody),
    });

    // Log response details
    console.log("📥 Response Status:", response.status);
    console.log("📥 Response Headers:", response.headers);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ API Error Response:", errorText);
      throw new Error(
        `API request failed with status ${response.status}: ${errorText}`
      );
    }

    const data = await response.json();

    if (data.errors) {
      console.error("❌ GraphQL Errors:", JSON.stringify(data.errors, null, 2));
      throw new Error(
        data.errors[0].message || "Failed to fetch customer data"
      );
    }

    if (!data.data?.customer) {
      console.error(
        "❌ No customer data found. Response:",
        JSON.stringify(data, null, 2)
      );
      throw new Error("No customer data found");
    }

    // Extract and format the customer ID
    const customerId = data.data.customer.id;
    const formattedCustomerId = customerId.split("/").pop(); // This will get the last part of the ID
    console.log("👤 Customer ID:", formattedCustomerId);

    // Add the formatted ID to the response
    data.data.customer.formattedId = formattedCustomerId;

    console.log("📦 Customer Data:", JSON.stringify(data, null, 2));
    return data;
  } catch (error) {
    console.error("❌ Error fetching customer data:", error);
    throw error;
  }
};

// Update the navigation type
type RootStackParamList = {
  CompanyRegistration: undefined;
  // Add other screen names as needed
};

function ShopifyWebView({ onLoginSuccess }: ShopifyWebViewProps) {
  const webViewRef = useRef<WebView>(null);
  const [loading, setLoading] = useState(false); // Start with loading false to show login page
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const hasLoggedInRef = useRef(false);
  const isNavigatingRef = useRef(false);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const hasDispatchedNavigationRef = useRef(false);

  const { } = useShopifyAuth(); // Using the hook but not extracting any methods
  const uri = "https://sunrise-trade.myshopify.com/account/login";
  // const SHOPIFY_ADMIN_TOKEN = "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";
  // const SHOPIFY_ADMIN_URL =
  //   "https://sunrise-trade.myshopify.com/admin/api/2025-01/graphql.json";
  const hasNavigatedRef = useRef(false);

  useEffect(() => {
    // Use a ref to track if navigation has already been triggered

    const bootstrap = async () => {
      // If we've already navigated, don't do anything
      if (hasNavigatedRef.current) {
        return;
      }

      const token = await AsyncStorage.getItem("customerToken");
      const customerData = await AsyncStorage.getItem("customerData");

      if (token && customerData) {
        try {
          const parsedCustomer = JSON.parse(customerData);
          if (parsedCustomer?.data?.customer) {
            console.log("🔑 Found existing login, processing auto-login");

            // Show loading indicator for auto-login
            setLoading(true);

            // Mark that we're already logged in
            hasLoggedInRef.current = true;
            setIsLoggedIn(true);

            // Check verification status
            const isVerified = await checkVerificationStatus();
            console.log(
              "🔁 Auto-login: user already logged in. Verified?",
              isVerified
            );

            // Call onLoginSuccess callback
            onLoginSuccess({
              token,
              customerData: parsedCustomer?.data?.customer,
            });

            // Trigger login success notification for auto-login
            // await NotificationService.onLoginSuccess({
            //   accessToken: token,
            //   customerData: parsedCustomer?.data?.customer,
            // });

            // Mark that we've already triggered navigation
            hasNavigatedRef.current = true;
            await AsyncStorage.setItem("hasNavigated", "true");

            // Navigate to the appropriate screen
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [
                  {
                    name: isVerified
                      ? "DrawerNavigation"
                      : "CompanyRegistration",
                    ...(isVerified ? { params: { screen: "Home" } } : {}),
                  },
                ],
              })
            );

            // Don't set loading to false here - the component will unmount anyway
          }
        } catch (e) {
          console.error("❌ Error during auto-login bootstrap:", e);
          setLoading(false);
        }
      } else {
        console.log("👁️ No existing login found, showing login page");
        // No token, ensure WebView is visible
        setLoading(false);
      }
    };

    bootstrap();

    // Cleanup function
    return () => {
      // Clear any timeouts when component unmounts
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, []);

  const checkVerificationStatus = async () => {
    try {
      const storedCustomerData = await AsyncStorage.getItem("customerData");
      const customerData = JSON.parse(storedCustomerData || "{}");
      const customerId = customerData?.data?.customer?.id;

      if (!customerId) {
        console.warn("Customer ID not found in AsyncStorage");
        return;
      }

      const query = `
            query {
              customer(id: "${customerId}") {
                metafield(namespace: "custom", key: "verified") {
                  value
                }
              }
            }
          `;

      const response = await fetch(`${process.env.EXPO_PUBLIC_ADMIN_API_URL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": `${process.env.EXPO_PUBLIC_ADMIN_ACCESS_TOKEN}`,
        },
        body: JSON.stringify({ query }),
      });

      const result = await response.json();
      const verifiedStatus = result?.data?.customer?.metafield?.value;

      console.log("Verification status:", verifiedStatus);
      const isVerified = verifiedStatus === "true";
      console.log("isVerified:", isVerified);

      // Store verification status in AsyncStorage
      if (
        verifiedStatus === undefined ||
        verifiedStatus === null ||
        verifiedStatus === "" 
      ) {
        await AsyncStorage.setItem("isCompanyVerified", "false");
      }
      else {
        await AsyncStorage.setItem("isCompanyVerified", "true");
      }

      // Return the verification status instead of navigating directly
      return isVerified;
    } catch (error) {
      console.error("Verification check error:", error);
    }
  };

  const handleNavigationStateChange = async (navState: WebViewNavigation) => {
    // If we're already logged in, don't process navigation changes
    if (hasLoggedInRef.current) {
      console.log(
        "🛑 Already logged in, ignoring navigation to:",
        navState.url
      );
      return false;
    }

    console.log("🌐 Navigated to:", navState.url);

    // Check for OTP form submission and subsequent steps - this happens after email is entered
    // Only set loading and navigating flags when OTP is actually submitted or we're in post-OTP flow
    if (
      (navState.navigationType === "formsubmit" &&
        navState.url.includes("/authentication/") &&
        navState.url.includes("/verify")) ||
      (navState.url.includes("/authentication/") &&
        navState.url.includes("/verify") &&
        navState.loading) ||
      (navState.url.includes("/authentication/") &&
        navState.url.includes("/verify") &&
        isNavigatingRef.current) ||
      (navState.url.includes("/authentication/") &&
        navState.url.includes("/oauth/authorize")) || // Redirect step after OTP
      (navState.url.includes("/account/callback") &&
        navState.url.includes("code=")) // Final OAuth redirect
    ) {
      console.log(
        "🔑 OTP submitted/loading, showing loader immediately..."
      );

      // Set a flag to indicate we're expecting a profile redirect
      isNavigatingRef.current = true;

      // Set loading to true to show the loader and hide the WebView
      setLoading(true);

      // OPTIONAL: hide WebView immediately
      webViewRef.current?.injectJavaScript(
        `document.body.style.display = 'none'; true;`
      );
    }

    // For login and authentication pages, make sure they're visible
    else if (
      (navState.url.includes("/login") ||
        navState.url.includes("/authentication")) &&
      !navState.url.includes("/verify") && // not OTP
      !navState.url.includes("/oauth/authorize") && // not after OTP
      !navState.url.includes("/account/callback") // not final redirect
    ) {
      console.log("👁️ Showing login/auth page:", navState.url);
      setLoading(false);
      isNavigatingRef.current = false;
    }

    // Check if the URL is the profile page or contains account - hide it but allow it to load in background
    // But exclude login and authentication pages
    if (
      (navState.url.includes("/account/profile") ||
        navState.url.includes("/account/orders") ||
        navState.url.includes("/account?") ||
        navState.url.includes("/account/") ||
        (navState.url.includes("/account") &&
          navState.loading &&
          isNavigatingRef.current)) &&
      !navState.url.includes("/login") &&
      !navState.url.includes("/authentication")
    ) {
      // If we're already handling login, don't process this again
      if (hasLoggedInRef.current) {
        console.log("🛑 Already logged in, blocking profile page load");
        return false;
      }

      console.log(
        "🔒 Hiding profile page but allowing it to load in background:",
        navState.url
      );

      // Immediately show loading and hide WebView from user
      setLoading(true);
      // Set navigating flag to true to maintain loading state
      isNavigatingRef.current = true;

      // Don't stop the page from loading - we need it to load to get the access token
      // But keep it hidden from the user with the loading overlay

      // Immediately inject JavaScript to get the necessary data
      webViewRef.current?.injectJavaScript(`
        (function() {
          function checkLoginData() {
            try {
              const rawCookies = document.cookie.split(';');
              const cookies = rawCookies.reduce((acc, cookie) => {
                const [name, ...rest] = cookie.trim().split('=');
                acc[name] = decodeURIComponent(rest.join('='));
                return acc;
              }, {});
              let accessToken = null;
              let accountNumber = null;
              // Check localStorage for customer account token
              for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key?.includes('__CUSTOMER_ACCOUNT_EXCHANGE_TOKEN__')) {
                  const item = localStorage.getItem(key);
                  try {
                    const parsed = JSON.parse(item);
                    if (parsed?.accessToken) {
                      accessToken = parsed.accessToken;
                    }
                  } catch (_) {}
                }
              }

              // Check for customer account data
              const customerAccountData = localStorage.getItem('customer_account_data');
              let customerData = null;
              if (customerAccountData) {
                try {
                  customerData = JSON.parse(customerAccountData);
                } catch (_) {}
              }
              const isLoggedIn = !!(
                accessToken ||
                cookies['logged_in'] === 'true' ||
                cookies['__session'] ||
                cookies['_shopify_s'] ||
                customerData?.customerId
              );

              // If we have the necessary data, send it immediately
              if (isLoggedIn && (accessToken || customerData)) {
                console.log("🔑 Found login data, sending to app...");
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  isLoggedIn,
                  url: window.location.href,
                  session: cookies['__session'] || null,
                  shopifyEssential: cookies['_shopify_essential'] || null,
                  shopifyS: cookies['_shopify_s'] || null,
                  shopifyY: cookies['_shopify_y'] || cookies['_y'] || null,
                  masterDeviceId: cookies['master_device_id'] || null,
                  cfClearance: cookies['cf_clearance'] || null,
                  accessToken,
                  accountNumber,
                  customerData
                }));
              } else {
                // If data is not ready, retry immediately but with a limit
                console.log("🔄 Login data not found, retrying...");
                setTimeout(checkLoginData, 100);
              }
            } catch (error) {
              console.error("❌ Error extracting login data:", error);
              window.ReactNativeWebView.postMessage(JSON.stringify({
                error: 'JS extraction failed',
                message: error.message
              }));
            }
          }

          // Start checking for login data immediately
          checkLoginData();
        })();
        true;
      `);
    }

    if (navState.url.startsWith("sunrise://")) {
      webViewRef.current?.stopLoading();
      setLoading(true); // Show loading indicator to hide the WebView content

      try {
        const url = new URL(navState.url);
        const code = url.searchParams.get("code");
        const state = url.searchParams.get("state");

        // Verify state to prevent CSRF
        const storedState = await AsyncStorage.getItem("oauth_state");
        if (state !== storedState) {
          throw new Error("Invalid state parameter");
        }

        if (code) {
          const tokenData = await exchangeCodeForToken(code);
          await AsyncStorage.setItem("access_token", tokenData.access_token);
          await AsyncStorage.setItem("refresh_token", tokenData.refresh_token);
          await AsyncStorage.setItem("isLoggedIn", "true");

          setIsLoggedIn(true);

          // Store additional token data
          if (tokenData.id_token) {
            await AsyncStorage.setItem("id_token", tokenData.id_token);
          }

          if (tokenData.expires_in) {
            const expiresAt = Date.now() + tokenData.expires_in * 1000;
            await AsyncStorage.setItem(
              "token_expires_at",
              expiresAt.toString()
            );
          }

          // Fetch customer data
          try {
            const customerData = await fetchCustomerData(
              tokenData.access_token
            );
            await AsyncStorage.setItem(
              "customerData",
              JSON.stringify(customerData)
            );

            if (customerData?.data?.customer) {
              const email =
                customerData.data.customer.emailAddress?.emailAddress;
              const accountNumber = customerData.data.customer.formattedId;

              if (email) {
                await AsyncStorage.setItem("customerEmail", email);
              }
              if (accountNumber) {
                await AsyncStorage.setItem("accountNumber", accountNumber);
              }
            }

            // Call onLoginSuccess with the token first
            onLoginSuccess({
              token: tokenData.access_token,
              customerData: customerData?.data?.customer,
            });

            // Trigger login success notification
            // await NotificationService.onLoginSuccess({
            //   accessToken: tokenData.access_token,
            //   customerData: customerData?.data?.customer,
            // });

            // Immediately check verification status and redirect
            try {
              const isVerified = await checkVerificationStatus();
              console.log("OAuth user verification status:", isVerified);

              // Forcefully navigate based on verification status
              if (isVerified) {
                console.log("OAuth user is verified, redirecting to Home");
                navigation.dispatch(
                  CommonActions.reset({
                    index: 0,
                    routes: [
                      { name: "DrawerNavigation", params: { screen: "Home" } },
                    ],
                  })
                );
              } else {
                console.log(
                  "OAuth user is not verified, redirecting to CompanyRegistration"
                );
                navigation.dispatch(
                  CommonActions.reset({
                    index: 0,
                    routes: [{ name: "CompanyRegistration" }],
                  })
                );
              }
            } catch (error) {
              console.error("Error checking OAuth verification status:", error);
              // Default to CompanyRegistration if verification check fails
              console.log(
                "Error in OAuth verification check, defaulting to CompanyRegistration"
              );
              navigation.dispatch(
                CommonActions.reset({
                  index: 0,
                  routes: [{ name: "CompanyRegistration" }],
                })
              );
            }
          } catch (error) {
            console.error("Failed to fetch customer data after OAuth:", error);
            // If we can't fetch customer data, just go to CompanyRegistration
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [{ name: "CompanyRegistration" }],
              })
            );
          }
        }
      } catch (error) {
        console.error("Deep link handling error:", error);
      }

      return false;
    }

    // Handle regular navigation
    if (
      navState.url.includes("/account") ||
      navState.url.includes("/company")
    ) {
      webViewRef.current?.injectJavaScript(`
        (function() {
          try {
            const rawCookies = document.cookie.split(';');
            const cookies = rawCookies.reduce((acc, cookie) => {
              const [name, ...rest] = cookie.trim().split('=');
              acc[name] = decodeURIComponent(rest.join('='));
              return acc;
            }, {});
            let accessToken = null;
            let accountNumber = null;
            // Check localStorage for customer account token
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key?.includes('__CUSTOMER_ACCOUNT_EXCHANGE_TOKEN__')) {
                const item = localStorage.getItem(key);
                try {
                  const parsed = JSON.parse(item);
                  if (parsed?.accessToken) {
                    accessToken = parsed.accessToken;
                  }
                } catch (_) {}
              }
            }

            // Check for customer account data
            const customerAccountData = localStorage.getItem('customer_account_data');
            let customerData = null;
            if (customerAccountData) {
              try {
                customerData = JSON.parse(customerAccountData);
              } catch (_) {}
            }
            const isLoggedIn = !!(
              accessToken ||
              cookies['logged_in'] === 'true' ||
              cookies['__session'] ||
              cookies['_shopify_s'] ||
              customerData?.customerId
            );
            window.ReactNativeWebView.postMessage(JSON.stringify({
              isLoggedIn,
              url: window.location.href,
              session: cookies['__session'] || null,
              shopifyEssential: cookies['_shopify_essential'] || null,
              shopifyS: cookies['_shopify_s'] || null,
              shopifyY: cookies['_shopify_y'] || cookies['_y'] || null,
              masterDeviceId: cookies['master_device_id'] || null,
              cfClearance: cookies['cf_clearance'] || null,
              accessToken,
              accountNumber,
              customerData
            }));
          } catch (error) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              error: 'JS extraction failed',
              message: error.message
            }));
          }
        })();
        true;
      `);
    }
    return true; // Allow navigation to proceed
  };

  const handleMessage = async (event: any) => {
    // Use a ref to track if navigation has already been triggered from this handler
    try {
      // If we're already logged in (but not just navigating), ignore this message
      if (hasLoggedInRef.current) {
        console.log("🛑 Already logged in, ignoring message");
        return;
      }

      const data = JSON.parse(event.nativeEvent.data);
      console.log("📩 Cookie Data:", data);

      // Check if we're on the profile page and have the necessary data
      if (data.isLoggedIn && (data.accessToken || data.customerData)) {
        // Mark that we're handling navigation
        isNavigatingRef.current = true;

        // Keep loading state true during the process
        setLoading(true);

        const userData = {
          session: data.session,
          shopifyEssential: data.shopifyEssential,
          shopifyS: data.shopifyS,
          shopifyY: data.shopifyY,
          url: data.url,
          accessToken: data.accessToken,
          customerData: data.customerData,
        };

        // Store all relevant data
        if (data.accessToken) {
          console.log("🔑 Raw access token:", data.accessToken);

          // Store the token exactly as received
          await AsyncStorage.setItem("customerToken", data.accessToken);
          await AsyncStorage.setItem("isLoggedIn", "true");

          const isLoggedIn = await AsyncStorage.getItem("isLoggedIn");
          const customerToken = await AsyncStorage.getItem("customerToken");
          if (isLoggedIn === "true") {
            // Login success notification will be handled by the main login flow
            console.log("Login success notification will be handled by main flow");
          }
          console.log("isLoggedIn>>>", isLoggedIn);
          console.log("customerToken>>>>", customerToken);

          try {
            const customerData = await fetchCustomerData(data.accessToken);
            console.log(
              "👤 Customer Profile:",
              JSON.stringify(customerData, null, 2)
            );
            await AsyncStorage.setItem(
              "customerData",
              JSON.stringify(customerData, null, 2)
            );

            userData.customerData = customerData;

            if (customerData?.data?.customer) {
              const email =
                customerData.data.customer.emailAddress?.emailAddress;

              const accountNumber = customerData.data.customer.formattedId;
              console.log("🔑 Account Number:", accountNumber);
              if (email) {
                await AsyncStorage.setItem("customerEmail", email);
              }
              if (accountNumber) {
                await AsyncStorage.setItem("accountNumber", accountNumber);
              }
            }
          } catch (error) {
            console.error("Failed to fetch customer data:", error);
            // If token validation fails, try to get a new token
            if (
              error instanceof Error &&
              error.message.includes("Token validation failed")
            ) {
              console.log("🔄 Attempting to get new token...");
              // Trigger OAuth flow
              const { initiateLogin } = useShopifyAuth();
              await initiateLogin();
              isNavigatingRef.current = false;
              return;
            }
          }
        }

        if (data.customerData) {
          await AsyncStorage.setItem(
            "customerData",
            JSON.stringify(data.customerData)
          );
        }
        if (data.accountNumber) {
          await AsyncStorage.setItem("accountNumber", data.accountNumber);
        }

        // Mark that we're logged in
        setIsLoggedIn(true);
        hasLoggedInRef.current = true;

        // Call onLoginSuccess with the userData first to ensure data is properly stored
        onLoginSuccess(userData);
        if (hasDispatchedNavigationRef.current) {
          console.log("🛑 Navigation already dispatched. Skipping...");
          return;
        }
        hasDispatchedNavigationRef.current = true;
        // Immediately check verification status and redirect
        try {
          const isVerified = await checkVerificationStatus();
          console.log("User verification status:", isVerified);

          // Forcefully navigate based on verification status
          if (isVerified) {
            console.log("User is verified, redirecting to Home");
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [
                  { name: "DrawerNavigation", params: { screen: "Home" } },
                ],
              })
            );
          } else {
            console.log(
              "User is not verified, redirecting to CompanyRegistration"
            );
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [{ name: "CompanyRegistration" }],
              })
            );
          }

          // Don't set loading to false here - the component will unmount anyway
        } catch (error) {
          console.error("Error checking verification status:", error);
          // Default to CompanyRegistration if verification check fails
          console.log(
            "Error in verification check, defaulting to CompanyRegistration"
          );
          navigation.dispatch(
            CommonActions.reset({
              index: 0,
              routes: [{ name: "CompanyRegistration" }],
            })
          );
        }
      }
    } catch (error) {
      console.error("❌ Error parsing cookie message:", error);
      setLoading(false);
      isNavigatingRef.current = false;
    }
  };

  // Handle initial load and subsequent navigation
  const handleLoadStart = (event: any) => {
    // For initial load, don't show loading indicator for login pages
    if (!initialLoadComplete) {
      // If it's a login or authentication page, don't show the loader
      if (
        event.nativeEvent &&
        event.nativeEvent.url &&
        (event.nativeEvent.url.includes("/login") ||
          event.nativeEvent.url.includes("/authentication"))
      ) {
        console.log("👁️ Initial load of login page, keeping it visible");
        setLoading(false);
        isNavigatingRef.current = false;
      } else {
        // For other pages during initial load, show loader
        setLoading(true);
      }
      return;
    }

    // Show loading for OTP submission or any account page
    // But only for account pages after login (not the login page itself)
    if (
      event.nativeEvent &&
      event.nativeEvent.url &&
      // For OTP pages, always show loader if we're in the verification process
      ((event.nativeEvent.url.includes("otp") && isNavigatingRef.current) ||
        // For account pages (but not login or authentication pages), show loader
        ((event.nativeEvent.url.includes("/account/profile") ||
          event.nativeEvent.url.includes("/account/orders") ||
          event.nativeEvent.url.includes("/account?") ||
          event.nativeEvent.url.includes("/account/") ||
          (event.nativeEvent.url.includes("/account") &&
            isNavigatingRef.current)) &&
          !event.nativeEvent.url.includes("/login") &&
          !event.nativeEvent.url.includes("/authentication")))
    ) {
      // For OTP, show loader
      if (event.nativeEvent.url.includes("otp")) {
        console.log("🔄 OTP page loading, showing loader...");
        setLoading(true);
      }
      // For account pages (not login), show loader and set navigating flag
      else if (
        !event.nativeEvent.url.includes("/login") &&
        !event.nativeEvent.url.includes("/authentication")
      ) {
        console.log("🔄 Account page loading, showing loader...");
        setLoading(true);
        isNavigatingRef.current = true;
      }
    }
  };

  // Function to control which URLs are allowed to load
  const handleShouldStartLoad = (request: any) => {
    const url = request.url || "";

    // If we're already logged in, block all navigation
    if (hasLoggedInRef.current) {
      console.log("🛑 Already logged in, blocking navigation to:", url);
      return false;
    }

    // For login and authentication pages, always allow and make sure they're visible
    // But if we're in the middle of OTP verification, keep the loader visible
    if (
      (url.includes("/login") || url.includes("/authentication")) &&
      !(url.includes("otp") && isNavigatingRef.current)
    ) {
      console.log("👁️ Allowing login/auth page to load:", url);
      setLoading(false);
      isNavigatingRef.current = false;
      return true;
    }

    // For OTP pages when we're already in the verification process, keep the loader visible
    if (url.includes("otp") && isNavigatingRef.current) {
      console.log("🔒 Keeping loader visible for OTP verification:", url);
      setLoading(true);
      return true;
    }

    // For profile page or any account page after login form submission
    // But exclude the login page itself
    if (
      (url.includes("/account/profile") ||
        url.includes("/account/orders") ||
        url.includes("/account?") ||
        url.includes("/account/") ||
        (url.includes("/account") && isNavigatingRef.current)) &&
      !url.includes("/login") &&
      !url.includes("/authentication")
    ) {
      // Log that we're allowing the profile page to load in the background
      if (url.includes("/account/profile")) {
        console.log("🔒 Allowing profile page to load in background:", url);
      } else {
        console.log("🔑 Allowing account page to load in background:", url);
      }

      // Show loading screen immediately and set navigating flag
      setLoading(true);
      isNavigatingRef.current = true;

      // Extract data from the page after it loads
      setTimeout(() => {
        // Only inject if we're not already logged in
        if (!hasLoggedInRef.current) {
          webViewRef.current?.injectJavaScript(`
            (function() {
              try {
                // Try to extract login data from localStorage
                let accessToken = null;
                for (let i = 0; i < localStorage.length; i++) {
                  const key = localStorage.key(i);
                  if (key?.includes('__CUSTOMER_ACCOUNT_EXCHANGE_TOKEN__')) {
                    const item = localStorage.getItem(key);
                    try {
                      const parsed = JSON.parse(item);
                      if (parsed?.accessToken) {
                        accessToken = parsed.accessToken;
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                          isLoggedIn: true,
                          accessToken: accessToken
                        }));
                        return;
                      }
                    } catch (_) {}
                  }
                }
              } catch (error) {
                console.error("Error extracting token:", error);
              }
            })();
            true;
          `);
        }
      }, 50); // Reduced timeout for faster response

      // Allow the profile page to load in the background but keep it hidden with the loading overlay
      return true;
    }

    // Detect form submission on OTP page only (not email form submission)
    if (
      (url.includes("otp") &&
        (request.navigationType === "formsubmit" ||
          request.navigationType === "other"))
    ) {
      console.log("🔑 OTP submitted, showing loader...");
      setLoading(true);
      // Set a flag to indicate we're expecting a profile redirect
      isNavigatingRef.current = true;
    }

    // Allow all other URLs to load
    return true;
  };

  const handleLoadEnd = (event: any) => {
    // Clear any existing timeout
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }

    // Don't hide loading indicator if we're in the middle of a login flow
    // or if we're on a profile/account page
    if (isNavigatingRef.current || hasLoggedInRef.current || isLoggedIn) {
      console.log("🔒 Keeping loader visible during login flow");

      // Still mark initial load as complete
      if (!initialLoadComplete) {
        setInitialLoadComplete(true);
      }
      return;
    }

    // Check if we're on an account page (but not login or authentication pages)
    if (
      event.nativeEvent &&
      event.nativeEvent.url &&
      (event.nativeEvent.url.includes("/account/profile") ||
        event.nativeEvent.url.includes("/account/orders") ||
        event.nativeEvent.url.includes("/account?") ||
        (event.nativeEvent.url.includes("/account/") &&
          !event.nativeEvent.url.includes("/account/login"))) &&
      !event.nativeEvent.url.includes("/login") &&
      !event.nativeEvent.url.includes("/authentication")
    ) {
      console.log(
        "🔒 Keeping loader visible for account page:",
        event.nativeEvent.url
      );
      isNavigatingRef.current = true;
      return;
    }

    // For login and authentication pages, allow them to be visible
    // But keep OTP pages hidden if we're in the verification process
    if (
      event.nativeEvent &&
      event.nativeEvent.url &&
      (event.nativeEvent.url.includes("/login") ||
        event.nativeEvent.url.includes("/authentication"))
    ) {
      // If it's an OTP page and we're in the verification process, keep the loader visible
      if (event.nativeEvent.url.includes("otp") && isNavigatingRef.current) {
        console.log(
          "🔒 Keeping loader visible for OTP verification:",
          event.nativeEvent.url
        );
        setLoading(true);
        return;
      }

      console.log("👁️ Showing login/auth page:", event.nativeEvent.url);
      // Only set loading to false if we're not in the middle of a form submission
      if (!isNavigatingRef.current) {
        setLoading(false);
      }
      return;
    }

    // Set a timeout to prevent flickering if another load starts soon
    loadingTimeoutRef.current = setTimeout(() => {
      setLoading(false);
      if (!initialLoadComplete) {
        setInitialLoadComplete(true);
      }
    }, 300); // Small delay to prevent flickering
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, []);

  return (
    <View style={{ flex: 1 }}>
      {/* Always show WebView when not logged in */}
      {!hasLoggedInRef.current && !isLoggedIn && (
        <WebView
          ref={webViewRef}
          source={{ uri: uri }}
          onNavigationStateChange={handleNavigationStateChange}
          onMessage={handleMessage}
          onLoadStart={handleLoadStart}
          onLoadEnd={handleLoadEnd}
          onShouldStartLoadWithRequest={handleShouldStartLoad}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          sharedCookiesEnabled={true}
          thirdPartyCookiesEnabled={true}
          style={{
            flex: 1,
            // Hide the WebView when we're in the middle of a login process
            // or when we're on an OTP page during verification
            display: loading && isNavigatingRef.current ? "none" : "flex",
            // Use opacity as a backup to ensure it's completely invisible
            opacity: loading && isNavigatingRef.current ? 0 : 1,
            height: loading && isNavigatingRef.current ? 0 : "auto",
            overflow: loading && isNavigatingRef.current ? "hidden" : "visible",
          }}
        />
      )}

      {/* Only show loading indicator when we're in a login process or already logged in */}
      {((loading && isNavigatingRef.current) ||
        hasLoggedInRef.current ||
        isLoggedIn) && (
          <View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "#fff",
              zIndex: 9999, // Ensure it's on top of everything
            }}
          >
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text
              style={{
                marginTop: 20,
                ...FONTS.fontMedium,
                fontSize: 16,
                color: COLORS.text,
                textAlign: "center",
                paddingHorizontal: 20,
              }}
            >
              {isLoggedIn || hasLoggedInRef.current
                ? "Login successful! Redirecting to company page..."
                : "Verifying login information..."}
            </Text>
          </View>
        )}
    </View>
  );
}

export default ShopifyWebView;
