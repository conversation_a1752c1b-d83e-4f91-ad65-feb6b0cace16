import React, {
  useRef,
  useState,
  useEffect,
  useMemo,
  useCallback,
  memo,
} from "react";
import {
  View,
  Image,
  Text,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  TextInput,
  Animated,
  StyleSheet,
  TouchableWithoutFeedback,
} from "react-native";
import { gql, useQuery } from "@apollo/client";

import {
  GET_HOMEPAGE_BANNERS,
  GET_COLLECTION_PRODUCTS,
  GET_HOMEPAGE_CMS,
  HOME_PAGE_FEATURED_COLLECTION,
} from "../../api/homepageQuery";
import { ApolloProvider } from "@apollo/client";
import client from "../../api/appoloClient";

import { GlobalStyleSheet } from "../../constants/StyleSheet";
import Swiper from "react-native-swiper/src";
import { IMAGES } from "../../constants/Images";
import Header from "../../layout/Header";
import Button from "../Button/Button";
import {
  Button<PERSON>abel,
  COLORS,
  FONTS,
  FONT<PERSON>EIGHT,
  SIZES,
} from "../../constants/theme";
import { Colors } from "react-native/Libraries/NewAppScreen";
import CardHeader from "../CardHeader/CardHeader";
import CardSlider from "../CardSlider/CardSlider";
import SingleBannerImage, {
  SingleSmallBannerImage,
} from "../SingleBannerImage/SingleBannerImage";
import AddToCartCard from "../AddToCartCard/AddToCartCard";
import SingleCollectionBannerImage from "../SingleBannerImage/SingleCollectionBannerImage";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import SearchImg from "../../assets/icons/search.png";
import { useNavigation } from "@react-navigation/native";
import { ActivityIndicator } from "react-native-paper";


export default function MainHome() {
  const navigation = useNavigation();

  const [opacity] = useState(new Animated.Value(1));
  const [activeCategory, setActiveCategory] = useState<string>("");
  const [categoryData, setCategoryData] = useState<any[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<{
    [key: string]: string;
  }>({});

  const handleCategoryChange = (sectionKey: string, categoryTitle: string) => {
    setSelectedCategories((prev) => ({
      ...prev,
      [sectionKey]: categoryTitle,
    }));
  };

  const {
    loading,
    error,
    data: homePageData,
  } = useQuery(HOME_PAGE_FEATURED_COLLECTION);

  if (loading)
    return (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          height: 600,
        }}
      >
        <ActivityIndicator size="large" color={COLORS.primary} />
      </View>
    );
  if (error)
    return (
      <View>
        <Text> Error: {error.message} </Text>
      </View>
    );
  const sectionNameToQueryKey: any = {
    "top Banner slider": "top Banner slider",
    "Explore our category": "Explore our category",
    "Single banner 1": "Single banner 1",
    "product by collection 1": "product by collection 1",
    "Single-banner-2": "Single-banner-2",
    "product by collection 2": "product by collection 2",
    "Single-banner-3": "Single-banner-3",
    "APP - Shop by Row 2": "APP - Shop by Row 2",
    "product by collection 3": "product by collection 3",
    "Single-banner-4": "Single-banner-4",
    "product by collection 4": "product by collection 4",
  };

  const normalizeSectionNodes = (nodes: any) => {
    return nodes.map((node: any) => {
      const section: any = {};
      node.fields.forEach((field: any) => {
        section[field.key] = field.value;
      });
      return section;
    });
  };

  const rawSections = homePageData.Section_sorting.nodes;

  const sections = normalizeSectionNodes(rawSections);

  const visibleSortedSections = sections
    .filter((section: any) => section.show_section === "true")
    .sort((a: any, b: any) => Number(a.position) - Number(b.position));

  const blinkAnimation = () => {
    Animated.sequence([
      Animated.timing(opacity, {
        toValue: 0, // Fade out
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(opacity, {
        toValue: 1, // Fade in
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };
  const handleFocus = () => {
    navigation.navigate("Search");
  };
     

  return (
    <GestureHandlerRootView style={styles.container}>
      <ScrollView>
        <View style={{}}>
          <View style={{ marginTop: 20, backgroundColor: Colors.background }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={handleFocus}
              style={{
                height: 48,
                flex: 1,
                borderRadius: 30,
              }}
            >
              <Image
                source={SearchImg}
                style={{
                  position: "absolute",
                  width: 18,
                  height: 18,
                  top: 12,
                  left: 25,
                  zIndex: 1000,
                  shadowColor: "red",
                  shadowOffset: { x: 0, y: 0 },
                  shadowOpacity: 0.5,
                  shadowRadius: 25,
                }}
              />
              <TextInput
                style={{
                  ...FONTS.fontRegular,
                  fontSize: 16,
                  color: "black",
                  paddingLeft: 40,
                  flex: 1,
                  marginHorizontal: 10,
                  borderRadius: 30,
                  backgroundColor: "white",
                }}
                placeholder="Search"
                placeholderTextColor="gray"
                editable={false}
              />
            </TouchableOpacity>

            <ScrollView
              showsVerticalScrollIndicator={false}
              style={{ backgroundColor: COLORS.background }}
            >
              {visibleSortedSections.map((section: any, index: any) => {
                const sectionName: any = section.section_name;

                // If section is not in our list, skip rendering
                if (!sectionNameToQueryKey[sectionName]) return null;

                let sectionData = [];

                // Match section data from GraphQL response
                switch (sectionName) {
                  case "top Banner slider":
                    sectionData =
                      homePageData.App_top_banner_slider?.nodes || [];
                    const banners = sectionData.map((item: any) => {
                      const fields: any = {};
                      item.fields.forEach((field: any) => {
                        fields[field.key] = field;
                      });

                      return {
                        imageUrl:
                          fields.add_image_banner?.reference?.image?.url,
                        handle: fields.select_collection_url?.reference?.handle,
                      };
                    });
                    return (
                      <View
                        style={[GlobalStyleSheet.container, { padding: 0 }]}
                      >
                        <View style={{ zIndex: 12, marginVertical: 10 }}>
                          <Swiper
                            autoplay
                            autoplayDelay={0.1}
                            autoplayLoop
                            height={"auto"}
                            onIndexChanged={blinkAnimation}
                            showsButtons={false}
                            loop={true}
                            paginationStyle={{
                              bottom: 10,
                            }}
                            dotStyle={{
                              width: 8,
                              height: 8,
                              backgroundColor: "rgb(255, 255, 255)",
                              // opacity: 0.5,
                              borderRadius: 10,
                            }}
                            activeDotStyle={{
                              width: 25,
                              height: 8,
                              backgroundColor: "rgba(0, 0, 0, 1)",
                              // opacity: 0.5,
                              borderRadius: 10,
                            }}
                          >
                            {banners?.map((data: any, index: any) => {
                              return (
                                <View
                                  key={data.imageUrl}
                                  style={{
                                    justifyContent: "center",
                                    alignItems: "center",
                                  }}
                                >
                                  <Image
                                    key={index}
                                    style={{
                                      width: "90%",
                                      height: undefined,
                                      aspectRatio: 1 / 1,
                                      borderRadius: 15,
                                      objectFit: "cover",
                                    }}
                                    source={{ uri: data.imageUrl }}
                                  />
                                  <Button
                                    title={ButtonLabel.shopNow}
                                    btnRounded
                                    size="sm"
                                    onPress={() => {
                                      navigation.navigate(
                                        "ProductListingPage",
                                        { handle: data.handle }
                                      );
                                    }}
                                    style={{
                                      position: "absolute",
                                      bottom: 30,
                                      left: 240,
                                      paddingVertical: 4,
                                      opacity: 0.9,
                                    }}
                                    text="white"
                                  />
                                </View>
                              );
                            })}
                          </Swiper>
                        </View>
                      </View>
                    );
                  case "Explore our category":
                    const exploreCategoryData =
                      homePageData.Explore_category?.nodes || [];
                    const sectionTitle = exploreCategoryData?.[0]?.fields?.find(
                      (f: any) => f.key === "section_title"
                    )?.value;

                    // Extract all category items
                    const categories = exploreCategoryData.map((node: any) => {
                      const fields: any = {};
                      node.fields.forEach((f) => {
                        fields[f.key] = f;
                      });

                      return {
                        imageUrl: fields.image?.reference?.image?.url,
                        title: fields.title?.value,
                        handle: fields.select_collection_?.reference?.handle,
                      };
                    });

                    return (
                      <View style={{ paddingHorizontal: 16, marginTop: 40 }}>
                        <CardHeader
                          cardHeaderTitle={sectionTitle}
                          viewAllProducts={{ title: "View All", data: "data" }}
                        />
                        <View style={{ marginTop: 15 }}>
                          <CardSlider products={categories} />
                        </View>
                      </View>
                    );
                  case "Single banner 1":
                    const singleBannerNode =
                      homePageData?.Single_banner?.nodes?.[0];

                    const imageUrl = singleBannerNode?.fields?.find(
                      (f: any) => f.key === "add_banner"
                    )?.reference?.image?.url;

                    const handle = singleBannerNode?.fields?.find(
                      (f: any) => f.key === "select_collection_for_redirection"
                    )?.reference?.handle;
                    return (
                      <View style={{ paddingHorizontal: 16, marginTop: 40 }}>
                        <SingleBannerImage
                          badge={{ badgeTitle: "Pre Order", bg: "white" }}
                          imgURL={imageUrl}
                          handle={handle}
                        />
                      </View>
                    );
                  case "product by collection 1":
                    const productNode =
                      homePageData?.Product_by_coll_1?.nodes?.[0]?.fields?.find(
                        (f: any) => f.key === "choose_collection"
                      );

                    const products =
                      productNode?.reference?.products?.edges?.map(
                        (edge: any) => {
                          const product = edge.node;

                          return {
                            title: product.title,
                            handle: product.handle,
                            image: product.featuredImage?.src,
                            vendor: product.vendor,
                            tags: Array.isArray(product.tags) ? product.tags : [],
                            variants: product.variants?.edges?.map(
                              (variantEdge: any) => {
                                const variant = variantEdge.node;

                                return {
                                  title: variant.title,
                                  price: variant?.price?.amount || "0.00", // Make sure price is being queried
                                  image:
                                    variant?.image?.src ||
                                    product.featuredImage?.src, // Fallback
                                };
                              }
                            ),
                          };
                        }
                      ) || [];

                    return (
                      <View
                        style={{
                          marginHorizontal: 16,
                          marginTop: 10,
                          backgroundColor: COLORS.background,
                        }}
                      >
                        <AddToCartCard products={products} />
                      </View>
                    );
                  case "Single-banner-2": {
                    const singleBannerNode =
                      homePageData?.Single_banner_2?.nodes?.[0];

                    const imageUrl = singleBannerNode?.fields?.find(
                      (f: any) => f.key === "select_image_banner"
                    )?.reference?.image?.url;

                    const handle = singleBannerNode?.fields?.find(
                      (f: any) => f.key === "select_collection_for_redirection"
                    )?.reference?.handle;

                    return (
                      <View style={{ paddingHorizontal: 16, marginTop: 40 }}>
                        <SingleBannerImage
                          // badge={{ badgeTitle: "New", bg: "white" }}
                          imgURL={imageUrl}
                          handle={handle}
                        />
                      </View>
                    );
                  }
                  case "product by collection 2": {
                    const collectionNode =
                      homePageData?.Product_by_coll_2?.nodes?.[0];

                    const fields = collectionNode?.fields || [];

                    const sectionTitle = fields.find(
                      (f: any) => f.key === "section_title"
                    )?.value;

                    const products =
                      fields.find((f: any) => f.key === "choose_collection")
                        ?.reference?.products?.edges || [];

                    const formattedProducts = products.map(
                      (productEdge: any) => {
                        const product = productEdge.node;

                        return {
                          title: product.title,
                          handle: product.handle,
                          image: product.featuredImage?.src,
                          vendor: product.vendor,
                          variants: product.variants.edges.map(
                            (variant: any) => ({
                              title: variant.node.title,
                              price: variant.node?.price?.amount || "0.00",
                              image:
                                variant.node?.image?.src ||
                                product.featuredImage?.src,
                            })
                          ),
                        };
                      }
                    );

                    console.log("sectionTitle ", sectionTitle);

                    return (
                      <View
                        style={{
                          marginHorizontal: 16,
                          marginTop: 40,
                          backgroundColor: COLORS.background,
                        }}
                      >
                        <View>
                          <CardHeader
                            cardHeaderTitle={sectionTitle}
                            viewAllProducts={{
                              title: "View All",
                              data: "data",
                            }}
                          />
                        </View>
                        <View style={{ marginTop: 15 }}>
                          <AddToCartCard products={formattedProducts} />
                        </View>
                      </View>
                    );
                  }
                  case "Single-banner-3": {
                    const singleBannerNode =
                      homePageData?.Single_banner_3?.nodes?.[0];

                    const imageUrl = singleBannerNode?.fields?.find(
                      (f: any) => f.key === "select_image_banner"
                    )?.reference?.image?.url;

                    const handle = singleBannerNode?.fields?.find(
                      (f: any) => f.key === "select_collection_for_redirection"
                    )?.reference?.handle;
                    console.log("imgurl", imageUrl);
                    return (
                      <View style={{ marginTop: 40 }}>
                        <SingleSmallBannerImage
                          // badge={{ badgeTitle: "New", bg: "white" }}
                          imgURL={imageUrl}
                          handle={handle}
                          fullImage={true}
                        />
                      </View>
                    );
                  }
                  case "APP - Shop by Row 2": {
                    const nodes = homePageData?.BadmintoCollection?.nodes || [];

                    const formattedCategories = nodes.map((node: any) => {
                      const fields = node.fields || [];

                      const collectionField = fields.find(
                        (f: any) => f.key === "collection_handle"
                      );
                      const imageField = fields.find(
                        (f: any) => f.key === "image"
                      );

                      const handle = collectionField?.value || "";
                      const title = handle;
                      const imageUrl = imageField?.reference?.image?.url || "";

                      return {
                        title,
                        handle,
                        imageUrl,
                      };
                    });

                    console.log(
                      "Shop by Row 2 Categories:",
                      formattedCategories
                    );

                    return (
                      <View style={{ paddingHorizontal: 16, marginTop: 10 }}>
                        <CardSlider
                          products={formattedCategories}
                          smallCard={true}
                        />
                      </View>
                    );
                  }
                  case "product by collection 3": {
                    const sectionKey = "product by collection 3";

                    const collectionNodes =
                      homePageData?.Product_by_coll_3?.nodes || [];

                    const formattedCategoryData = collectionNodes.map(
                      (node: any) => {
                        const collectionField = node.fields.find(
                          (f: any) => f.key === "choose_collection"
                        );
                        const sectionTitleField = node.fields.find(
                          (f: any) => f.key === "section_title"
                        );

                        const categoryTitle =
                          collectionField?.reference?.title || "Unknown";
                        const sectionTitle =
                          sectionTitleField?.value || categoryTitle;

                        const products =
                          collectionField?.reference?.products?.edges || [];

                        const formattedProducts = products.map(
                          (productEdge: any) => {
                            const product = productEdge.node;

                            return {
                              title: product.title,
                              handle: product.handle,
                              image: product.featuredImage?.src,
                              vendor: product.vendor,
                              variants: product.variants.edges.map(
                                (variant: any) => ({
                                  title: variant.node.title,
                                  price: variant?.node?.price?.amount || "0.00",
                                  image:
                                    variant?.node?.image?.src ||
                                    product.featuredImage?.src,
                                })
                              ),
                            };
                          }
                        );

                        return {
                          categoryTitle,
                          sectionTitle,
                          products: formattedProducts,
                        };
                      }
                    );

                    // Use state to store selected category
                    const [selectedCategory, setSelectedCategory] =
                      React.useState(formattedCategoryData[0]?.categoryTitle);

                    return (
                      <View style={{ marginTop: 40, gap: 15 }}>
                        {/* Section Header */}
                        <View style={{ marginHorizontal: 16 }}>
                          <CardHeader
                            cardHeaderTitle={
                              formattedCategoryData.find(
                                (cat) => cat.categoryTitle === selectedCategory
                              )?.sectionTitle || "Shop"
                            }
                            viewAllProducts={{
                              title: "View All",
                              data: "data",
                            }}
                          />
                        </View>

                        {/* Category Tabs */}
                        <View style={{ marginHorizontal: 5 }}>
                          <ScrollView
                            horizontal
                            showsHorizontalScrollIndicator={false}
                            contentContainerStyle={{
                              gap: 10,
                              flexDirection: "row",
                              paddingHorizontal: 10,
                            }}
                            style={{ width: "100%" }}
                          >
                            {formattedCategoryData.map((cat, index) => (
                              <TouchableOpacity
                                key={cat.categoryTitle + index}
                                onPress={() =>
                                  handleCategoryChange(
                                    sectionKey,
                                    cat.categoryTitle
                                  )
                                }
                                style={{
                                  backgroundColor:
                                    selectedCategory === cat.categoryTitle
                                      ? "black"
                                      : "white",
                                  justifyContent: "center",
                                  alignItems: "center",
                                  borderRadius: 20,
                                  borderWidth: 1,
                                  borderColor: COLORS.darkgray,
                                  paddingVertical: 8,
                                  paddingHorizontal: 20,
                                }}
                              >
                                <Text
                                  style={{
                                    color:
                                      selectedCategory === cat.categoryTitle
                                        ? COLORS.white
                                        : COLORS.darkgray,
                                    fontSize: SIZES.fontLg,
                                    fontFamily: "DMSansBlackItalic",
                                  }}
                                >
                                  {cat.categoryTitle}
                                </Text>
                              </TouchableOpacity>
                            ))}
                          </ScrollView>
                        </View>

                        {/* Product List for Selected Category */}
                        {formattedCategoryData
                          .filter(
                            (cat) => cat.categoryTitle === selectedCategory
                          )
                          .map((cat) => (
                            <View
                              key={cat.categoryTitle + "_section"}
                              style={{
                                marginHorizontal: 16,
                                backgroundColor: COLORS.background,
                              }}
                            >
                              <AddToCartCard products={cat.products} />
                            </View>
                          ))}
                      </View>
                    );
                  }
                  case "Single-banner-4": {
                    const singleBannerNode =
                      homePageData?.Single_banner_4?.nodes?.[0];

                    const imageUrl = singleBannerNode?.fields?.find(
                      (f: any) => f.key === "select_image_banner"
                    )?.reference?.image?.url;

                    const handle = singleBannerNode?.fields?.find(
                      (f: any) => f.key === "select_collection_for_redirection"
                    )?.reference?.handle;
                    console.log("imgurl", imageUrl);
                    return (
                      <View style={{ marginTop: 40 }}>
                        <SingleSmallBannerImage
                          // badge={{ badgeTitle: "New", bg: "white" }}
                          imgURL={imageUrl}
                          handle={handle}
                          fullImage={true}
                        />
                      </View>
                    );
                  }
                  case "product by collection 4": {
                    const collectionNode =
                      homePageData?.Product_by_coll_4?.nodes?.[0];

                    const fields = collectionNode?.fields || [];

                    const sectionTitle = fields.find(
                      (f: any) => f.key === "section_title"
                    )?.value;

                    const products =
                      fields.find((f: any) => f.key === "choose_collection")
                        ?.reference?.products?.edges || [];

                    const formattedProducts = products.map(
                      (productEdge: any) => {
                        const product = productEdge.node;

                        return {
                          title: product.title,
                          handle: product.handle,
                          image: product.featuredImage?.src,
                          vendor: product.vendor,
                          variants: product.variants.edges.map(
                            (variant: any) => ({
                              title: variant.node.title,
                              price: variant.node?.price?.amount || "0.00",
                              image:
                                variant.node?.image?.src ||
                                product.featuredImage?.src,
                            })
                          ),
                        };
                      }
                    );

                    return (
                      <View
                        style={{
                          marginHorizontal: 16,
                          marginTop: 10,
                          backgroundColor: COLORS.background,
                        }}
                      >
                        <AddToCartCard products={formattedProducts} />
                      </View>
                    );
                  }

                  default:
                    return null;
                }
              })}

              {/*
              <View style={{ paddingHorizontal: 16, marginTop: 40 }}>
                <SingleBannerImage imgURL="https://cdn.grabon.in/gograbon/images/merchant/1575960889194.png" />
                <View style={{ marginTop: 15 }}>
                  <AddToCartCard products={finalProductData} />
                </View>
              </View>
              <View style={{ paddingHorizontal: 16, marginTop: 40 }}>
                <SingleBannerImage
                  badge={{ badgeTitle: "Badminton", bg: "white" }}
                  imgURL="https://t3.ftcdn.net/jpg/03/10/62/12/360_F_310621281_foEqKBGtGlNWFQRePgdF5BpLOFyTsnzO.jpg"
                />
                <View style={{ marginTop: 15 }}>
                  <CardSlider products={SponsoredData} smallCard={true} />
                </View>
              </View>
              <View style={{ paddingHorizontal: 16, gap: 15, marginTop: 40 }}>
                <CardHeader
                  cardHeaderTitle="Shop Badminton"
                  viewAllProducts={{ title: "View All", data: "data" }}
                />
                <ScrollView
                  horizontal
                  pagingEnabled // Smooth snapping effect
                  decelerationRate="fast" // Faster deceleration
                  scrollEventThrottle={16}
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={{
                    gap: 10,
                    flexDirection: "row",
                  }}
                >
                  {BadmintonCategory?.map((category, index) => {
                    return (
                      <TouchableOpacity
                        key={category + "" + index}
                        // onPress={() => changeCategory(category)}
                        style={{
                          backgroundColor:
                            activeCategory === category ? "black" : "white",
                          justifyContent: "center",
                          alignItems: "center",
                          borderRadius: 20,
                          borderWidth: 1,
                          borderColor: COLORS.darkgray,
                          paddingVertical: 8,
                          paddingHorizontal: 20,
                        }}
                      >
                        <Text
                          style={{
                            color:
                              activeCategory === category
                                ? COLORS.white
                                : COLORS.darkgray,
                            fontSize: SIZES.fontLg,
                            fontFamily: "DMSansBlackItalic",
                          }}
                        >
                          {category}
                        </Text>
                      </TouchableOpacity>
                    );
                  })}
                </ScrollView>
                <AddToCartCard products={finalProductData} />
                <SingleBannerImage
                  badge={{ badgeTitle: "Badminton", bg: "white" }}
                  imgURL="https://media.istockphoto.com/id/1761333789/photo/badminton-shuttlecocks-and-racket-placed-in-the-corner-of-a-synthetic-field.jpg?s=612x612&w=0&k=20&c=3rr4BZqe1rDWsCe6LF_YPCXZe6Um5jizc6d6n96U1Q4="
                />
                <AddToCartCard products={finalProductData} />
              </View>
              <View
                style={{
                  paddingHorizontal: 16,
                  marginTop: 40,
                  gap: 15,
                }}
              >
                <CardHeader
                  cardHeaderTitle="Shop Tennis"
                  viewAllProducts={{ title: "View All", data: "data" }}
                />
                <ScrollView
                  horizontal
                  pagingEnabled // Smooth snapping effect
                  decelerationRate="fast" // Faster deceleration
                  scrollEventThrottle={16}
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={{
                    gap: 5,
                    flexDirection: "row",
                  }}
                >
                  {BadmintonCategory.map((category, index) => {
                    return (
                      <TouchableOpacity
                        key={category + "" + index}
                        key={category}
                        // onPress={() => changeCategory(category)}
                        style={{
                          width: 100,
                          backgroundColor:
                            activeCategory === category ? "black" : "white",
                          justifyContent: "center",
                          alignItems: "center",
                          borderRadius: 25,
                          padding: 8,
                        }}
                      >
                        <Text
                          style={{
                            color:
                              activeCategory === category ? "white" : "black",
                          }}
                        >
                          {category}
                        </Text>
                      </TouchableOpacity>
                    );
                  })}
                </ScrollView>
                <AddToCartCard products={finalProductData} />
                <SingleBannerImage
                  badge={{ badgeTitle: "Badminton", bg: "white" }}
                  imgURL="https://t3.ftcdn.net/jpg/00/38/59/44/360_F_38594431_y0XRoIsqk7hj1VLv8WzNFuccl2OTmpia.jpg"
                />
                <AddToCartCard products={finalProductData} />
              </View>
              <View style={{ paddingHorizontal: 16, marginTop: 40 }}>
                <SingleBannerImage
                  title="Shop Running Shoes"
                  badge={{ badgeTitle: "Running", bg: "white" }}
                  imgURL="https://media.istockphoto.com/id/1350560575/photo/pair-of-blue-running-sneakers-on-white-background-isolated.jpg?s=612x612&w=0&k=20&c=A3w_a9q3Gz-tWkQL6K00xu7UHdN5LLZefzPDp-wNkSU="
                />
                <View style={{ marginTop: 15 }}>
                  <CardSlider products={SponsoredData} />
                </View>
              </View>
              <View style={{ paddingHorizontal: 16, marginTop: 40, gap: 15 }}>
                <CardHeader
                  cardHeaderTitle="Basketball Collection"
                  viewAllProducts={{ title: "View All", data: "data" }}
                />
                <AddToCartCard products={finalProductData}>
                  <SingleCollectionBannerImage imgUrl="https://encrypted-tbn3.gstatic.com/images?q=tbn:ANd9GcRFarVVHWPjdp4XNxFEZWJPzSiKcj0eWZ2WV3aNRvZG6NS6DKot" />
                </AddToCartCard>
              </View>
              <View style={{ paddingHorizontal: 16, marginTop: 40, gap: 15 }}>
                <CardHeader
                  cardHeaderTitle="Football Collection"
                  viewAllProducts={{ title: "View All", data: "data" }}
                />
                <AddToCartCard products={finalProductData}>
                  <SingleCollectionBannerImage imgUrl="https://i.pinimg.com/236x/e0/cf/b6/e0cfb601da6d68042329bf76501ce009.jpg" />
                </AddToCartCard>
              </View>
              <View style={[GlobalStyleSheet.container]}>
                <SingleBannerImage imgURL="https://www.yonex.com/media/scandiweb/slider/a/s/astrox88_sd_key_visual_2880x1120_.jpg" />
              </View>
              <View
                style={[
                  GlobalStyleSheet.container,
                  {
                    justifyContent: "center",
                    alignItems: "center",
                    gap: 10,
                  },
                ]}
              >
                <View>
                  <Text
                    style={{
                      ...FONTS.fontMedium,
                      ...FONTWEIGHT.SemiBold,
                      ...FONTS.h4,
                    }}
                  >
                    Know About Yonex Products
                  </Text>
                </View>
                <View
                  style={{ justifyContent: "center", alignItems: "center" }}
                >
                  <Text
                    style={{
                      textAlign: "center",
                      ...FONTWEIGHT.SemiBold,
                      ...FONTS.font,
                      ...FONTS.fontTitle,
                      lineHeight: 22,
                    }}
                  >
                    Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                    Quae, ea. Cum, dolor! Natus ducimus beatae, rerum
                    accusantium minus ab ipsam maxime quaerat necessitatibus
                    quaerat
                  </Text>
                </View>
                <TouchableOpacity
                  style={{
                    backgroundColor: COLORS.black,
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    borderRadius: 25,
                  }}
                >
                  <Text style={{ color: COLORS.white, ...FONTS.fontMedium }}>
                    Explore More{" "}
                  </Text>
                </TouchableOpacity>
              </View>
              <View></View> */}
            </ScrollView>
          </View>
        </View>
      </ScrollView>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    backgroundColor: "red ",
    alignItems: "center",
  },
});
