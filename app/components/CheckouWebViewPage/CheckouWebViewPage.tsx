import React, { useRef, useState } from "react";
import WebView from "react-native-webview";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useDispatch } from "react-redux";
import { setCartId } from "../../redux/reducer/cartReducer";
import { useMutation } from "@apollo/client";
import { CREATE_CART } from "../../api/cartQuery";
import CustomAlert from "../../components/CustomAlert";
import { useCustomAlert } from "../../hooks/useCustomAlert";
import NotificationService from "../../services/NotificationService";
import { CommonActions } from "@react-navigation/native";

export default function CheckouWebViewPage({ route, navigation }: any) {
  const webViewRef = useRef<WebView>(null);
  const [orderCompleted, setOrderCompleted] = useState(false);
  const dispatch = useDispatch();

  const { checkoutUrl } = route?.params;
  console.log("checkoutUrl>>>", checkoutUrl);

  const [createCart] = useMutation(CREATE_CART);

  // Custom Alert Hook
  const {
    alertConfig,
    visible: alertVisible,
    showSuccess,
    hideAlert,
  } = useCustomAlert();

  const clearCartData = async () => {
    try {
      console.log("🛒 Clearing cart data after successful order...");

      // Create empty cart using GraphQL mutation first to get new cart ID
      const response = await createCart({
        variables: {
          lines: [],
        },
      });

      const newCartId = response.data?.cartCreate?.cart?.id || "";
      console.log("🆕 New empty cart ID:", newCartId);

      // Clear AsyncStorage
      await AsyncStorage.removeItem("merchandiseId");

      // Update AsyncStorage and Redux with new empty cart ID
      if (newCartId) {
        await AsyncStorage.setItem("cartId", newCartId);
        dispatch(setCartId(newCartId));
      } else {
        await AsyncStorage.removeItem("cartId");
        dispatch(setCartId("" as any));
      }

      console.log("✅ Cart data cleared successfully");
      showSuccess(
        "Order Placed Successfully!",
        "Your order has been placed successfully",
        [
          {
            text: "OK",
            onPress: () => {
              // Navigate to Profile tab in BottomNavigation
              navigation.dispatch(
                CommonActions.navigate({
                  name: 'MyCart',
                  params: { cartId: newCartId },
                })
              );
            },
          },
        ]
      );
      // Send order success notification
      try {
        await NotificationService.sendFirebaseNotification(
          "Order Placed Successfully! 🎉",
          "Your order has been placed and confirmed. Thank you for shopping with us!",
          {
            type: "order_success",
            action: "view_orders",
            timestamp: new Date().toISOString(),
            cartId: newCartId,
          }
        );
        console.log("✅ Order success notification sent");
      } catch (notificationError) {
        console.error("❌ Failed to send notification:", notificationError);
        // Don't block the flow if notification fails
      }

      // Show success message
    } catch (error) {
      console.error("❌ Error clearing cart data:", error);

      // Send order success notification even if cart clearing failed
      try {
        await NotificationService.sendFirebaseNotification(
          "Order Placed Successfully! 🎉",
          "Your order has been placed and confirmed. Thank you for shopping with us!",
          {
            type: "order_success",
            action: "view_orders",
            timestamp: new Date().toISOString(),
            note: "cart_clearing_failed",
          }
        );
        console.log("✅ Order success notification sent (despite cart error)");
      } catch (notificationError) {
        console.error("❌ Failed to send notification:", notificationError);
        // Don't block the flow if notification fails
      }

      // Still show success message even if cart clearing fails
      showSuccess(
        "Order Placed Successfully!",
        "Your order has been placed successfully.",
        [
          {
            text: "OK",
            onPress: () => {
              hideAlert();
              // Navigate to Profile tab in BottomNavigation
              navigation.navigate("DrawerNavigation", { screen: "Home" });
            },
          },
        ]
      );
    }
  };

  const handleNavigationStateChange = (navState: any) => {
    console.log("🌐 WebView navigated to:", navState.url);

    // Check if the URL contains "thank-you" which indicates order completion
    if (navState.url && navState.url.includes("thank-you") && !orderCompleted) {
      console.log("🎉 Order completed! URL contains 'thank-you'");
      setOrderCompleted(true);
      clearCartData();
    }
  };

  return (
    <>
      <WebView
        ref={webViewRef}
        source={{ uri: checkoutUrl }}
        onNavigationStateChange={handleNavigationStateChange}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        sharedCookiesEnabled={true}
        thirdPartyCookiesEnabled={true}
        startInLoadingState={true}
      />

      {/* Custom Alert */}
      {alertConfig && (
        <CustomAlert
          visible={alertVisible}
          title={alertConfig.title}
          message={alertConfig.message}
          type={alertConfig.type}
          buttons={alertConfig.buttons}
          onClose={hideAlert}
        />
      )}
    </>
  );
}
