import { useEffect, useRef, useState } from "react";
import {
  Image,
  Platform,
  TouchableOpacity,
  View,
  Animated,
  Text,
  Dimensions,
} from "react-native";
import { COLORS, SIZES, FONTS, FONTWEIGHT } from "../constants/theme";
import { useTheme } from "@react-navigation/native";

import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from "react-native-responsive-screen";
import { IMAGES } from "../constants/Images";
import { GlobalStyleSheet } from "../constants/StyleSheet";
import { useSelector } from "react-redux";
import { LanguageState } from "../redux/reducer/languageReducer";
import { staticTranslations } from "../constants/staticTranslations";

type Props = {
  state: any;
  navigation: any;
  descriptors: any;
};

const BottomTab = ({ state, descriptors, navigation }: Props) => {
  const cart = useSelector((state: any) => state.cart.cart);
  const { locale }: LanguageState = useSelector((state: any)=> state.language);
  const staticLabels = locale === "en"? staticTranslations.english: staticTranslations.malay;

  const theme = useTheme();
  const { colors }: { colors: any } = theme;

  const [tabWidth, setWidth] = useState(wp("100%"));

  const tabWD = tabWidth < SIZES.container ? tabWidth / 5 : SIZES.container / 5;

  const circlePosition = useRef(new Animated.Value(0)).current;

  Dimensions.addEventListener("change", (val) => {
    setWidth(val.window.width);
  });

  useEffect(() => {
    Animated.spring(circlePosition, {
      toValue: state.index * tabWD,
      useNativeDriver: true,
    }).start();
  }, [state.index, tabWidth]);

  const onTabPress = (index: number) => {
    const tabW =
      tabWidth < SIZES.container ? tabWidth / 5 : SIZES.container / 5; // Adjust this according to your tab width

    Animated.spring(circlePosition, {
      toValue: index * tabW,
      useNativeDriver: true,
    }).start();
  };

  return (
    <View
      style={[
        {
          backgroundColor: colors.card,
          shadowColor: "rgba(0,0,0,1)",
          shadowOffset: {
            width: 0,
            height: 0,
          },
          shadowOpacity: 0.1,
          shadowRadius: 5,
          //position: 'absolute',
          left: 0,
          bottom: 0,
          right: 0,
        },
        Platform.OS === "ios" && {
          backgroundColor: colors.card,
        },
      ]}
    >
      <View
        style={{
          height: 60,
          backgroundColor: colors.cardbackground,
        }}
      >
        <View
          style={[
            GlobalStyleSheet.container,
            {
              flexDirection: "row",
              alignItems: "center",
              paddingHorizontal: 0,
              paddingTop: 0,
              paddingBottom: 0,
            },
            Platform.OS === "web" && { padding: 15 },
          ]}
        >
          <Animated.View
            style={{
              position: "absolute",
              height: "100%",
              width:
                tabWidth < SIZES.container ? tabWidth / 5 : SIZES.container / 5,
              alignItems: "center",
              justifyContent: "center",
              transform: [{ translateX: circlePosition }],
            }}
          >
            <View
              style={{
                height: 40,
                width: 40,
                borderRadius: 38,
                // backgroundColor: COLORS.danger,
                marginTop: 50,
              }}
            />
          </Animated.View>
          {state.routes.map((route: any, index: any) => {
            const { options } = descriptors[route.key];
            const label =
              options.tabBarLabel !== undefined
                ? options.tabBarLabel
                : options.title !== undefined
                ? options.title
                : route.name;

            const isFocused = state.index === index;

            const iconTranslateY = useRef(new Animated.Value(0)).current;
            Animated.timing(iconTranslateY, {
              toValue: isFocused ? -18 : 0,
              duration: 200,
              useNativeDriver: true,
            }).start();

            const onPress = () => {
              const event = navigation.emit({
                type: "tabPress",
                target: route.key,
                canPreventDefault: true,
              });

              if (!isFocused && !event.defaultPrevented) {
                navigation.navigate({ name: route.name, merge: true });
                onTabPress(index);
              }
            };

            return (
              <TouchableOpacity
                key={index}
                activeOpacity={0.8}
                accessibilityRole="button"
                accessibilityState={isFocused ? { selected: true } : {}}
                accessibilityLabel={options.tabBarAccessibilityLabel}
                testID={options.tabBarTestID}
                onPress={onPress}
                style={{
                  flex: 1,
                  alignItems: "center",
                  height: "100%",
                  justifyContent: "center",
                  marginTop: 5,
                  borderLeftWidth: 0,
                  borderRightWidth: 0,
                  borderBottomWidth: 0,
                  borderTopWidth: isFocused ? 2 : 0,
                  marginHorizontal: 10,
                  opacity: isFocused ? 1 : 0.6,
                }}
              >
                <Image
                  style={{
                    width: 21,
                    height: 21,
                    tintColor: isFocused ? COLORS.black : colors.title,
                    resizeMode: "contain",
                  }}
                  source={
                    label == staticLabels.home
                      ? IMAGES.home
                      : label == staticLabels.search
                      ? IMAGES.search
                      : label == staticLabels.my_cart
                      ? IMAGES.shopping
                      : label == staticLabels.category
                      ? IMAGES.document
                      : label == staticLabels.profile
                      ? IMAGES.user2
                      : IMAGES.home
                  }
                />
                <Text
                  style={{
                    ...FONTS.fontXs,
                    marginTop: 5,
                    fontFamily: "WorkSansMedium",
                    ...(isFocused ? FONTWEIGHT.SemiBold : FONTWEIGHT.Medium),
                  }}
                >
                  {label === "" ? "Home" : label}
                </Text>

                {/* {label == "MyCart" ? (
                  <View
                    style={[
                      GlobalStyleSheet.notification,
                      { position: "absolute", right: 15, bottom: 40 },
                    ]}
                  >
                    <Text
                      style={{
                        ...FONTS.fontRegular,
                        fontSize: 10,
                        color: COLORS.white,
                      }}
                    >
                      {cart.length}
                    </Text>
                  </View>
                ) : null} */}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    </View>
  );
};

export default BottomTab;
