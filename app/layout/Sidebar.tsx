import React from 'react';
import { Image, ScrollView, Text, TouchableOpacity, View, Alert } from 'react-native';
import { FONTS } from '../constants/theme';
import Feather from 'react-native-vector-icons/Feather';
import { useTheme, CommonActions } from '@react-navigation/native';
import ThemeBtn from '../components/ThemeBtn';
import { IMAGES } from '../constants/Images';
import { useShopifyAuth } from '../api/shopifyAuth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NotificationService from '../services/NotificationService';

// User info component to display user data
const UserInfo = ({ colors }) => {
    const [userName, setUserName] = React.useState('Guest');
    const [userEmail, setUserEmail] = React.useState('');

    React.useEffect(() => {
        const loadUserData = async () => {
            try {
                const customerDataStr = await AsyncStorage.getItem('customerData');
                if (customerDataStr) {
                    const customerData = JSON.parse(customerDataStr);
                    if (customerData?.data?.customer) {
                        const firstName = customerData.data.customer.firstName || '';
                        const lastName = customerData.data.customer.lastName || '';
                        const email = customerData.data.customer.emailAddress?.emailAddress || '';

                        setUserName(`${firstName} ${lastName}`.trim() || 'Guest');
                        setUserEmail(email);
                    }
                }
            } catch (error) {
                console.error('Error loading user data:', error);
            }
        };

        loadUserData();
    }, []);

    return (
        <View>
            <Text style={{ ...FONTS.fontSemiBold, fontSize: 18, color: colors.title }}>{userName}</Text>
            <Text style={{ ...FONTS.fontRegular, fontSize: 15, color: colors.title }}>{userEmail}</Text>
        </View>
    );
};

const Sidebar = ({navigation} : any) => {

    const theme = useTheme();
    const { colors } : {colors : any } = theme;
    const { logout } = useShopifyAuth();

    const handleLogout = async () => {
        Alert.alert(
            "Logout",
            "Are you sure you want to logout?",
            [
                {
                    text: "Cancel",
                    style: "cancel"
                },
                {
                    text: "Logout",
                    onPress: async () => {
                        try {
                            // Clear notification state
                            await NotificationService.onLogout();

                            await logout();
                            // Navigate to login screen
                            navigation.dispatch(
                                CommonActions.reset({
                                    index: 0,
                                    routes: [{ name: 'ShopifyLogin' }],
                                })
                            );
                        } catch (error) {
                            console.error('Logout error:', error);
                            Alert.alert('Error', 'Failed to logout. Please try again.');
                        }
                    }
                }
            ]
        );
    };

    const navItem = [
        {
            icon: IMAGES.home,
            name: "Home",
            navigate: "BottomNavigation",
        },
        {
            icon: IMAGES.producta,
            name: "Products",
            navigate: "Products",
        },
        {
            icon: IMAGES.components,
            name: "Components",
            navigate: "Components",
        },
        {
            icon: IMAGES.star,
            name: "Featured",
            navigate: "WriteReview",
        },
        {
            icon: IMAGES.heart2,
            name: "Wishlist",
            navigate: "Wishlist",
        },
        {
            icon: IMAGES.order,
            name: "My Orders",
            navigate: 'Myorder',
        },
        {
            icon: IMAGES.shopping,
            name: "My Cart",
            navigate: 'MyCart',
        },
        {
            icon: IMAGES.chat,
            name: "Chat List",
            navigate: 'Chat',
        },
        {
            icon: IMAGES.user2,
            name: "Profile",
            navigate: "Profile",
        },
        {
            icon: IMAGES.logout,
            name: "Logout",
            action: handleLogout,
        },
    ]

    return (
        <>
            <View style={{ flex: 1, backgroundColor: colors.background }}>
                <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
                    <View
                        style={{
                            paddingTop: 30,
                            paddingHorizontal: 20,
                            borderBottomWidth: 1,
                            borderColor: colors.border,
                            paddingBottom: 20,
                            marginBottom: 15,
                            alignItems: 'flex-start',
                        }}
                    >
                        <View style={{
                            flexDirection: 'row',
                        }}>
                            <View style={{
                                alignItems: 'flex-start',
                                flex: 1,
                            }}>
                                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10 }}>
                                    <Image
                                        style={{ height: 60, width: 60, resizeMode: 'contain', borderRadius: 20 }}
                                        source={IMAGES.small1}
                                    />
                                    <UserInfo colors={colors} />
                                </View>
                            </View>
                            <View style={{ position: 'absolute', right: 0, top: -10 }}>
                                <ThemeBtn />
                            </View>
                        </View>
                    </View>

                    <View style={{ flex: 1 }}>
                        {navItem.map((data, index) => {
                            return (
                                <TouchableOpacity
                                    onPress={() => {
                                        if (data.action) {
                                            data.action();
                                        } else if (data.navigate === "Account") {
                                            navigation.navigate('BottomNavigation', { screen: data.navigate });
                                        } else if (data.navigate) {
                                            navigation.navigate(data.navigate);
                                        }
                                    }}
                                    key={index}
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center',
                                        paddingHorizontal: 20,
                                        paddingVertical: 14,
                                    }}
                                >
                                    <View style={{ marginRight: 15 }}>
                                        <Image
                                            style={{ height: 20, width: 20, resizeMode: 'contain', tintColor: colors.title }}
                                            source={data.icon}
                                        />
                                    </View>
                                    <Text style={{ ...FONTS.fontTitle, fontSize: 14, color: colors.title, flex: 1 }}>{data.name}</Text>
                                    <Feather  size={16} color={colors.title} name='chevron-right' />
                                </TouchableOpacity>
                            )
                        })}
                    </View>

                    <View
                        style={{
                            paddingHorizontal: 20,
                            paddingVertical: 20,
                            marginTop: 10,
                            borderTopWidth: 1,
                            borderTopColor: colors.border
                        }}
                    >
                        <Text style={{ ...FONTS.fontSemiBold, fontSize: 13, color: colors.title, marginBottom: 4 }}>pixio<Text style={{ ...FONTS.fontRegular, fontSize: 13 }}> Fashion Store</Text></Text>
                        <Text style={{ ...FONTS.fontRegular, fontSize: 13, color: colors.title }}>App Version 1.0</Text>
                    </View>
                </ScrollView>
            </View>
        </>
    );
};

export default Sidebar;