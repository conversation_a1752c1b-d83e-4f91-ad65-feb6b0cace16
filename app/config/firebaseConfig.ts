import { Platform } from 'react-native';
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Notifications } from 'react-native-notifications';

/**
 * Firebase Configuration Module
 * 
 * This module handles Firebase initialization and ensures Firebase is properly
 * configured before any Firebase services are used.
 */

let firebaseInitialized = false;
let initializationPromise: Promise<boolean> | null = null;

/**
 * Initialize Firebase for the current platform
 * This function ensures Firebase is only initialized once
 */
export const initializeFirebase = async (): Promise<boolean> => {
  // Return existing promise if initialization is already in progress
  if (initializationPromise) {
    return initializationPromise;
  }

  // Return true if already initialized
  if (firebaseInitialized) {
    return true;
  }

  initializationPromise = new Promise(async (resolve) => {
    try {
      console.log('🔥 Initializing Firebase...');

      if (Platform.OS === 'ios') {
        // For iOS, Firebase should be initialized in the native AppDelegate
        try {
          // For now, assume we're always on simulator in development
          // In production, this should be false
          const isSimulator = __DEV__;

          if (isSimulator) {
            console.log('🔧 Running on iOS Simulator - Firebase messaging has limitations');
            console.log('📱 For iOS 16.4+ simulators, you can test push notifications using:');
            console.log('   xcrun simctl push <device> <bundle-id> <payload.json>');
            console.log('📱 Testing local notifications in simulator...');

            // Test local notification in simulator
            try {
              const notificationPayload: any = {
                identifier: Date.now().toString(),
                title: '🔥 Firebase Test Notification',
                body: 'Firebase is working! This is a test notification in simulator.',
                sound: 'default',
                badge: 1,
                payload: { test: true, timestamp: new Date().toISOString() },
                category: 'firebase_test',
                type: 'local',
                thread: 'firebase_test'
              };

              console.log('📱 Posting test local notification:', notificationPayload);
              Notifications.postLocalNotification(notificationPayload);
              console.log('✅ Test notification posted successfully');
              console.log('💡 Use the Firebase Test Button in the app to manually test notifications');
            } catch (notificationError) {
              console.error('❌ Error posting test notification:', notificationError);
            }

            console.log('✅ Firebase iOS initialization verified (simulator mode)');
            firebaseInitialized = true;
            resolve(true);
          } else {
            // Real device - register for remote messages first
            await messaging().registerDeviceForRemoteMessages();
            console.log('✅ Device registered for remote messages');

            // Now we can safely get the FCM token
            const token = await messaging().getToken();
            console.log('✅ Firebase iOS initialization verified, FCM token obtained');
            firebaseInitialized = true;
            resolve(true);
          }
        } catch (error) {
          console.error('❌ Firebase iOS initialization failed:', error);
          console.log('⚠️ Firebase may not be properly configured on iOS');
          // Don't fail completely, let the app continue
          firebaseInitialized = true;
          resolve(false);
        }
      } else {
        // For Android, Firebase should auto-initialize with google-services.json
        try {
          // Android doesn't need explicit device registration like iOS
          const token = await messaging().getToken();
          console.log('✅ Firebase Android initialization verified, FCM token obtained');
          firebaseInitialized = true;
          resolve(true);
        } catch (error) {
          console.error('❌ Firebase Android initialization failed:', error);
          firebaseInitialized = true;
          resolve(false);
        }
      }
    } catch (error) {
      console.error('❌ Firebase initialization error:', error);
      firebaseInitialized = true;
      resolve(false);
    }
  });

  return initializationPromise;
};

/**
 * Check if Firebase has been initialized
 */
export const isFirebaseInitialized = (): boolean => {
  return firebaseInitialized;
};

/**
 * Get Firebase messaging instance safely
 * This ensures Firebase is initialized before returning the messaging instance
 */
export const getMessagingInstance = async () => {
  await initializeFirebase();
  return messaging();
};

/**
 * Setup Firebase background message handler safely
 * This ensures Firebase is initialized before setting up the handler
 */
export const setupBackgroundMessageHandler = async () => {
  try {
    console.log('🔥 Setting up Firebase background message handler...');
    
    // Ensure Firebase is initialized first
    const initialized = await initializeFirebase();
    
    if (!initialized) {
      console.warn('⚠️ Firebase not properly initialized, skipping background handler setup');
      return false;
    }

    const messaging = await getMessagingInstance();
    
    messaging.setBackgroundMessageHandler(async (remoteMessage) => {
      console.log('📬 Background FCM Message received:', remoteMessage);

      // For Firebase campaigns and when app is truly in background/closed,
      // we need to show system notifications AND store data
      try {
        console.log('📩 Processing background notification...');

        // Store notification data first
        const existingNotifications = await AsyncStorage.getItem('notifications');
        let notifications = [];

        try {
          notifications = existingNotifications ? JSON.parse(existingNotifications) : [];
        } catch (parseError) {
          console.error('❌ Error parsing existing notifications, resetting:', parseError);
          notifications = [];
        }

        // Add the new notification
        const newNotification = {
          id: remoteMessage.messageId || `notification-${Date.now()}`,
          title: remoteMessage.notification?.title || 'New Notification',
          body: remoteMessage.notification?.body || '',
          data: remoteMessage.data || {},
          date: new Date().toISOString(),
          read: false,
        };

        notifications = [newNotification, ...notifications];
        await AsyncStorage.setItem('notifications', JSON.stringify(notifications));
        console.log('✅ Background notification data stored');

        // Show system notification for Firebase campaigns
        if (remoteMessage.notification) {
          const title = remoteMessage.notification.title || 'New Notification';
          const body = remoteMessage.notification.body || '';
          const notificationId = Date.now();

          console.log('🔔 Showing background system notification:', title, body);

          const notificationPayload: any = {
            identifier: notificationId.toString(),
            title: title,
            body: body,
            sound: 'default',
            badge: 1,
            payload: remoteMessage.data || {},
            category: 'sunrise_notifications',
            android: {
              channelId: 'high-priority',
              priority: 'high',
              autoCancel: true,
              color: '#FF4081',
              vibrate: true,
              lights: true,
              ongoing: false,
            }
          };

          console.log('📱 Posting background notification:', notificationPayload);
          Notifications.postLocalNotification(notificationPayload);
          console.log('✅ Background system notification posted successfully');
        }

      } catch (error) {
        console.error('❌ Error processing background notification:', error);
      }
    });

    console.log('✅ Firebase background message handler registered');
    return true;
  } catch (error) {
    console.error('❌ Error setting up background message handler:', error);
    return false;
  }
};
