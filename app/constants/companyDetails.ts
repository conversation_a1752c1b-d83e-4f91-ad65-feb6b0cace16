export const companyDetails = {
  headerTitle: "Company Registration",
  pageDescription:
    "To comply with the e-Invoice implementation by the Malaysia's IRBM/LHDN, we seek your help to input the mandatory info that was required under the e-Invoice implementation.",
  steps: [
    {
      title: "Basic Details",
      input: [
        {
          type: "TEXT",
          label: "Individual Name as per MyKad/passport [for non-malaysian]",
          isMandatory: true,
          valueField: "individualName",
          fieldName: "name",
        },
        {
          type: "TEXT",
          label: "Company Name as per SSM for malaysian",
          isMandatory: true,
          valueField: "companyName",
          fieldName: "companyName",
        },
        {
          type: "EMAIL",
          label: "Email Address(to receive validation of e-invoice from IRBM)",
          isMandatory: false,
          valueField: "emailAddress",
          fieldName: "email",
        },
        {
          type: "NUMBER",
          label: "Business Contact Number",
          isMandatory: true,
          valueField: "businessContactNumber",
          fieldName: "phone",
        },
        {
          type: "TEXT",
          label: "Tax Identification No.(TIN)",
          isMandatory: true,
          valueField: "taxIdentificationNo",
          fieldName: "tin",
        },
      ],
    },
    {
      title: "Business Details",
      input: [
        {
          type: "TEXT",
          label: "Business Registration No. as per SSM for malaysian ",
          isMandatory: true,
          valueField: "businessRegistrationNo",
          fieldLabel: "brn",
        },
        {
          type: "TEXT",
          label: "NRIC No.as per MyKad/passport[for non-malaysian]",
          isMandatory: false,
          valueField: "nricNo",
          fieldLabel: "nricNumber",
        },
        {
          type: "TEXT",
          label: "Old Business Registration No.(as per SSM)",
          isMandatory: false,
          valueField: "oldBusinessRegistrationNo",
          fieldLabel: "obrn",
        },
        {
          type: "NUMBER",
          label: "SST (Sales and Service Tax) Registration No.",
          isMandatory: true,
          valueField: "sstRegistrationNo",
          fieldLabel: "sst",
        },
        {
          type: "TEXT",
          label: "Country ",
          isMandatory: true,
          valueField: "country",
          fieldLabel: "country",
        },
        {
          type: "TEXT",
          label: "State ",
          isMandatory: true,
          valueField: "state",
          fieldLabel: "state",
        },
        {
          type: "TEXT",
          label: "City ",
          isMandatory: true,
          valueField: "city",
          fieldLabel: "city",
        },
        {
          type: "NUMBER",
          label: "Zipcode ",
          isMandatory: true,
          valueField: "zipcode",
          fieldLabel: "pincode",
        },
        {
          type: "TEXT",
          label: "Business Address ",
          isMandatory: true,
          valueField: "businessAddress",
          fieldLabel: "businessAddress",
        },
        {
          type: "TEXT",
          label: "Business Activity Description",
          isMandatory: true,
          valueField: "businessActivityDescription",
          fieldLabel: "businessDescription",
        },
        {
          type: "TEXT",
          label: "Malaysia standard industrial classification (MSIC) codes.",
          isMandatory: true,
          valueField: "msicCodes",
          fieldLabel: "msicCodes",
        },
      ],
    },
    {
      title: "Document Required",
      input: [
        {
          type: "Select Company Type",
          label: "Select Company Type",
          isMandatory: true,
          valueField: "individualName",
          isInputDropdown: true,
          options: [
            { label: "Limited Liability Company", value: "company" },
            { label: "Sole Proprietor", value: "Sole Proprietor" },
          ],
        },
      ],
    },
    {
      title: "Review & Submit",
      input: [
        {
          type: "TEXT",
          label: "Individual Name as per MyKad/passport [for non-malaysian]*",
          isMandatory: true,
          valueField: "businessRegistrationNo",
        },
        {
          type: "TEXT",
          label: "Company Name as per SSM for malaysian*",
          isMandatory: false,
          valueField: "nricNo",
        },
        {
          type: "TEXT",
          label: "Email Address(to receive validation of e-invoice from IRBM)",
          isMandatory: false,
          valueField: "oldBusinessRegistrationNo",
        },
        {
          type: "NUMBER",
          label: "Business Contact Number",
          isMandatory: true,
          valueField: "sstRegistrationNo",
        },
        {
          type: "TEXT",
          label: "Tax Identification No.(TIN) ",
          isMandatory: true,
          valueField: "country",
        },
        {
          type: "TEXT",
          label: "Business Registration No. as per SSM for malaysian* ",
          isMandatory: true,
          valueField: "state",
        },
      ],
    },
  ],
};
export const companyRegistraion = {
  english: {
    headerTitle: "Company Registration",
  pageDescription:
    "To comply with the e-Invoice implementation by the Malaysia's IRBM/LHDN, we seek your help to input the mandatory info that was required under the e-Invoice implementation.",
  steps: [
    {
      title: "Basic Details",
      input: [
        {
          type: "TEXT",
          label: "Individual Name as per MyKad/passport [for non-malaysian]",
          isMandatory: true,
          valueField: "individualName",
          fieldName: "name",
        },
        {
          type: "TEXT",
          label: "Company Name as per SSM for malaysian",
          isMandatory: true,
          valueField: "companyName",
          fieldName: "companyName",
        },
        {
          type: "EMAIL",
          label: "Email Address(to receive validation of e-invoice from IRBM)",
          isMandatory: false,
          valueField: "emailAddress",
          fieldName: "email",
        },
        {
          type: "NUMBER",
          label: "Business Contact Number",
          isMandatory: true,
          valueField: "businessContactNumber",
          fieldName: "phone",
        },
        {
          type: "TEXT",
          label: "Tax Identification No.(TIN)",
          isMandatory: true,
          valueField: "taxIdentificationNo",
          fieldName: "tin",
        },
      ],
    },
    {
      title: "Business Details",
      input: [
        {
          type: "TEXT",
          label: "Business Registration No. as per SSM for malaysian ",
          isMandatory: true,
          valueField: "businessRegistrationNo",
          fieldLabel: "brn",
        },
        {
          type: "TEXT",
          label: "NRIC No.as per MyKad/passport[for non-malaysian]",
          isMandatory: false,
          valueField: "nricNo",
          fieldLabel: "nricNumber",
        },
        {
          type: "TEXT",
          label: "Old Business Registration No.(as per SSM)",
          isMandatory: false,
          valueField: "oldBusinessRegistrationNo",
          fieldLabel: "obrn",
        },
        {
          type: "NUMBER",
          label: "SST (Sales and Service Tax) Registration No.",
          isMandatory: true,
          valueField: "sstRegistrationNo",
          fieldLabel: "sst",
        },
        {
          type: "TEXT",
          label: "Country ",
          isMandatory: true,
          valueField: "country",
          fieldLabel: "country",
        },
        {
          type: "TEXT",
          label: "State ",
          isMandatory: true,
          valueField: "state",
          fieldLabel: "state",
        },
        {
          type: "TEXT",
          label: "City ",
          isMandatory: true,
          valueField: "city",
          fieldLabel: "city",
        },
        {
          type: "NUMBER",
          label: "Zipcode ",
          isMandatory: true,
          valueField: "zipcode",
          fieldLabel: "pincode",
        },
        {
          type: "TEXT",
          label: "Business Address ",
          isMandatory: true,
          valueField: "businessAddress",
          fieldLabel: "businessAddress",
        },
        {
          type: "TEXT",
          label: "Business Activity Description",
          isMandatory: true,
          valueField: "businessActivityDescription",
          fieldLabel: "businessDescription",
        },
        {
          type: "TEXT",
          label: "Malaysia standard industrial classification (MSIC) codes.",
          isMandatory: true,
          valueField: "msicCodes",
          fieldLabel: "msicCodes",
        },
      ],
    },
    {
      title: "Document Required",
      input: [
        {
          type: "Select Company Type",
          label: "Select Company Type",
          isMandatory: true,
          valueField: "individualName",
          isInputDropdown: true,
          options: [
            { label: "Limited Liability Company", value: "company" },
            { label: "Sole Proprietor", value: "Sole Proprietor" },
          ],
        },
      ],
    },
    {
      title: "Review & Submit",
      input: [
        {
          type: "TEXT",
          label: "Individual Name as per MyKad/passport [for non-malaysian]*",
          isMandatory: true,
          valueField: "businessRegistrationNo",
        },
        {
          type: "TEXT",
          label: "Company Name as per SSM for malaysian*",
          isMandatory: false,
          valueField: "nricNo",
        },
        {
          type: "TEXT",
          label: "Email Address(to receive validation of e-invoice from IRBM)",
          isMandatory: false,
          valueField: "oldBusinessRegistrationNo",
        },
        {
          type: "NUMBER",
          label: "Business Contact Number",
          isMandatory: true,
          valueField: "sstRegistrationNo",
        },
        {
          type: "TEXT",
          label: "Tax Identification No.(TIN) ",
          isMandatory: true,
          valueField: "country",
        },
        {
          type: "TEXT",
          label: "Business Registration No. as per SSM for malaysian* ",
          isMandatory: true,
          valueField: "state",
        },
      ],
    },
  ],
  },
  malay: {
    headerTitle: "Pendaftaran Syarikat",
    pageDescription:
      "Untuk mematuhi pelaksanaan e-Invois oleh LHDN Malaysia, kami memerlukan kerjasama anda untuk mengisi maklumat wajib yang diperlukan di bawah pelaksanaan e-Invois.",
    steps: [
      {
        title: "Maklumat Asas",
        input: [
          {
            type: "TEXT",
            label: "Nama Individu seperti dalam MyKad/pasport [untuk bukan warganegara]",
            isMandatory: true,
            valueField: "individualName",
            fieldName: "name",
          },
          {
            type: "TEXT",
            label: "Nama Syarikat seperti dalam SSM untuk warganegara Malaysia",
            isMandatory: true,
            valueField: "companyName",
            fieldName: "companyName",
          },
          {
            type: "EMAIL",
            label: "Alamat E-mel (untuk menerima pengesahan e-Invois daripada LHDN)",
            isMandatory: false,
            valueField: "emailAddress",
            fieldName: "email",
          },
          {
            type: "NUMBER",
            label: "Nombor Telefon Perniagaan",
            isMandatory: true,
            valueField: "businessContactNumber",
            fieldName: "phone",
          },
          {
            type: "TEXT",
            label: "Nombor Pengenalan Cukai (TIN)",
            isMandatory: true,
            valueField: "taxIdentificationNo",
            fieldName: "tin",
          },
        ],
      },
      {
        title: "Maklumat Perniagaan",
        input: [
          {
            type: "TEXT",
            label: "Nombor Pendaftaran Perniagaan seperti dalam SSM untuk warganegara Malaysia",
            isMandatory: true,
            valueField: "businessRegistrationNo",
            fieldLabel: "brn",
          },
          {
            type: "TEXT",
            label: "Nombor NRIC seperti dalam MyKad/pasport [untuk bukan warganegara]",
            isMandatory: false,
            valueField: "nricNo",
            fieldLabel: "nricNumber",
          },
          {
            type: "TEXT",
            label: "Nombor Pendaftaran Perniagaan Lama (seperti dalam SSM)",
            isMandatory: false,
            valueField: "oldBusinessRegistrationNo",
            fieldLabel: "obrn",
          },
          {
            type: "NUMBER",
            label: "Nombor Pendaftaran SST (Cukai Jualan dan Perkhidmatan)",
            isMandatory: true,
            valueField: "sstRegistrationNo",
            fieldLabel: "sst",
          },
          {
            type: "TEXT",
            label: "Negara",
            isMandatory: true,
            valueField: "country",
            fieldLabel: "country",
          },
          {
            type: "TEXT",
            label: "Negeri",
            isMandatory: true,
            valueField: "state",
            fieldLabel: "state",
          },
          {
            type: "TEXT",
            label: "Bandar",
            isMandatory: true,
            valueField: "city",
            fieldLabel: "city",
          },
          {
            type: "NUMBER",
            label: "Poskod",
            isMandatory: true,
            valueField: "zipcode",
            fieldLabel: "pincode",
          },
          {
            type: "TEXT",
            label: "Alamat Perniagaan",
            isMandatory: true,
            valueField: "businessAddress",
            fieldLabel: "businessAddress",
          },
          {
            type: "TEXT",
            label: "Penerangan Aktiviti Perniagaan",
            isMandatory: true,
            valueField: "businessActivityDescription",
            fieldLabel: "businessDescription",
          },
          {
            type: "TEXT",
            label: "Kod pengelasan industri standard Malaysia (MSIC)",
            isMandatory: true,
            valueField: "msicCodes",
            fieldLabel: "msicCodes",
          },
        ],
      },
      {
        title: "Dokumen Diperlukan",
        input: [
          {
            type: "Select Company Type",
            label: "Pilih Jenis Syarikat",
            isMandatory: true,
            valueField: "individualName",
            isInputDropdown: true,
            options: [
              { label: "Syarikat Liabiliti Terhad", value: "company" },
              { label: "Perniagaan Milikan Tunggal", value: "Sole Proprietor" },
            ],
          },
        ],
      },
      {
        title: "Semak & Hantar",
        input: [
          {
            type: "TEXT",
            label: "Nama Individu seperti dalam MyKad/pasport [untuk bukan warganegara]*",
            isMandatory: true,
            valueField: "businessRegistrationNo",
          },
          {
            type: "TEXT",
            label: "Nama Syarikat seperti dalam SSM untuk warganegara Malaysia*",
            isMandatory: false,
            valueField: "nricNo",
          },
          {
            type: "TEXT",
            label: "Alamat E-mel (untuk menerima pengesahan e-Invois daripada LHDN)",
            isMandatory: false,
            valueField: "oldBusinessRegistrationNo",
          },
          {
            type: "NUMBER",
            label: "Nombor Telefon Perniagaan",
            isMandatory: true,
            valueField: "sstRegistrationNo",
          },
          {
            type: "TEXT",
            label: "Nombor Pengenalan Cukai (TIN)",
            isMandatory: true,
            valueField: "country",
          },
          {
            type: "TEXT",
            label: "Nombor Pendaftaran Perniagaan seperti dalam SSM untuk warganegara Malaysia*",
            isMandatory: true,
            valueField: "state",
          },
        ],
      },
    ],
  }
}