
export const staticTranslations = {
  english: {
    items: "items",
    view_all: "View All",
    product_not_found: "No Product found",
    home: "Home",
    category: "Category",
    search: "Search",
    my_cart: "My Cart",
    profile: "Profile",
    trending_searches: "Trending searches",
    popular_searches: "Popular Searches",
    no_recent_searches_found: "No recent searches found",
    no_result_found: "No results found",
    recent_searches: "RECENT SEARCHES",
    no_banner_found: "No Banner found",
    no_collections_found: "No collections found",
    items_selected: "Items Selected",
    order_details: "Order Details",
    subtotal: "Subtotal",
    incl_of_all_taxes: "(Incl. of all taxes)",
    place_order: "Place Order",
    categories: "Categories",
    updating_cart: "Updating Cart",
    filter_results: "Filter Results",
    apply: "Apply",
    clear_all: "CLEAR ALL",
    filter: "Filter",
    sort_by: "Sort By",
    best_selling: "BEST SELLING",
    newest_arrivals: "NEWEST ARRIVALS",
    oldest_arrivals: "OLDEST ARRIVALS",
    price_low_to_high: "PRICE: LOW TO HIGH",
    price_high_to_low: "PRICE: HIGH TO LOW",
    title_a_to_z: "TITLE A → Z",
    title_z_to_a: "TITLE Z → A",
    most_relevant: "MOST RELEVANT",
    add_to_bag: "Add to bag",
    proudct_details: "Proudct Details",
    explore_all: "EXPLORE ALL",
    shop_now: "Shop Now",
    select_oneproduct_errMsg: "Please select at least one product",
    select_size_errMsg: "Please select a size",
    select_validinput_errMsg: "Please Select valid input value",
    error: "Error",
    empty_input: "Input have no value",
    Added_tocart_successfully: "Added to cart successfully!",
    applicationsubTitle: "Application Submitted",
    applciation_submitted: "To comply with the e-Invoice implementation by the Malaysia's IRBM/LHDN, we seek your help to input the mandatory info that was required under the e-Invoice implementation.",
    approved: "Your application has been approved, and your account is ready to use",
    notApproved: "Your application has not been approved, and is under review",
    applicationStatusTitle: {
      approved: "Application Approved!",
      pending: "Application Pending",
    },
    applicationStatusDesc: {
      approved: "Your application has been approved, and your account is ready to use.",
      pending: "Your application has not been approved, and is under review.",
    },
    contactSupport: "If you have any queries or need to make changes to your application, contact us at:",
    go_home: "	Go to Home",
    view_application: "View Application",
    choose_Color: "Choose Color",
    choose_size: "Choose Size",
    size_guide: " Size Guide",
    inclAllTaxes: "Inclusive all taxes",
    submit: "Submit",
    view_status:"View Status",
    pre_order:"Pre Order"
  },
  malay: {
    view_all: "Lihat Semua",
    product_not_found: "Produk Tidak Dijumpai",
    home: "Laman Utama",
    category: "Kategori",
    search: "Cari",
    my_cart: "Troli Saya",
    profile: "Profil",
    trending_searches: "Carian Popular",
    popular_searches: "Carian Laris",
    no_recent_searches_found: "Tiada carian terkini ditemui",
    no_result_found: "Tiada hasil ditemui",
    recent_searches: "CARIAN BARU-BARU INI",
    no_banner_found: "Tiada Banner ditemui",
    no_collections_found: "Tiada koleksi ditemui",
    items_selected: "Item Dipilih",
    order_details: "Butiran Pesanan",
    subtotal: "Jumlah Keseluruhan",
    incl_of_all_taxes: "(Termasuk semua cukai)",
    place_order: "Buat Pesanan",
    categories: "Kategori",
    updating_cart: "Mengemaskini Troli",
    filter_results: "Tapis Keputusan",
    apply: "Terapkan",
    clear_all: "Kosongkan",
    filter: "Tapis",
    sort_by: "Susun mengikut",
    best_selling: "PALING LARIS",
    newest_arrivals: "KETIBAAN TERBARU",
    oldest_arrivals: "KETIBAAN TERLAMA",
    price_low_to_high: "HARGA: RENDAH KE TINGGI",
    price_high_to_low: "HARGA: TINGGI KE RENDAH",
    title_a_to_z: "TAJUK A → Z",
    title_z_to_a: "TAJUK Z → A",
    most_relevant: "PALING RELEVAN",
    add_to_bag: "Tambah ke beg",
    proudct_details: "Butiran Produk",
    explore_all: "TEROKAI SEMUA",
    shop_now: "Beli Sekarang",
    select_oneproduct_errMsg: "Sila pilih sekurang-kurangnya satu produk",
    select_size_errMsg: "Sila pilih saiz",
    select_validinput_errMsg: "Sila pilih nilai input yang sah",
    error: "Ralat",
    empty_input: "Input tiada nilai",
    Added_tocart_successfully: "Berjaya ditambah ke troli!",
    applicationsubTitle: "Permohonan Dihantar",
    applciation_submitted: "Untuk mematuhi pelaksanaan e-Invois oleh LHDN Malaysia, kami memohon kerjasama anda untuk mengisi maklumat wajib yang diperlukan di bawah pelaksanaan e-Invois ini.",
    approved: "Permohonan anda telah diluluskan, dan akaun anda sudah sedia untuk digunakan",
    notApproved: "Permohonan anda belum diluluskan, dan sedang dalam semakan",
    applicationStatusTitle: {
      approved: "Permohonan Diluluskan!",
      pending: "Permohonan Sedang Diproses",
    },
    applicationStatusDesc: {
      approved: "Permohonan anda telah diluluskan, dan akaun anda sudah sedia untuk digunakan.",
      pending: "Permohonan anda belum diluluskan, dan sedang dalam semakan.",
    },
    contactSupport: "Jika anda mempunyai sebarang pertanyaan atau perlu membuat perubahan kepada permohonan anda, hubungi kami di:",
    go_home: "Pergi ke Laman Utama",
    view_application: "Lihat Permohonan",
    choose_Color: "Pilih Warna",
    choose_size: "Pilih Saiz",
    size_guide: "Panduan Saiz",
    inclAllTaxes: "Termasuk semua cukai",
    submit: "Hantar",
    view_status:"lihat status",
    pre_order:"Pra Tempah"
  }
};