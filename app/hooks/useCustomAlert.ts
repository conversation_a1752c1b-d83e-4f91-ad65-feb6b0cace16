import { useState } from 'react';

interface AlertButton {
  text: string;
  onPress?: () => void;
  style?: 'default' | 'cancel' | 'destructive';
}

interface AlertConfig {
  title: string;
  message: string;
  type?: 'success' | 'error' | 'info' | 'warning';
  buttons?: AlertButton[];
}

export const useCustomAlert = () => {
  const [alertConfig, setAlertConfig] = useState<AlertConfig | null>(null);
  const [visible, setVisible] = useState(false);

  const showAlert = (config: AlertConfig) => {
    setAlertConfig(config);
    setVisible(true);
  };

  const hideAlert = () => {
    setVisible(false);
    setTimeout(() => {
      setAlertConfig(null);
    }, 300); // Wait for animation to complete
  };

  // Convenience methods for different alert types
  const showSuccess = (title: string, message: string, buttons?: AlertButton[]) => {
    showAlert({
      title,
      message,
      type: 'success',
      buttons: buttons || [{ text: 'OK' }],
    });
  };

  const showError = (title: string, message: string, buttons?: AlertButton[]) => {
    showAlert({
      title,
      message,
      type: 'error',
      buttons: buttons || [{ text: 'OK' }],
    });
  };

  const showInfo = (title: string, message: string, buttons?: AlertButton[]) => {
    showAlert({
      title,
      message,
      type: 'info',
      buttons: buttons || [{ text: 'OK' }],
    });
  };

  const showWarning = (title: string, message: string, buttons?: AlertButton[]) => {
    showAlert({
      title,
      message,
      type: 'warning',
      buttons: buttons || [{ text: 'OK' }],
    });
  };

  return {
    alertConfig,
    visible,
    showAlert,
    hideAlert,
    showSuccess,
    showError,
    showInfo,
    showWarning,
  };
};
