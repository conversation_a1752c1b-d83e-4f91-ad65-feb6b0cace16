# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# =============================================================================
# SECURITY - SENSITIVE FILES
# =============================================================================
# Environment files with sensitive data
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# Firebase service account keys
firebase-service-account*.json
*-firebase-adminsdk-*.json
service-account*.json

# Google Services configuration files (generated from env vars)
google-services.json
GoogleService-Info.plist

# Android keystore files
*.keystore
*.jks
release.keystore
debug.keystore

# iOS certificates and provisioning profiles
*.p12
*.p8
*.mobileprovision
*.cer
*.certSigningRequest

# API keys and secrets
secrets.json
config/secrets.json
api-keys.json

# Backend configuration
backend-example/firebase-service-account*.json
backend-example/config/
backend-example/.env*
# typescript
*.tsbuildinfo

app-example
# Firebase service account keys
backend-example/*-firebase-adminsdk-*.json
**/google-services.json
**/GoogleService-Info.plist